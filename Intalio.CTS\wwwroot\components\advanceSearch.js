import Intalio from './common.js'
import DocumentDetails from './documentDetails.js'
import { DelegationUsers, Categories } from './lookup.js'
import ReportCorrespondenceDetailExport from './reportCorrespondenceDetailExport.js'
class DocumentAdvanceSearchModel extends Intalio.Model
{
    constructor()
    {
        super();
        this.drawContentBuilder = false;
        this.configuration = null;
        this.delegationId = null;
        this.showOCRContentField = window.EnableOCR && (CrawlerServerUrl && CrawlerServerUrl.split(Splitter).length > 0);
    }
}
var table = null;
var firstTime = true;
var gLocked = false;
var gTableName = "grdSearchItems";
var categoryFormBuilder = null;
var advanceSearchFormBuilder = null;
var categoryFormDesignerComponents = null;
var advanceSearchFormDesignerComponents = null;
var gFromLink = false;
var formIsValid = false;
var categoryFormIsValid = true;
function enableSearch()
{
    $('#btnSearch').attr("disabled", !(formIsValid && categoryFormIsValid));
    $('#btnClear').attr("disabled", !(formIsValid && categoryFormIsValid));
}
function delegationUsersForSelect2(data)
{
    var retVal = [];
    if (data && data.length > 0)
    {
        retVal.push({
            id: 0,
            text: Resources.MyInbox
        });
        for (var i = 0; i < data.length; i++)
        {
            retVal.push({
                id: data[i].id,
                text: data[i].fromUser
            });
        }
    }
    return retVal;
}
function buildAdvanceSeachForm(self, configuration)
{
    var formDesigner = (configuration.content || "") !== "" ? JSON.parse(configuration.content) : null;
    var formDesignerTranslation = (configuration.contentTranslation || "") !== "" ? JSON.parse(configuration.contentTranslation) : null;
    if (formDesigner !== null && formDesigner.components.length > 0)
    {
        advanceSearchFormDesignerComponents = formDesigner;
        var translationObject = formDesignerTranslation;
        var translationEn = {}, translationAr = {}, translationFr = {};
        if (translationObject !== null)
        {
            for (var i = 0; i < translationObject.length; i++)
            {
                translationEn[translationObject[i].Keyword] = translationObject[i].En;
                translationAr[translationObject[i].Keyword] = translationObject[i].Ar;
                translationFr[translationObject[i].Keyword] = translationObject[i].Fr;
            }
        }
        Formio.createForm(document.getElementById('contentBuilder'), formDesigner, {
            noAlerts: true, template: 'bootstrap3', readOnly: false, language: window.language,
            i18n: {
                'en': translationEn,
                'ar': translationAr,
                'fr': translationFr
            }
        }).then(function (form)
        {
            advanceSearchFormBuilder = form;
            form.nosubmit = true;
            form.on('change', function (value)
            {
                formIsValid = form.checkValidity();
                enableSearch();
            });
            formIsValid = form.checkValidity();
            enableSearch();
        });
        if (self.model.configuration.includeCategory)
        {
            $("#divDefault").addClass('with-border').removeClass('without-border');
        }
    } else
    {
        $("#contentBuilder").empty();
        advanceSearchFormBuilder = null;
        formIsValid = true;
        enableSearch();
    }
}
function loadFormByCategoryId(self, id)
{
    Common.ajaxGet('/Category/GetSearchAttribute', { id: id }, function (data)
    {
        var formDesigner = (data.attribute || "") !== "" ? JSON.parse(data.attribute) : null;
        var formDesignerTranslation = (data.attributeTranslation || "") !== "" ? JSON.parse(data.attributeTranslation) : null;
        if (formDesigner !== null)
        {
            categoryFormDesignerComponents = formDesigner;
            var translationObject = formDesignerTranslation;
            var translationEn = {}, translationAr = {}, translationFr = {};
            if (translationObject !== null)
            {
                for (var i = 0; i < translationObject.length; i++)
                {
                    translationEn[translationObject[i].Keyword] = translationObject[i].En;
                    translationAr[translationObject[i].Keyword] = translationObject[i].Ar;
                    translationFr[translationObject[i].Keyword] = translationObject[i].Fr;
                }
            }
            Formio.createForm(document.getElementById('categoryBuilder'), formDesigner, {
                noAlerts: true, template: 'bootstrap3', readOnly: false, language: window.language,
                i18n: {
                    'en': translationEn,
                    'ar': translationAr,
                    'fr': translationFr
                }
            }).then(function (form)
            {
                categoryFormBuilder = form;
                form.nosubmit = true;
                form.on('change', function (value)
                {
                    categoryFormIsValid = form.checkValidity();
                    enableSearch();
                });
                categoryFormIsValid = form.checkValidity();
                enableSearch();
            });
            if (self.model.drawContentBuilder)
            {
                $("#divContentBuilder").addClass('with-border').removeClass('without-border');
            } else
            {
                $("#divDefault").addClass('with-border').removeClass('without-border');
            }
            $('#divCategoryBuilderBody').removeClass('hidden');
        } else
        {
            $("#categoryBuilder").empty();
            categoryFormBuilder = null;
            categoryFormIsValid = true;
            if (self.model.drawContentBuilder)
            {
                $("#divContentBuilder").addClass('without-border').removeClass('with-border');
            } else
            {
                $("#divDefault").addClass('without-border').removeClass('with-border');
            }
            $('#divCategoryBuilderBody').addClass('hidden');
            enableSearch();
        }
    }, null, true);
}
function isObject(obj)
{
    return obj !== undefined && obj !== null && obj.constructor === Object;
}
function getInputs(data, inputs, customKey)
{
    $.each(data, function (key, value)
    {
        var input = {};
        if (customKey)
        {
            input.text = customKey + "." + key;
        } else
        {
            input.text = key;
        }
        if (isObject(value))
        {
            getInputs(value, inputs, input.text);
        } else
        {
            if (Array.isArray(value))
            {
                for (var i = 0; i < value.length; i++)
                {
                    if (value[i])
                    {
                        var tempInput = {};
                        tempInput.text = input.text;
                        tempInput.value = value[i];
                        inputs.push(tempInput);
                    }
                }
            } else
            {
                input.value = value;
                inputs.push(input);
            }
        }
    });
}
function search(self, categories, priorities, privacies, importances, statuses)
{
    if (firstTime)
    {
        Common.gridCommon();
        var securityMatrix = JSON.parse(new CoreComponents.Lookup.SecurityMatrix().get(window.language));// JSON.parse(sessionStorage.getItem("SecurityMatrix"));
        var buttons = SecurityMatrix.getToolbarActions(securityMatrix, TreeNodes.Search, gTableName);
        var showCheckBoxes = self.model.fromLink || buttons.length > 0;
        table = $("#grdItems").on('draw.dt',
            function ()
            {
                $('#grdItems tbody tr td').each(function ()
                {
                    this.setAttribute('title', $(this).text());
                });
            })
            .DataTable({
                processing: true,
                ordering: true,
                serverSide: true,
                pageLength: 10,
                "ajax": {
                    "url": "/AdvanceSearch/List",
                    "type": "POST",
                    "datatype": "json",
                    "data": function (d)
                    {
                        d.categoryId = $("#cmbCategory").val();
                        d.delegationId = $("#cmbDelegation").val();
                        if (advanceSearchFormBuilder)
                        {
                            d.formData = JSON.stringify(advanceSearchFormBuilder._data);
                        }
                        if (categoryFormBuilder)
                        {
                            d.categoryFormData = JSON.stringify(categoryFormBuilder._data);
                        }
                        if (self.model.delegationId)
                        {
                            d.delegationId = self.model.delegationId;
                        }
                        d.__RequestVerificationToken = $('input[name="__RequestVerificationToken"]').val();
                        return d;
                    },
                    "dataSrc": function (response)
                    {
                        $('#grdItems_processing').css('display', 'none');
                        if (response.recordsTotal > 0)
                        {
                            return response.data;
                        } else
                        {
                            if (response.message != undefined && response.message != "")
                            {
                                Common.showScreenErrorMsg(response.message);
                            }
                            response.data = [] //since datatables will be checking for the object as array
                            return response.data;
                        }
                    }
                },
                "order": [],
                "columns": [
                    {
                        visible: showCheckBoxes,
                        title: '<input id="chkAll" type="checkbox" />',
                        width: '16px',
                        "orderable": false,
                        "render": function (data, type, row)
                        {
                            return "<input type='checkbox' onclick='event.stopPropagation();' data-id=" + row.id + " />";
                        }
                    },
                    {
                        "className": 'details-control',
                        "orderable": false,
                        "data": null,
                        "defaultContent": '',
                        width: '16px'
                    },
                    { title: "Id", data: "id", visible: false, "orderable": false },
                    {
                        title: Resources.Category, data: "categoryId", "orderable": true, orderSequence: ["asc", "desc"],
                        render: function (data, type, full, meta)
                        {
                            for (var i = 0; i < categories.length; i++)
                            {
                                if (categories[i].id === data)
                                {
                                    return categories[i].text;
                                }
                            }
                            return "";
                        }
                    },
                    { title: Resources.ReferenceNumber, data: "referenceNumber", render: $.fn.dataTable.render.text(), "orderable": true, orderSequence: ["asc", "desc"] },
                    { title: Resources.Subject, data: "subject", render: $.fn.dataTable.render.text(), "orderable": true, orderSequence: ["asc", "desc"] },
                    {
                        title: Resources.Status, data: "statusId", "orderable": false, width: "50px",
                        "render": function (data)
                        {
                            for (var i = 0; i < statuses.length; i++)
                            {
                                if (statuses[i].id === data)
                                {
                                    return "<div class='label' style='background-color:" + (statuses[i].color !== null ? statuses[i].color : "#27c24c") + "'>" + statuses[i].text + "</div>";
                                }
                            }
                            return "";
                        }
                    },
                    {
                        "className": "text-right",
                        "autoWidth": false,
                        "bAutoWidth": false,
                        width: "16px",
                        'orderable': false,
                        'sortable': false,
                        'render': function (data, type, full, meta)
                        {
                            var delegationId = null;
                            if (self.model.delegationUsers && self.model.delegationUsers.length > 0)
                            {
                                delegationId = $("#cmbDelegation").val() !== null && typeof $("#cmbDelegation").val() !== "undefined" ? $("#cmbDelegation").val() : "0";
                            }
                            let btnView = document.createElement("button");
                            btnView.setAttribute("class", "btn btn-xs btn-warning mr-sm view");
                            btnView.setAttribute("title", Resources.View);
                            btnView.setAttribute("clickattr", "openSearchDocument(" + full.id + "," + delegationId + ")");
                            btnView.innerHTML = "<i class='fa fa-eye fa-white'/>";
                            return btnView.outerHTML;
                        }
                    }
                ],
                "fnInitComplete": function (settings, json)
                {
                    gLocked = false;
                    $('[data-toggle="tooltip"]').tooltip();
                },
                dom: 'trpi',
                buttons: []
            });
        $('#grdItems tbody').on('click', 'tr', function ()
        {
            var input = this.getElementsByTagName('input')[0];
            if (typeof input !== "undefined")
                input.checked = input.checked ? false : true;
        });
        $('#grdItems tbody').on('click', ".view", function ()
        {
            var onclick = $(this).attr("clickattr");
            eval(onclick);
        });
        $('#grdItems tbody').on('dblclick', 'tr', function ()
        {
            var onclick = $(this).find(".view").attr("clickattr");
            eval(onclick);
        });
        $('#grdItems tbody').on('click', 'td.details-control', function ()
        {
            let tr = $(this).closest('tr');
            let row = table.row(tr);
            if (row.child.isShown())
            {
                row.child.hide();
                tr.removeClass('shown');
            }
            else
            {
                row.child(format(row.data(), importances, priorities, privacies, statuses)).show();
                tr.addClass('shown');
            }
        });
        table.$('td').tooltip({
            "delay": 0,
            "track": true,
            "fade": 250
        });
        $('#chkAll').change(function ()
        {
            $('tbody tr td input[type="checkbox"]').prop('checked', $(this).prop('checked'));
        });
    } else
    {
        table.ajax.reload();
        gLocked = false;
    }
    firstTime = false;
}
function format(row, importances, priorities, privacies, statuses)
{
    var importance = "", priority = "", privacy = "";
    for (let i = 0; i < priorities.length; i++)
    {
        if (priorities[i].id === row.priorityId)
        {
            priority = priorities[i].text;
        }
    }
    for (let i = 0; i < privacies.length; i++)
    {
        if (privacies[i].id === row.privacyId)
        {
            privacy = privacies[i].text;
        }
    }
    for (let i = 0; i < importances.length; i++)
    {
        if (importances[i].id === row.importanceId)
        {
            importance = importances[i].text;
        }
    }
    return '<table style="width:100%" cellspacing="0" border="0">' +
        '<tr>' +
        '<th style="width: 10%;padding:5px">' + Resources.SendingEntity + ':</th>' +
        '<td style="width: 90%;padding:5px;word-break: break-all;"">' + (row.sendingEntity || '') + '</td>' +
        '</tr>' +
        '<tr>' +
        '<th style="width: 10%;padding:5px">' + Resources.ReceivingEntity + ':</th>' +
        '<td style="width: 90%;padding:5px;word-break: break-all;"">' + (row.receivingEntity || '') + '</td>' +
        '</tr>' +
        '<tr>' +
        '<th style="width: 10%;padding:5px">' + Resources.Priority + ':</th>' +
        '<td style="width: 90%;padding:5px;word-break: break-all;"">' + priority + '</td>' +
        '</tr>' +
        '<tr>' +
        '<th style="width: 10%;padding:5px">' + Resources.Privacy + ':</th>' +
        '<td style="width: 90%;padding:5px;word-break: break-all;"">' + privacy + '</td>' +
        '</tr>' +
        '<tr>' +
        '<th style="width: 10%;padding:5px">' + Resources.Importance + ':</th>' +
        '<td style="width: 90%;padding:5px;word-break: break-all;"">' + importance + '</td>' +
        '</tr>' +
        '<tr>' +
        '<th style="width: 10%;padding:5px">' + Resources.CreatedDate + ':</th>' +
        '<td style="width: 90%;padding:5px;word-break: break-all;"">' + (DateConverter.toHijriFormated(row.createdDate, null, window.CalendarType) || '') + '</td>' +
        '</tr>' +
        '<tr>' +
        '<th style="width: 10%;padding:5px">' + Resources.CreatedBy + ':</th>' +
        '<td style="width: 90%;padding:5px;word-break: break-all;"">' + (row.createdByUser || '') + '</td>' +
        '</tr>' +
        '<tr>' +
        '<th style="width: 10%;padding:5px">' + Resources.DueDate + ':</th>' +
        '<td style="width: 90%;padding:5px;word-break: break-all;"">' + (DateConverter.toHijriFormated(row.dueDate, ConversionDateFormat.DateTimeFormat24_Slash, window.CalendarType) || '') + '</td>' +
        '</tr>' +

        '</table>';
}
function openSearchDocument(id, delegationId)
{
    var params = { id: id };
    if (delegationId !== null)
    {
        params.delegationId = delegationId;
    }
    Common.ajaxGet('/Document/GetSearchDocument', params, function (response)
    {
        if (response && response === "NoAccess")
        {
            Common.alertMsg(Resources.NoPermission);
        } else
        {
            $(".delegation").removeClass("active");
            $("#searchContainerDiv").hide();

            var model = new DocumentDetails.DocumentDetails();
            model.documentModel = response;
            model.delegationId = delegationId;
            model.documentId = response.id;
            model.referenceNumber = response.referenceNumber;
            model.categoryName = response.categoryName;
            model.statusId = response.status;
            model.readonly = true;
            model.statuses = new CoreComponents.Lookup.Statuses().get(window.language);
            model.showMyTransfer = false;
            model.showMyTask = false;
            model.fromSearch = true;
            model.attachmentId = response.attachmentId;
            model.attachmentVersion = response.attachmentVersion;

            var securityMatrix = JSON.parse(new CoreComponents.Lookup.SecurityMatrix().get(window.language));// JSON.parse(sessionStorage.getItem("SecurityMatrix"));
            var tabs = securityMatrix.SecurityNodes[Number(TreeNodes.Search)].SecurityCategories[response.categoryId].Tabs;
            model.tabs = $.grep(tabs, function (element, index)
            {
                return !element.Name.includes("myTransfer") && !element.Name.includes("documentMetadata") &&
                    !element.Name.includes("transferHistory") && !element.Name.includes("visualTracking") &&
                    !element.Name.includes("activityLog") && !element.Name.includes("attachments") && !element.Name.includes("notes") &&
                    !element.Name.includes("linkedDocument") && !element.Name.includes("nonArchivedAttachments");
            });
            model.tabsWithStatic = tabs;
            model.tabsActions = securityMatrix.SecurityNodes[Number(TreeNodes.Search)].SecurityCategories[response.categoryId].SecurityTabs;
            var wrapper = $(".content-wrapper");

            var view = new DocumentDetails.DocumentDetailsView(wrapper, model);
            view.render();
            if (!gFromLink)
            {
                $(document).off('click', '.btn-back');
                $(document).on('click', '.btn-back', function ()
                {
                    $("#searchContainerDiv").show();
                    view.remove();
                    $(".toRemove").remove();
                });

                $(document).off('click', '.btn-export');
                $(document).on('click', '.btn-export', function ()
                {
                    var wrapper = $(".modal-window");
                    var model = new ReportCorrespondenceDetailExport.ReportCorrespondenceDetailExport();
                    model.documentId = response.id;
                    model.delegationId = delegationId;
                    var reportCorrespondenceDetailExportView = new ReportCorrespondenceDetailExport.ReportCorrespondenceDetailExportView(wrapper, model);
                    reportCorrespondenceDetailExportView.render();

                    $("#modalReportCorrespondenceDetailExport").off("hidden.bs.modal");
                    $("#modalReportCorrespondenceDetailExport").off("shown.bs.modal");
                    $("#modalReportCorrespondenceDetailExport").on('shown.bs.modal', function ()
                    { });
                    $("#modalReportCorrespondenceDetailExport").on('hidden.bs.modal', function ()
                    { });
                    $("#modalReportCorrespondenceDetailExport").modal("show");
                });
            }
        }
    }, function () { Common.showScreenErrorMsg(); }, true);
}
class DocumentAdvanceSearchView extends Intalio.View
{
    constructor(element, model)
    {
        super(element, "advancesearch", model);
    }
    render()
    {
        var self = this;
        gFromLink = self.model.fromLink;
        var categories = new Categories().get(window.language);
        var priorities = new CoreComponents.Lookup.Priorities().get(window.language);
        var privacies = new CoreComponents.Lookup.Privacies().get(window.language);
        var importances = new CoreComponents.Lookup.Importances().get(window.language);
        var statuses = new CoreComponents.Lookup.Statuses().get(window.language);
        if (statuses.length > 0)
        {
            statuses = statuses.filter(function (obj)
            {
                return obj.id !== SystemStatus.Draft;
            });
        }
        $.fn.select2.defaults.set("theme", "bootstrap");
        firstTime = true;
        $('#divContentBuilder,#divCategoryBuilderBody').keydown(function (e)
        {
            var code = e.keyCode || e.which;
            if (code === 13 && formIsValid && categoryFormIsValid)
            {
                e.preventDefault();
                $('#btnSearch').trigger("click");
            }
        });
        $("#btnSearch").on('click', function ()
        {
            if (!gLocked)
            {
                gLocked = true;
                try
                {
                    search(self, categories, priorities, privacies, importances, statuses);
                    $('.search').hide();
                    $('.btn-scroll').fadeIn();
                    $('.gridResult').fadeIn();
                    $("#expandIcon").show();
                    $("#expandIcon").removeClass("fa-compress").addClass("fa-expand");
                    $("#resultExpandIcon").removeClass("fa-compress").addClass("fa-expand");
                } catch (e)
                {
                    gLocked = false;
                }
            }
        });
        $("#btnClear").on('click', function ()
        {
            $("#expandIcon").hide();
            $("#cmbCategory").val('').trigger('change');
            $('#cmbCategory').trigger({
                type: 'select2:unselect'
            });
            if (self.model.delegationUsers && self.model.delegationUsers.length > 0)
            {
                $("#cmbDelegation").val('0').trigger('change');
            }
            if (self.model.drawContentBuilder)
            {
                buildAdvanceSeachForm(self, self.model.configuration);
            }
        });
        if (self.model.drawContentBuilder || self.model.configuration.includeCategory || self.model.configuration.enableDelegation)
        {
            buildAdvanceSeachForm(self, self.model.configuration);
        } else
        {
            $('#btnClear').attr("disabled", true);
            $('#btnClear').button('reset');
            $('#btnSearch').attr("disabled", true);
            $('#btnSearch').button('reset');
        }
        if (self.model.configuration.includeCategory)
        {
            $('#cmbCategory').select2({
                dir: window.language === "ar" ? "rtl" : "ltr",
                language: window.language,
                width: "100%",
                allowClear: true,
                placeholder: "",
                data: categories,
                dropdownParent: $('#categoryContainer')
            }).on("select2:select", function (e)
            {
                var id = e.params.data.id.toString();
                loadFormByCategoryId(self, id);
            }).on("select2:unselect", function (e)
            {
                $("#categoryBuilder").empty();
                categoryFormBuilder = null;
                categoryFormIsValid = true;
                if (self.model.drawContentBuilder)
                {
                    $("#divContentBuilder").addClass('without-border').removeClass('with-border');
                } else
                {
                    $("#divDefault").addClass('without-border').removeClass('with-border');
                }
                $('#divCategoryBuilderBody').addClass('hidden');
                enableSearch();
            });
            $("#cmbCategory").val('').trigger('change');
            $('#cmbCategory').keydown(function (e)
            {
                var code = e.keyCode || e.which;
                if (code === 9)
                {
                    e.preventDefault();
                    if (e.shiftKey)
                    {
                        $('#btnClear').focus();
                    }
                    else
                    {
                        $('#txtSubject').focus();
                    }
                }
            });
            $('#btnClear').keydown(function (e)
            {
                var code = e.keyCode || e.which;
                if (code === 9)
                {
                    e.preventDefault();
                    if (e.shiftKey)
                    {
                        $('#btnSearch').focus();
                    }
                    else
                    {
                        $('#cmbCategory').focus();
                    }
                }
            });
            $('#cmbCategory').focus();
        }
        if (self.model.configuration.enableDelegation)
        {
            $('#cmbDelegation').select2({
                dir: window.language === "ar" ? "rtl" : "ltr",
                language: window.language,
                width: "100%",
                allowClear: false,
                placeholder: "",
                data: delegationUsersForSelect2(new DelegationUsers().get(window.language)),
                dropdownParent: $('#delegationContainer')
            });
            $('#cmbDelegation').keydown(function (e)
            {
                var code = e.keyCode || e.which;
                if (code === 9)
                {
                    e.preventDefault();
                    if (e.shiftKey)
                    {
                        $('#btnClear').focus();
                    }
                    else
                    {
                        $('#txtSubject').focus();
                    }
                }
            });
            $('#btnClear').keydown(function (e)
            {
                var code = e.keyCode || e.which;
                if (code === 9)
                {
                    e.preventDefault();
                    if (e.shiftKey)
                    {
                        $('#btnSearch').focus();
                    }
                    else
                    {
                        $('#cmbCategory').focus();
                    }
                }
            });
            $('#cmbCategory').focus();
        }
        if (!self.model.configuration.enableDelegation && !self.model.configuration.includeCategory)
        {
            $("#divDefault").addClass('without-border').removeClass('with-border');
        }
        $('.btn-scroll').on('click', function ()
        {
            if ($('.gridResult').is(":visible"))
            {
                $('.search').fadeIn();
                $('.gridResult').hide();
                $("#expandIcon").removeClass("fa-expand").addClass("fa-compress");
                $("#resultExpandIcon").removeClass("fa-expand").addClass("fa-compress");
            } else
            {
                $('.gridResult').fadeIn();
                $('.search').hide();
                $("#expandIcon").removeClass("fa-compress").addClass("fa-expand");
                $("#resultExpandIcon").removeClass("fa-compress").addClass("fa-expand");
            }
        });
    }
}
export default { DocumentAdvanceSearchModel, DocumentAdvanceSearchView };
