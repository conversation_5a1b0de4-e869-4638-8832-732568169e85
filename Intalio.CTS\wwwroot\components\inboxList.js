﻿import Intalio from './common.js'
import DocumentDetails from './documentDetails.js'
import { IdentityService, Categories, CategoryModel, DelegationUsers, Helper, Nodes } from './lookup.js'
import SendTransferModal from './sendTransfer.js'
import ReportCorrespondenceDetailExport from './reportCorrespondenceDetailExport.js'
import SendToReceivingEntityIndex from './sendToReceivingEntity.js'
import Transfer from './transfer.js'
import VipDocumentInbox from './vipInboxList.js'
import LinkedCorrespondenceIndex from './linkedCorrespondenceIndex.js'
import LinkedCorrespondenceDocument from './linkedCorrespondenceDocument.js'
import CustomizeNodeColomns from '../components/customizeNodeColomns.js'
import VisualTracking from './visualTracking.js'
import NoteComponentIndex from './noteComponent.js'


class DocumentInbox extends Intalio.Model
{
    constructor()
    {
        super();
        this.title = null;
        this.nodeId = null;
        this.delegationId = null;
        this.categories = null;
        this.statuses = null;
        this.priorities = null;
        this.privacies = null;
        this.fromStructureInbox = false;
        this.transferId = null;
        this.isExported = false;
        //this.IsInboxModeWithGrouping = false;
    }
}
var gfirstTime = true;
var gDataChanged = true;
var gAction = "";
var gFilterChanged = false;
var gFilterStructure = "";
var gFilterFromDate = "";
var gFilterToDate = "";
var gFilterUser = "";
var gFilterOverdue = "";
var gOverdue = false;
var clear = false;
var gReportName = "Inbox Report";
var gColumns = [];




//function completeTransfer(ids, model) {
//    Common.mask(document.getElementById('grdInboxItems'), "inboxListContainer-mask");
        
//        Common.showConfirmMsg(Resources.CompleteOneTsfConfirmation, function () {
//            Common.ajaxPost('/Transfer/Complete',
//                {
//                    'ids': ids, 'delegationId': model.delegationId, 'completeReasonNote': reason, '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
//                },
//                function (result) {
//                    if (result != null && result.length > 0) {
//                        swal.close()
//                        let msg = "";
//                        for (var i = 0; i < result.length; i++) {
//                            if (!result[i].updated) {
//                                msg += "\n ○ " + result[i].uncompletedDocumentReferenceNumber;
//                            }
//                        }
//                        if (msg !== "") {
//                            setTimeout(function () {
//                                Common.alertMsg(Resources.CannotCompleteWarning + msg);
//                            }, 300);
//                            Common.unmask("inboxListContainer-mask");
//                            GridCommon.Refresh(gTableName);
//                            TreeNode.refreshTreeNodeCounts(TreeNode.Draft);
//                        }
//                        else {
//                            Common.unmask("inboxListContainer-mask");
//                            Common.showScreenSuccessMsg();
//                            GridCommon.Refresh(gTableName);
//                            TreeNode.refreshTreeNodeCounts(TreeNode.Completed);
//                            TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);
//                            TreeNode.refreshTreeNodeCounts(TreeNode.StructureInbox);
//                            TreeNode.refreshTreeNodeCounts(TreeNode.StructureSent);
//                        }
//                    } else {
//                        Common.showScreenErrorMsg();
//                    }
//                }, null, false);
//        });
//}

function requestToCompleteTask(ids, model) {
    Common.mask(document.getElementById('grdInboxItems'), "inboxListContainer-mask");
    Common.ajaxPost('/Transfer/RequestToComplete',
        {
            'ids': ids, 'delegationId': model.delegationId, '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
        },
        function (result) {
            if (result != null && result.length > 0) {
                swal.close()
                let msg = "";
                for (var i = 0; i < result.length; i++) {
                    if (!result[i].updated) {
                        msg += "\n ○ " + result[i].documentId + " " + result[i].message;
                    }
                }
                if (msg !== "") {
                    setTimeout(function () {
                        Common.alertMsg(Resources.CannotCompleteWarning + msg);
                    }, 300);
                    Common.unmask("inboxListContainer-mask");
                    GridCommon.Refresh(gTableName);
                    TreeNode.refreshTreeNodeCounts(TreeNode.Draft);
                }
                else {
                    Common.unmask("inboxListContainer-mask");
                    Common.showScreenSuccessMsg();
                    GridCommon.Refresh(gTableName);
                    TreeNode.refreshTreeNodeCounts(TreeNode.Completed);
                    TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);
                    TreeNode.refreshTreeNodeCounts(TreeNode.StructureInbox);  TreeNode.refreshTreeNodeCounts(TreeNode.StructureSent);
                }
            } else {
                Common.showScreenErrorMsg();
            }
        }, null, false);
}

function dismissCarbonCopy(dismissIds, model, allSelectedData)
{
    Common.mask(document.getElementById('grdInboxItems'), "inboxListContainer-mask");
    Common.ajaxPost('/Transfer/DismissCarbonCopy',
        {
            'ids': dismissIds, 'delegationId': model.delegationId, '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
        },
        function (result)
        {
            if (result != null && result.length > 0)
            {
                swal.close()
                let msg = "";
                for (var i = 0; i < result.length; i++)
                {
                    if (!result[i].updated)
                    {
                        var transfer = $.grep(allSelectedData, function (e)
                        {
                            return e.id === result[i].transferId;
                        });
                        if (transfer[0])
                        {
                            msg += "\n ○ " + transfer[0].referenceNumber;

                        } else
                        {
                            msg += "\n ○ " + result[i].transferId;
                        }
                    }
                }
                if (msg !== "")
                {
                    setTimeout(function ()
                    {
                        Common.alertMsg(Resources.CannotDismissCarbonCopyWarning + msg);
                    }, 300);
                    Common.unmask("inboxListContainer-mask");
                    GridCommon.Refresh(gTableName);
                    TreeNode.refreshTreeNodeCounts(TreeNode.Draft);
                } else
                {
                    Common.unmask("inboxListContainer-mask");
                    Common.showScreenSuccessMsg();
                    GridCommon.Refresh(gTableName);
                    TreeNode.refreshTreeNodeCounts(TreeNode.Completed);
                    TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);
                    TreeNode.refreshTreeNodeCounts(TreeNode.StructureInbox);  TreeNode.refreshTreeNodeCounts(TreeNode.StructureSent);
                }
            } else
            {
                Common.showScreenErrorMsg();
            }
        }, null, false);
}
function format(row, nodeId)
{
  
    return '<table style="width:100%" cellspacing="0" border="0">' + buildColumnsDetails(row, nodeId) + '</table>';
}
function unlock(row, id, delegationId, nodeId, self)
{
    gLocked = true;
    Common.showConfirmMsg(Resources.UnlockConfirmation, function ()
    {
        Common.mask(document.getElementById('grdInboxItems'), "inboxListContainer-mask");
        var params = { "Id": id, "__RequestVerificationToken": $('input[name="__RequestVerificationToken"]').val() };
        if (delegationId !== null)
        {
            params.DelegationId = delegationId;
        }
        Common.ajaxPost('/Transfer/UnLock', params, function (response)
        {
            gLocked = false;
            if (response === "False")
            {
                Common.showScreenErrorMsg();
                Common.unmask("inboxListContainer-mask");
            }
            else if (response === "FileInUse")
            {
                setTimeout(function ()
                {
                    Common.alertMsg(Resources.FileInUse);
                }, 300);
                Common.unmask("inboxListContainer-mask");
            }
            else if (self.model.isExported)
            {
                var onclick = $(row).parent().parent().parent().find(".edit").attr("clickattr").replace("true", "false");
                $(row).parent().parent().parent().find(".edit").attr("clickattr", onclick);
                let btnView = document.createElement("button");
                btnView.setAttribute("class", "btn btn-xs btn-warning mr-sm view");
                btnView.setAttribute("title", Resources.View);
                btnView.setAttribute("clickattr", "openDocumentViewMode(" + id + ",true," + delegationId + "," + false + ", " + nodeId + ")");
                btnView.innerHTML = "<i class='fa fa-eye fa-white'/>";
                $(row).parent().parent().parent().find(".edit").parent().prepend(btnView.outerHTML);
                $(row).remove();
                Common.unmask("inboxListContainer-mask");
            }
            else
            {
                var onclick = $(row).parent().parent().parent().find(".edit").attr("clickattr").replace("true", "false");
                $(row).parent().parent().parent().find(".edit").attr("clickattr", onclick);
                let btnView = document.createElement("button");
                btnView.setAttribute("class", "btn btn-xs btn-warning mr-sm view");
                btnView.setAttribute("title", Resources.View);
                btnView.setAttribute("clickattr", "openDocumentViewMode(" + id + ",true," + delegationId + "," + false + ", " + nodeId + ")");
                btnView.innerHTML = "<i class='fa fa-eye fa-white'/>";
                $(row).parent().parent().parent().find(".edit").parent().prepend(btnView.outerHTML);
                $(row).remove();
                Common.unmask("inboxListContainer-mask");
                //$("#divLock" + id).empty();
            }
            GridCommon.RefreshCurrentPage("grdInboxItems", true);

        }, function ()
        {
            gLocked = false; Common.showScreenErrorMsg();
            Common.unmask("inboxListContainer-mask");
        });
    });
}
function markAsUnread(id, isRead, delegationId, isCced, nodeId,lockedDate, lockedby) {

    
    if (!lockedDate) {

        var params = {
            "Id": id,
            "__RequestVerificationToken": $('input[name="__RequestVerificationToken"]').val()
        };

        if (delegationId !== null) {
            params.DelegationId = delegationId;
        }


        if (isRead) {
            Common.ajaxPost('/Transfer/UnRead', params, function (response) {
                if (!response.success) {
                    if (response.message === "FileInUse") 
                        setTimeout(function () {
                            Common.alertMsg(Resources.FileInUse);
                        }, 300);
                }
                else
                    TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);
            });
            
        } 
    } else {
        Common.showScreenErrorMsg("This item cannot be marked as unread because it is locked");
    }
}
function openDocumentViewMode(id, isRead, delegationId, isCced, nodeId)
{
    gLocked = false;
    var params = { "Id": id, "__RequestVerificationToken": $('input[name="__RequestVerificationToken"]').val() };
    if (delegationId !== null)
    {
        params.DelegationId = delegationId;
    }
    if (!isRead && !isCced)
    {
        Common.showConfirmMsg(Resources.KindlyNoteMarkedRead + "\n" + Resources.OpenConfirmation, function ()
        {
            Common.ajaxPost('/Transfer/View', params, function ()
            {
                GridCommon.RefreshCurrentPage("grdInboxItems", true);
                openDocumentDetails(id, delegationId, true, nodeId, isCced);
            });
        });
    } else if (isCced)
    {
        Common.ajaxPost('/Transfer/View', params, function ()
        {
            GridCommon.RefreshCurrentPage("grdInboxItems", true);
            openDocumentDetails(id, delegationId, true, nodeId, isCced);
        });
    } else
    {
        openDocumentDetails(id, delegationId, true, nodeId, isCced);
    }
   
}
function openDocumentEditMode(id, lockedByMe, sentToUser, delegationId, nodeId, fromStructureInbox, isLocked)
{
   
    gLocked = false;
    var params = { "Id": id, "__RequestVerificationToken": $('input[name="__RequestVerificationToken"]').val() };
    if (delegationId !== null)
    {
        params.DelegationId = delegationId;
        params.fromStructureInbox = fromStructureInbox;
    }
    if ((!lockedByMe && !sentToUser) || (sentToUser && fromStructureInbox && !isLocked))
    {
        Common.showConfirmMsg(Resources.LockConfirmation, function ()
        {
            Common.ajaxPost('/Transfer/Lock', params, function (response)
            {
                if (typeof response !== "boolean")
                {
                    var titleLock = Resources.LockedBy + ": " + response + "</br>" + Resources.LockedDate + ": ";
                    var html = "<div class='mr-sm text-left' title=''><i class='fa fa-lock fa-lg text-danger infoDivIcon' title='" + titleLock + "'></i><div class='infoDiv font-13' style='opacity: 0;'>" +
                        titleLock + "</div></div>&nbsp;";
                    $("#divLock" + id).append(html);
                    setTimeout(function ()
                    {
                        Common.showConfirmMsg(Resources.AlreadyLockedBy + ": " + response + "\n" + Resources.DoYouWantToOpen, function ()
                        {
                            
                            TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);
                            GridCommon.RefreshCurrentPage("grdInboxItems", true);
                            openDocumentDetails(id, delegationId, false, nodeId);
                        }, function ()
                        {
                            
                            TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);
                            GridCommon.RefreshCurrentPage("grdInboxItems", true);
                        });
                    }, 300);
                }
                else
                {
                    
                    TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);
                    GridCommon.RefreshCurrentPage("grdInboxItems", true);
                    openDocumentDetails(id, delegationId, false, nodeId);
                }
            });
        });
    }
    //else if (fromStructureInbox)
    //{
    //    TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);
    //    GridCommon.RefreshCurrentPage("grdInboxItems", true);
    //    openDocumentDetails(id, delegationId, false, nodeId);
    //}
    else
    {
        Common.ajaxPost('/Transfer/View', params, function (response)
        {
            GridCommon.RefreshCurrentPage("grdInboxItems", true);
            openDocumentDetails(id, delegationId, false, nodeId);
        });
    }
}

function acceptRequest(id , delegationId)
{
    gLocked = false;
    Common.showConfirmMsg(Resources.AcceptRequestConfirmation, function () {
        var params = { "id": id };

        Common.ajaxPost('/Transfer/AcceptRequest', params, function (response) {
            if (response && response.success) {
                markAsUnread(id, true, delegationId, null, null, false);
                GridCommon.RefreshCurrentPage("grdInboxItems", true);
                TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);
                TreeNode.refreshTreeNodeCounts(TreeNode.Custom);
                Common.showScreenSuccessMsg();
            } else if (response && !response.success && response.message!=null)
            {
                Common.alertMsg(response.message);
            } else {
                Common.showScreenErrorMsg();
            }
            

        });
    });
    
}
function rejectRequest(id, delegationId) {

    if (window.EnableConfirmationMessage === "True") {
        Common.showConfirmMsg(Resources.RejectRequestConfirmation, function () {
            let modalWrapper = $(".modal-window");
            let modelIndex = new NoteComponentIndex.NoteComponentIndex();
            modelIndex.callback = function (response) {
                if (response && response.success) {
                    markAsUnread(id, true, delegationId, null, null, false);
                    GridCommon.RefreshCurrentPage("grdInboxItems", true);
                    TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);
                    TreeNode.refreshTreeNodeCounts(TreeNode.Custom);
                    Common.showScreenSuccessMsg();
                } else if (response && !response.success && response.message != null) {
                    Common.alertMsg(response.message);
                } else {
                    Common.showScreenErrorMsg();
                }
            }

                let NoteComponentIndexView = new NoteComponentIndex.NoteComponentIndexView(modalWrapper, modelIndex);

                NoteComponentIndexView.render({
                    url: '/Transfer/RejectRequest',
                    params: {
                        "id": id
                    }
                });
         
        });
    }
    else {
        let modalWrapper = $(".modal-window");
        let modelIndex = new NoteComponentIndex.NoteComponentIndex();
        modelIndex.callback = function (response) {
            if (response && response.success) {
                markAsUnread(id, true, delegationId, null, null, false);
                GridCommon.RefreshCurrentPage("grdInboxItems", true);
                TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);
                TreeNode.refreshTreeNodeCounts(TreeNode.Custom);
                Common.showScreenSuccessMsg();
            } else if (response && !response.success && response.message != null) {
                Common.alertMsg(response.message);
            } else {
                Common.showScreenErrorMsg();
            }

        }
            let NoteComponentIndexView = new NoteComponentIndex.NoteComponentIndexView(modalWrapper, modelIndex);

            NoteComponentIndexView.render({
                url: '/Transfer/RejectRequest',
                params: {
                    "id": id
                }
            });

        
    }
}

function openDocumentDetails(id, delegationId, readOnly, nodeId, isCced)
{
    gLocked = false;
    var params = { id: id };
    params.nodeId = nodeId;
    if (delegationId !== null)
    {
        params.delegationId = delegationId;
    }
    Common.ajaxGet('/Document/GetDocumentBasicInfoByTransferId', params, function (response)
    {
        if (response.id!=null) {
            let item = (fromStructureInbox ? "liStructureInbox" : "liInbox") + nodeId;

        if (delegationId !== null)
        {
                item = "index-" + nodeId;
            }
            Common.setActiveSidebarMenu(item);
            $(".delegation").removeClass("active");
            var wrapper = $(".modal-documents");
            var linkedCorrespondenceModel = new LinkedCorrespondenceIndex.LinkedCorrespondenceIndex();
            linkedCorrespondenceModel.reference = response.referenceNumber;
            linkedCorrespondenceModel.subject = response.subject;
            linkedCorrespondenceModel.documentId = response.id;
            var linkedCorrespondenceDocument = new LinkedCorrespondenceDocument.LinkedCorrespondenceDocumentView(wrapper, linkedCorrespondenceModel);
            linkedCorrespondenceDocument.render();

            var model = new DocumentDetails.DocumentDetails();
            model.categoryId = response.categoryId;
            model.delegationId = delegationId;
            model.id = id;
            model.documentId = response.id;
            model.referenceNumber = response.referenceNumber;
            model.categoryName = response.categoryName;
            model.statusId = response.status;
            model.createdByUser = response.createdByUser;
            model.readonly = readOnly === true;
            model.attachmentId = response.attachmentId;
            model.attachmentVersion = response.attachmentVersion;
            model.statuses = new CoreComponents.Lookup.Statuses().get(window.language);
            model.showMyTransfer = true;
            model.fromInbox = true;
            model.isCced = isCced;
            model.fromInbox = true;
            model.parentComponentId = linkedCorrespondenceDocument.model.ComponentId
            model.isModal = true;
            model.showBackButton = false;
            model.requestStatus = response.requestStatus;
            model.attachmentIslocked = response.attachmentIslocked;
            var securityMatrix = JSON.parse(new CoreComponents.Lookup.SecurityMatrix().get(window.language));// JSON.parse(sessionStorage.getItem("SecurityMatrix"));

            var tabs = securityMatrix.SecurityNodes[nodeId].SecurityCategories[response.categoryId].Tabs;
        model.tabs = $.grep(tabs, function (element, index)
        {
                return !element.Name.includes("myTransfer") && !element.Name.includes("documentMetadata") &&
                    !element.Name.includes("transferHistory") && !element.Name.includes("visualTracking") &&
                    !element.Name.includes("activityLog") && !element.Name.includes("attachments") && !element.Name.includes("notes") &&
                    !element.Name.includes("linkedDocument") && !element.Name.includes("nonArchivedAttachments");
            });
            model.tabsWithStatic = tabs;
            model.tabsActions = securityMatrix.SecurityNodes[nodeId].SecurityCategories[response.categoryId].SecurityTabs;

            wrapper = $(linkedCorrespondenceDocument.refs['linkDocumentDiv']);

            var view = new DocumentDetails.DocumentDetailsView(wrapper, model);
            view.render();

            $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).off("hidden.bs.modal");
            $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).off("shown.bs.modal");
            $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).on('shown.bs.modal', function () {
                $("ul[ref='tabDocumentDetails']").scrollingTabs('refresh');
            });
            $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).on('hidden.bs.modal', function () {
                if ($(this).data("remove") != true)
                    return;
                $(linkedCorrespondenceDocument.refs[linkedCorrespondenceDocument.model.ComponentId]).remove();
                swal.close();
                //if ($(".modal-documents").children().length > 0 || $(".modal-window").children().length > 0) {
                //    $('body').addClass('modal-open');
                //}
            });
            $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).modal("show");

            $(document).off('click', '.btn-back');
        $(document).on('click', '.btn-back', function ()
        {
                $("#gridContainerDiv").show();
                view.remove();
                $(".toRemove").remove();
            });

            $(document).off('click', '.btn-export');
        $(document).on('click', '.btn-export', function ()
        {
                var wrapper = $(".modal-window");
                var model = new ReportCorrespondenceDetailExport.ReportCorrespondenceDetailExport();
                model.documentId = response.id;
                model.delegationId = delegationId;
                var reportCorrespondenceDetailExportView = new ReportCorrespondenceDetailExport.ReportCorrespondenceDetailExportView(wrapper, model);
                reportCorrespondenceDetailExportView.render();

                $("#modalReportCorrespondenceDetailExport").off("hidden.bs.modal");
                $("#modalReportCorrespondenceDetailExport").off("shown.bs.modal");
            $("#modalReportCorrespondenceDetailExport").on('shown.bs.modal', function ()
            { });
            $("#modalReportCorrespondenceDetailExport").on('hidden.bs.modal', function ()
            { });
                $("#modalReportCorrespondenceDetailExport").modal("show");

            });
        }
    }, function () { Common.showScreenErrorMsg(); }, true);
}
function buildFilters(nodeId, categories)
{
    var nodes = new CoreComponents.Lookup.Nodes().getTreeNodes(window.language);
    var node = $.grep(nodes, function (element, index)
    {
        return element.id.toString() === nodeId;
    })[0];
    var filters = node.filters !== "" && node.filters !== null ? JSON.parse(node.filters) : [];

    if (filters.length > 0)
    {
        var html = '<div class="row">';
        var clearFix = 0;
        for (var i = 0; i < filters.length; i++)
        {
            var filter = filters[i];
            switch (filter)
            {
                case "ReferenceNumber":
                    clearFix += 3;
                    html += '<div class="col-lg-3 col-md-3 col-sm-6"><div class="form-group"><label class="control-label">' + Resources.ReferenceNumber + '</label>' +
                        '<input type="text" id="txtFilterInboxReferenceNumber" class="form-control" autocomplete="off" tabindex="' + i + '" aria-hidden="true"></div></div>';
                    break;
                case "FromDate":
                    clearFix += 3;
                    html += '<div class="col-lg-3 col-md-3 col-sm-6"><div class="form-group"><label class="control-label">' + Resources.FromDate + '</label>' +
                        '<div class="input-group date"><input id="filterInboxFromDate" type="text" tabindex="' + i + '" autocomplete="off" class="form-control" name="Date" data-parsley-errors-container="#filterInboxFromDateError">' +
                        '<span class="input-group-addon" id="filterInboxFromDate_img" style="cursor:pointer"><i class="fa fa-calendar" aria-hidden="true"></i></span><div id="filterInboxFromDateError"></div></div></div></div>';
                    break;
                case "ToDate":
                    clearFix += 3;
                    html += '<div class="col-lg-3 col-md-3 col-sm-6"><div class="form-group"><label class="control-label">' + Resources.ToDate + '</label>' +
                        '<div class="input-group date"><input id="filterInboxToDate" type="text" tabindex="' + i + '" autocomplete="off" class="form-control" name="Date" data-parsley-errors-container="#filterInboxToDateError">' +
                        '<span class="input-group-addon" id="filterInboxToDate_img" style="cursor:pointer"><i class="fa fa-calendar" aria-hidden="true"></i></span><div id="filterInboxToDateError"></div></div></div></div>';
                    break;
                case "Category":
                    clearFix += 3;
                    var div = '<div class="col-lg-3 col-md-3 col-sm-6 col-xs-12" id="categoryFilterInboxContainer"><div class="form-group"><label class="control-label">' + Resources.Category + '</label>' +
                        '<select id="cmbFilterInboxCategory" tabindex="' + i + '" data-parsley-errors-container="#cmbFilterInboxCategoryError" class="form-control">';
                    for (var j = 0; j < categories.length; j++)
                    {
                        div += '<option value="' + categories[j].id + '">' + categories[j].text + '</option>';
                    }
                    div += '</select><div id="cmbFilterInboxCategoryError"></div></div></div>';
                    html += div;
                    break;
                case "Subject":
                    clearFix += 3;
                    html += '<div class="col-lg-3 col-md-3 col-sm-6 col-xs-12"><div class="form-group"><label class="control-label">' + Resources.Subject +
                        '</label><input type="text" id="txtFilterInboxSubject" class="form-control" autocomplete="off" tabindex="' + i + '" aria-hidden="true"></div></div>';
                    break;
                case "Read":
                    clearFix += 2;
                    html += '<div class="col-lg-2 col-md-2 col-sm-6 col-xs-6"><div class="form-group"><label class="control-label"></label><div class="checkbox c-checkbox">' +
                        '<label><input tabindex="' + i + '" type="checkbox" id="chkFilterInboxRead" name="Read"><span class="fa fa-check"></span>' + Resources.Read + '</label></div></div></div>';
                    break;
                case "Locked":
                    clearFix += 2;
                    html += '<div class="col-lg-2 col-md-2 col-sm-6 col-xs-6"><div class="form-group"><label class="control-label"></label><div class="checkbox c-checkbox">' +
                        '<label><input tabindex="' + i + '" type="checkbox" id="chkFilterInboxLocked" name="Locked"><span class="fa fa-check"></span>' + Resources.Locked + '</label></div></div></div>';
                    break;
               
                case "OverDue":
                    clearFix += 2;
                    html += '<div class="col-lg-2 col-md-2 col-sm-6 col-xs-6"><div class="form-group"><label class="control-label"></label><div class="checkbox c-checkbox">' +
                        '<label><input tabindex="' + i + '" type="checkbox" id="chkFilterInboxOverdue" name="Overdue"><span class="fa fa-check"></span>' + Resources.OverDue + '</label></div></div></div>';
                    break;
                case "Purpose":
                    clearFix += 3;
                    html += '<div class="col-lg-3 col-md-3 col-sm-6 col-xs-12" id="purposeFilterInboxContainer"><div class="form-group"><label class="control-label">' + Resources.Purpose + '</label>' +
                        '<select id="cmbFilterInboxPurpose" tabindex="' + i + '" data-parsley-errors-container="#cmbFilterInboxPurposeError" class="form-control"></select></div></div>';
                    break;
                case "Priority":
                    clearFix += 3;
                    html += '<div class="col-lg-3 col-md-3 col-sm-6 col-xs-12" id="priorityFilterInboxContainer"><div class="form-group"><label class="control-label">' + Resources.Priority + '</label>' +
                        '<select id="cmbFilterInboxPriority" tabindex="' + i + '" data-parsley-errors-container="#cmbFilterInboxPriorityError" class="form-control"></select></div></div>';
                    break;
                case "Period":
                    clearFix += 3;
                    html += '<div class="col-lg-3 col-md-3 col-sm-6 col-xs-12" id="periodFilterInboxContainer"><div class="form-group"><label class="control-label">' + Resources.Period + '</label>' +
                        '<select id="cmbFilterInboxPeriod" tabindex="' + i + '" data-parsley-errors-container="#cmbFilterInboxPeriodError" class="form-control"></select></div></div>';
                    break;
                case "Privacy":
                    clearFix += 3;
                    html += '<div class="col-lg-3 col-md-3 col-sm-6 col-xs-12" id="privacyFilterInboxContainer"><div class="form-group"><label class="control-label">' + Resources.Privacy + '</label>' +
                        '<select id="cmbFilterInboxPrivacy" tabindex="' + i + '" data-parsley-errors-container="#cmbFilterInboxPrivacyError" class="form-control"></select></div></div>';
                    break;
                case "Structure":
                    clearFix += 3;
                    html += '<div class="col-lg-3 col-md-3 col-sm-6 col-xs-12" id="structureFilterInboxContainer"><div class="form-group"><label class="control-label">' + Resources.Structure + '</label>' +
                        '<select id="cmbFilterInboxStructure" tabindex="' + i + '" data-parsley-errors-container="#cmbFilterInboxStructureError" class="form-control"></select></div></div>';
                    break;
                case "User":
                    clearFix += 3;
                    html += '<div class="col-lg-3 col-md-3 col-sm-6 col-xs-12" id="userFilterInboxContainer"><div class="form-group"><label class="control-label">' + Resources.User + '</label>' +
                        '<select id="cmbFilterInboxUser" tabindex="' + i + '" data-parsley-errors-container="#cmbFilterInboxUserError" class="form-control"></select></div></div>';
                    break;
            }
            if (clearFix === 12 || clearFix === 11)
            {
                clearFix = 0;
                html += '<div class="clearfix"></div>';
            }
        }
        html += '<div class="col-lg-3 col-md-3 col-sm-6 col-xs-12 list-action-filter"><button id="btnFilterInboxSearch" tabindex="9" type="button" class="btn btn-primary">' + Resources.Search + '</button>' +
            ' <button id="btnFilterInboxClear" tabindex="10" type="button" class="btn btn-clear">' + Resources.Clear + '</button></div>';
        html += '</div>';
        $('#filtersContainer').html(html);

        
        $("#btnFilterInboxSearch").on('click', function ()
        {
            GridCommon.Refresh(gTableName);
        });
        $("#btnFilterInboxClear").on('click', function ()
        {
            $("#cmbFilterInboxPurpose").val('').trigger('change');
            $("#cmbFilterInboxPriority").val('').trigger('change');
            $("#cmbFilterInboxPrivacy").val('').trigger('change');
            $("#cmbFilterInboxCategory").val('').trigger('change');
            $("#txtFilterInboxReferenceNumber").val('');
            $("#txtFilterInboxSubject").val('');
            fromDate.clear();
            toDate.clear();
            $("#chkFilterInboxRead").prop("checked", false);
            $("#chkFilterInboxLocked").prop("checked", false);
            $("#chkFilterInboxAssigned").prop("checked", false);
            $("#chkFilterInboxOverdue").prop("checked", false);
            $("#cmbFilterInboxStructure").val('').trigger('change');
            $("#cmbFilterInboxUser").val('').trigger('change');
            $("#cmbFilterInboxPeriod").val('').trigger('change');

            GridCommon.Refresh(gTableName);
        });

        $('#cmbFilterInboxPurpose').select2({
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            width: "100%",
            allowClear: false,
            placeholder: "",
            data: new CoreComponents.Lookup.Purposes().get(window.language),
            dropdownParent: $('#purposeFilterInboxContainer')
        });
        $("#cmbFilterInboxPurpose").val('').trigger('change');

        $('#cmbFilterInboxPriority').select2({
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            width: "100%",
            allowClear: false,
            placeholder: "",
            data: new CoreComponents.Lookup.Priorities().get(window.language),
            dropdownParent: $('#priorityFilterInboxContainer')
        });

        $("#cmbFilterInboxPriority").val('').trigger('change');

        $('#cmbFilterInboxPeriod').select2({
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            width: "100%",
            allowClear: false,
            placeholder:
                window.language === "ar" ? "اختر المدة" :
                    window.language === "fr" ? "Sélectionnez la durée" :
                        "Select period",
            data: [
                {
                    id: "day", text:
                        window.language === "ar" ? "اليوم الأخير" :
                            window.language === "fr" ? "Dernier jour" :
                                "Last Day"
                },
                {
                    id: "week", text:
                        window.language === "ar" ? "الأسبوع الأخير" :
                            window.language === "fr" ? "Dernière semaine" :
                                "Last Week"
                },
                {
                    id: "month", text:
                        window.language === "ar" ? "الشهر الأخير" :
                            window.language === "fr" ? "Dernier mois" :
                                "Last Month"
                }
            ],
            dropdownParent: $('#periodFilterInboxContainer')
        });


        $("#cmbFilterInboxPeriod").val('').trigger('change');

        $('#cmbFilterInboxPrivacy').select2({
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            width: "100%",
            allowClear: false,
            placeholder: "",
            data: new CoreComponents.Lookup.Privacies().get(window.language),
            dropdownParent: $('#privacyFilterInboxContainer')
        });
        $("#cmbFilterInboxPrivacy").val('').trigger('change');

        $('#cmbFilterInboxCategory').select2({
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            width: "100%",
            allowClear: false,
            placeholder: "",
            dropdownParent: $('#categoryFilterInboxContainer')
        });
        $("#cmbFilterInboxCategory").val('').trigger('change');

        $('#cmbFilterInboxStructure').select2({
            minimumInputLength: 0,
            allowClear: false,
            placeholder: "",
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            dropdownParent: $("#structureFilterInboxContainer"),
            multiple: true,
            width: "100%",
            ajax: {
                delay: 400,
                url: window.IdentityUrl + '/api/SearchStructuresWithSearchAttributes',
                type: "POST",
                dataType: 'json',
                headers: { Authorization: 'Bearer ' + window.IdentityAccessToken },
                data: function (term)
                {
                    return {
                        "text": term.term ? term.term : "", "language": window.language, "attributes": [window.StructureNameAr, window.StructureNameFr]
                    };
                },
                processResults: function (data)
                {
                    var listitemsMultiList = [];
                    $.each(data, function (key, val)
                    {
                        var structureName = val.name;
                        if (val.attributes != null && val.attributes.length > 0)
                        {
                            var attributeLang = $.grep(val.attributes, function (e)
                            {
                                return e.text === window.StructureNameAr && window.language === "ar" ? e.value : (e.text === window.StructureNameFr && window.language === "fr" ? e.value : "");
                            });
                            if (attributeLang.length > 0)
                            {
                                structureName = attributeLang[0].value === "" ? structureName : attributeLang[0].value;
                            }
                        }
                        var item = {};
                        item.id = val.id;
                        item.text = structureName;
                        item.isStructure = true;
                        item.dataId = val.id;
                        listitemsMultiList.push(item);
                    });
                    return {
                        results: listitemsMultiList
                    };
                }
            },
            sorter: data => data.sort((a, b) => a.text.localeCompare(b.text))
        }).on("change", function ()
        {
            if (document.getElementById('cmbFilterInboxUser') !== null)
            {
                var type = "GET";
                var url = '/api/SearchUsers';
                var structures = $('#cmbFilterInboxStructure').val();
                //if (structures !== "" && structures !== null)
                //{
                //    type = "POST";
                //    url = '/api/SearchUsersByStructureIds';
                //}
                $('#cmbFilterInboxUser').select2({
                    minimumInputLength: 0,
                    allowClear: false,
                    placeholder: "",
                    dir: window.language === "ar" ? "rtl" : "ltr",
                    language: window.language,
                    dropdownParent: $("#userFilterInboxContainer"),
                    multiple: true,
                    width: "100%",
                    ajax: {
                        delay: 400,
                        url: window.IdentityUrl + url,
                        type: type,
                        dataType: 'json',
                        headers: { Authorization: 'Bearer ' + window.IdentityAccessToken },
                        data: function (term)
                        {
                            var params = { "text":  "", "language": window.language };
                            //var params = { "text": term.term ? term.term : "", "language": window.language };
                            //var structures = $('#cmbFilterInboxStructure').val();
                            //if (structures !== "" && structures !== null)
                            //{
                            //    params.ids = $('#cmbFilterInboxStructure').val();
                            //}
                            return params;
                        },
                        processResults: function (data,term)
                        {
                            var structures = $('#cmbFilterInboxStructure').val();
                           var termSearch= term.term ? term.term : "";
                            var listitemsMultiList = [];
                            $.each(data, function (key, val)
                            {
                                if (structures !== "" && structures !== null &&
                                   !((val.structureIds != null && val.structureIds.some(r => structures.indexOf(String(r)) >= 0)) ||
                                    structures.includes(val.defaultStructureId))) {
                                    return;
                                }
                                var fullName = val.fullName;
                                if (window.language != 'en') {
                                    fullName = getFullNameByLangauge(val);
                                    fullName = fullName.trim() == "" ? val.fullName : fullName
                                }
                                var allNames = getFullNameInAllLangauge(val);
                                if (allNames.length == 0) allNames.push(fullName);
                                if (termSearch != "" &&
                                    !allNames.some(r => r.toLowerCase().includes(termSearch.toLowerCase()))) {
                                    return;
                                }
                                let isExist = listitemsMultiList.some(function (usr) { return usr.id === val.id; });
                                if (!isExist) {
                                    var item = {};
                                    item.id = val.id;
                                    item.text = fullName;
                                    item.isStructure = false;
                                    item.dataId = val.id;
                                    listitemsMultiList.push(item);
                                }
                                
                            });
                            return {
                                results: listitemsMultiList
                            };
                        }
                    },
                    sorter: data => data.sort((a, b) => a.text.localeCompare(b.text))
                }).val("").trigger("change");
            }
        });
        $("#cmbFilterInboxStructure").val('').trigger('change');
        $('#cmbFilterInboxUser').select2({
            minimumInputLength: 0,
            allowClear: false,
            placeholder: "",
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            dropdownParent: $("#userFilterInboxContainer"),
            multiple: true,
            width: "100%",
            ajax: {
                delay: 400,
                url: window.IdentityUrl + '/api/SearchUsers',
                type: "GET",
                dataType: 'json',
                headers: { Authorization: 'Bearer ' + window.IdentityAccessToken },
                data: function (term)
                {
                    return { "text":  "", "language": window.language };
                    //return { "text": term.term ? term.term : "", "language": window.language };
                },
                processResults: function (data, term)
                {
                    var termSearch = term.term ? term.term : "";

                    var listitemsMultiList = [];
                    $.each(data, function (key, val)
                    {
                        var fullName = val.fullName;
                        if (window.language != 'en') {
                            fullName = getFullNameByLangauge(val);
                            fullName=fullName.trim() == "" ? val.fullName : fullName;
                        }
                        var allNames = getFullNameInAllLangauge(val);
                        if (allNames.length == 0) allNames.push(fullName);
                        if (termSearch != "" &&
                            !allNames.some(r => r.toLowerCase().includes(termSearch.toLowerCase()))) {
                            return;
                        }
                        let isExist = listitemsMultiList.some(function (usr) { return usr.id === val.id; });
                        if (!isExist) {
                            var item = {};
                            item.id = val.id;
                            item.text = fullName;
                            item.isStructure = false;
                            item.dataId = val.id;
                            listitemsMultiList.push(item);
                        }
                    });
                    return {
                        results: listitemsMultiList
                    };
                }
            },
            sorter: data => data.sort((a, b) => a.text.localeCompare(b.text))
        });
        $("#cmbFilterInboxUser").val('').trigger('change');

        var fromDate = $('#filterInboxFromDate').flatpickr({
            noCalendar: false,
            enableTime: false,
            dateFormat: 'd/m/Y',
            onOpen: function (ct)
            {
                this.set({
                    maxDate: jQuery('#filterInboxToDate').val() && jQuery('#filterInboxToDate').val() !== "" ? jQuery('#filterInboxToDate').val() : moment().format('DD/MM/YYYY')
                });
            }
        });
        $("#filterInboxFromDate_img").click(function ()
        {
            fromDate.toggle();
        });
        var toDate = $('#filterInboxToDate').flatpickr({
            noCalendar: false,
            enableTime: false,
            dateFormat: 'd/m/Y',
            onOpen: function (ct)
            {
                this.set({
                    minDate: jQuery('#fromDate').val() && jQuery('#fromDate').val() !== "" ? jQuery('#fromDate').val() : false,
                    maxDate: moment().format('DD/MM/YYYY')
                });
            }
        });
        $("#filterInboxToDate_img").click(function ()
        {
            toDate.toggle();
        });
        $('#txtFilterInboxReferenceNumber').keydown(function (e)
        {
            var code = e.keyCode || e.which;
            if (code === 9)
            {
                e.preventDefault();
                if (e.shiftKey)
                {
                    $('#btnFilterInboxSearch').focus();
                }
                else
                {
                    $('#filterInboxFromDate').focus();
                }
            }
        });
        $('#btnFilterInboxClear').keydown(function (e)
        {
            var code = e.keyCode || e.which;
            if (code === 9)
            {
                e.preventDefault();
                if (e.shiftKey)
                {
                    $('#btnFilterInboxSearch').focus();
                }
                else
                {
                    $('#txtFilterInboxReferenceNumber').focus();
                }
            }
        });
    } else
    {
        $('.searchToRemove > div:first-child').hide();
        $("#gridPanel").removeAttr("style");
        $("#gridPanel").addClass("panel-default");
    }
}
function buildColumns(gridcolumns, nodeId,fromChildTable)
{

    gridcolumns.push({
        visible: false,
        title: Resources.Notes,
        data: "notes",
        render: function (data, type, full, meta) {
            if (Array.isArray(full.note) && full.note.length > 0) {
                // Add a bullet point before each note and join with new lines
                return full.note.map(n => `\u2022 ${n.notes} \n`).join('\n');
            }

            return "";
        }
    });

    gridcolumns.push({
        visible: false,
        title: Resources.Instructions,
        data: "instruction",
        render: function (data, type, full, meta) {
            if (full.categoryId == 8) {
                data = full.body;
            }
            // Check if data is null or undefined, return an empty string if so
            if (data == null || data.length === 0) {
                return "";
            }
            // Otherwise, return the data with the text renderer for HTML safety
            return $.fn.dataTable.render.text().display(data);
        },
        orderable: !fromChildTable,
        orderSequence: fromChildTable ? [] : ["asc", "desc"],
        className: "min-max-width-150-250"
    });
    var nodes = new CoreComponents.Lookup.Nodes().getTreeNodes(window.language);
    var node = $.grep(nodes, function (element, index)
    {
        return element.id.toString() === nodeId;
    })[0];
    var newColomns = new Nodes().getCustomcolumns(nodeId);


    if (newColomns) {
        node.columns = newColomns.content;
    }
    var columns = node.columns !== "" && node.columns !== null ? JSON.parse(node.columns) : [];
    gColumns = $.grep(columns, function (element, index) {
        return element.isColumnDetail === false;
    })
    var columnDetails = $.grep(columns, function (element, index)
    {
        return element.isColumnDetail === true;
    });
    if (columnDetails.length > 0 || (IsInboxModeWithGrouping && !fromChildTable)) {
        gridcolumns.push(
            {
                title: fromChildTable ? '' : '<a id="expandAll" class="expand-control expand"></a>',
                "className": 'details-control',
                "orderable": false,
                "data": null,
                "defaultContent": '',
                width: '16px'
            });
    }
    gridcolumns.push(
        {
            "className": "text-left",
            "autoWidth": false,
            "bAutoWidth": false,
            width: "16px",
            'orderable': false,
            'sortable': false,
            data: "importanceId",
            'render': function (data, type, full, meta)
            {
                var importances = new CoreComponents.Lookup.Importances().get(window.language);
                for (var i = 0; i < importances.length; i++)
                {
                    if (importances[i].id === full.id)
                    {
                        let btnView = document.createElement("i");
                        btnView.setAttribute("class", "fa fa-exclamation fa-lg");
                        btnView.setAttribute("style", "color:" + importances[i].color);
                        btnView.setAttribute("title", importances[i].text);
                        return btnView.outerHTML;
                    }
                }
                return "";
            }
        });
    
    columns.sort((a, b) => (parseInt(a.order) > parseInt(b.order)) ? 1 : -1);
    var customColumn = [];
    for (var i = 0; i < columns.length; i++)
    {
        var column = columns[i];
        if (!column.isColumnDetail)
        {
            if (column.isCustom === true)
            {
                customColumn.push({ columnName: column.name, functionName: column.customFunctionName });
                var customColumnKeyword = column.customFunctionName;
                var escapedVariable = column.customFunctionName.replace(/"/g, '&quot;');

                if (customColumnKeyword.includes("()")) {
                    if (customColumnKeyword == "getCommitteeName()") {
                        customColumnKeyword = "Meeting Type";
                    }

                    if (customColumnKeyword == "getMeetingLocation()") {
                        customColumnKeyword = "Meeting Location";
                    }
                }

                gridcolumns.push({
                    title: GetCustomAttributeTranslationByLangauge(customColumnKeyword), "orderable": false, 'defaultContent': '<div data-function="' + escapedVariable + '"></div>',
                    "createdCell": function (td, cellData, rowData, row, col)
                    {
                        var htmlCell = "";
                        var div = td.children[0];
                        var customFunctionName = div !== null && typeof div !== "undefined" ? $(div).attr("data-function") : "";
                        //if (customFunctionName && typeof window[customFunctionName.replace("(", "").replace(")", "")] === 'function')
                        //{
                        //    var functionName = customFunctionName.split("(")[0];
                        //    htmlCell = eval(functionName + "(" + JSON.stringify(rowData) + ")");
                        //}

                        var customColumns = JSON.parse(rowData.documentForm.form);
                        var columnName = customColumn.find(f => f.functionName == customFunctionName).columnName;

                        var customData = customColumns[columnName]

                        if (customFunctionName.includes("()")) {
                            if (customFunctionName == "getCommitteeName()") {
                                customData = getCommitteeName(customData);
                            }

                            if (customFunctionName == "getMeetingLocation()") {
                                customData = getMeetingLocation(customData);
                            }
                        }

                        htmlCell = htmlCell == "" && customColumns != null ? (customData == undefined ? "" : customData) : htmlCell;

                        $(td).html(htmlCell);
                    }
                });
            } else
            {
                switch (column.name)
                {
                    case "To":
                        gridcolumns.push({
                            visible: fromChildTable | !IsInboxModeWithGrouping,
                            title: Resources.To, "orderable": false,
                            "render": function (data, type, full, meta) {
                                var retValue = "";
                                if (full.toStructure) {
                                    retValue += full.toStructure;
                                }
                                if (full.toUser) {
                                    var user = full.toUser;
                                    retValue += retValue !== "" ? "/" + user : user;
                                }
                                return retValue;
                            }
                        });
                        break;
                    case "Note":
                        gridcolumns.push(
                            {
                                title: Resources.Notes,
                                data: "notes",
                                render: function (data, type, full, meta) {
                                    
                                    if (Array.isArray(full.note) && full.note.length > 0) {
                                        return full.note.map(n => n.notes).join(',');
                                    }
                                    return "";
                                }
                            }
                        )
                        break;
                    case "Instruction":
                        gridcolumns.push(
                            {
                                title: Resources.Instructions,
                                data: "instruction",
                                render: function (data, type, full, meta) {
                                    gridcolumns.push({
                                        title: Resources.Instructions, data: "instruction",
                                        render: $.fn.dataTable.render.text(), "orderable": !fromChildTable, orderSequence:
                                            fromChildTable == true ? [] : ["asc", "desc"], "className": "min-max-width-150-250"
                                    });
                                    return "";
                                }
                            }
                        )
                        break;
                    case "Category":
                        gridcolumns.push({
                            title: Resources.Category, data: "categoryId",  "orderable": !fromChildTable, orderSequence: fromChildTable==true?[]:["asc", "desc"],
                            render: function (data, type, full, meta)
                            {
                                var categories = new Categories().get(window.language);
                                for (var i = 0; i < categories.length; i++)
                                {
                                    if (categories[i].id === data)
                                    {
                                        return categories[i].text;
                                    }
                                }
                                return "";
                            }
                        });
                        break;
                    case "ReferenceNumber":
                        gridcolumns.push({
                            title: Resources.ReferenceNumber, data: "referenceNumber",
                            render: function (data, type, full, meta) {
                                if (full.referenceNumber != undefined && full.referenceNumber != "" && full.referenceNumber != null) {
                                    var linkedCorresRefNumList = full.referenceNumber.split(',');
                                    var html = '';
                                    $(linkedCorresRefNumList).each(function () {
                                        html += "<div>" + this + "</div>";
                                    });
                                    return html;
                                }
                                return "";
                            },
                            "orderable": false, "className": "min-max-width-150-250 copyToClipboard"
                        });
                        break;
                    case "Subject":
                        gridcolumns.push({ title: Resources.Subject, data: "subject", render: function(data, type, full, meta)
                            {
                                return `<span class="subjectSpan">${full.subject ?? ""}</span>`;
                            }, "orderable": !fromChildTable, orderSequence: fromChildTable == true ? [] : ["asc", "desc"], "className": "min-max-width-150-250" });
                        break;
                    case "From":
                        gridcolumns.push({
                            visible: fromChildTable | !IsInboxModeWithGrouping,

                            title: Resources.From, "orderable": false,
                            "render": function (data, type, full, meta)
                            {
                                var retValue = "";
                                if (full.fromStructure)
                                {
                                    retValue += full.fromStructure;
                                }
                                if (full.fromUser)
                                {
                                    var user = full.fromUser;
                                    retValue += retValue !== "" ? "/" + user : user;
                                }
                                return retValue;
                            }
                        });
                        break;
                    case "TransferDate":
                        gridcolumns.push({
                            visible: fromChildTable | !IsInboxModeWithGrouping,

                            title: Resources.TransferDate, data: "transferDate",
                            render: function (data, type, full, meta)
                            {
                                return DateConverter.toHijriFormated(full.transferDate, null, window.CalendarType);
                            }, "orderable": !fromChildTable, orderSequence: fromChildTable == true ? [] : ["asc", "desc"], width: "150px",
                            "orderable": false, "className": "min-max-width-150-250"
                        });
                        break;
                    case "TransferNumberOfDays":
                        gridcolumns.push({
                            visible: fromChildTable | !IsInboxModeWithGrouping,

                            title: Resources.NumberOfDays, data: "transferNumberOfDays",
                           
                            width: "150px",
                            "orderable": false,
                            
                        });
                        break;
                    case "ExportedNumberOfDays":
                        gridcolumns.push({
                            visible: fromChildTable | !IsInboxModeWithGrouping,

                            title: Resources.ExportedNumberOfDays, data: "exportedNumberOfDays",
                            
                            width: "150px",
                            "orderable": false,
                            
                        });
                        break;
                    case "ExportedDate":
                        gridcolumns.push({


                            title: Resources.ExportedDate, data: "exportedDate",
                            render: function (data, type, full, meta) {
                                return DateConverter.toHijriFormated(full.exportedDate, null, window.CalendarType);
                            }, width: "150px",
                            "orderable": false, "className": "min-max-width-150-250"
                        });
                        break;
                    case "ExportedUser":
                        gridcolumns.push({
                            title: Resources.ExportedUser, data: "fromUser",
                            render: function (data, type, full, meta) {
                                if (full.fromUser != undefined && full.fromUser != "" && full.fromUser != null) {
                                    var html = '';
                                    html += "<div>" + full.fromUser + "</div>";
                                    return html;
                                }
                                return "";
                            },
                            "orderable": false, "className": "min-max-width-150-250 copyToClipboard"
                        });
                        break;
                    case "CreatedDate":
                        gridcolumns.push({
                            title: Resources.CreatedDate, data: "createdDate",  "orderable": !fromChildTable, orderSequence: fromChildTable==true?[]:["asc", "desc"], width: "150px",
                            render: function (data, type, full, meta)
                            {
                                return DateConverter.toHijriFormated(full.createdDate, null, window.CalendarType);
                            },
                            "orderable": false, "className": "min-max-width-150-250"
                        });
                        break;
                    case "SendingEntity":
                        gridcolumns.push({ title: Resources.SendingEntity, data: "sendingEntity", "orderable": false, width: "150px" });
                        break;
                    case "ReceivingEntity":
                        gridcolumns.push({ title: Resources.ReceivingEntity, data: "receivingEntity", "orderable": false, width: "150px" });
                        break;
                    case "Purpose":
                        gridcolumns.push({
                            title: Resources.Purpose, data: "purpose", "orderable": false, width: "150px",
                            render: function (data, type, full, meta)
                            {
                                var purposes = new CoreComponents.Lookup.Purposes().get(window.language);
                                for (let i = 0; i < purposes.length; i++)
                                {
                                    if (purposes[i].id === full.purposeId)
                                    {
                                        return purposes[i].text;
                                    }
                                }
                                return "";
                            },
                            "orderable": false, "className": "min-max-width-150-250"
                        });
                        break;
                    case "Priority":
                        gridcolumns.push({
                            title: Resources.Priority, data: "priority", "orderable": false, width: "150px",
                            render: function (data, type, full, meta)
                            {
                                var priorities = new CoreComponents.Lookup.Priorities().get(window.language);
                                for (let i = 0; i < priorities.length; i++)
                                {
                                    if (priorities[i].id === full.priorityId)
                                    {
                                        return priorities[i].text;
                                    }
                                }
                                return "";
                            }
                        });
                        break;
                    case "Privacy":
                        gridcolumns.push({
                            title: Resources.Privacy, data: "privacy", "orderable": false, width: "150px",
                            render: function (data, type, full, meta)
                            {
                                var privacies = new CoreComponents.Lookup.Privacies().get(window.language);
                                for (let i = 0; i < privacies.length; i++)
                                {
                                    if (privacies[i].id === full.privacyId)
                                    {
                                        return privacies[i].text;
                                    }
                                }
                                return "";
                            }
                        });
                        break;
                    case "CreatedBy":
                        gridcolumns.push({ title: Resources.CreatedBy, data: "createdByUser", "orderable": false, width: "150px", "className": "min-max-width-150-250" });
                        break;
                    case "OpenedDate":
                        gridcolumns.push({
                            title: Resources.OpenedDate, data: "openedDate", "orderable": false, width: "150px",
                            render: function (data, type, full, meta)
                            {
                                return DateConverter.toHijriFormated(full.openedDate, ConversionDateFormat.DateTimeFormat24_Slash, window.CalendarType);
                            },
                            "className": "min-max-width-150-250"
                        });
                        break;
                    case "DueDate":
                        gridcolumns.push({
                            title: Resources.DueDate, data: "dueDate", "orderable": false, width: "150px",
                            render: function (data, type, full, meta)
                            {
                                return DateConverter.toHijriFormated(full.dueDate, ConversionDateFormat.DateTimeFormat24_Slash, window.CalendarType);
                            },
                            "orderable": false, "className": "min-max-width-150-250"
                        });
                        break;
                    case "SignedBy":
                        gridcolumns.push({ title: Resources.SignedBy, data: "signedBy", "orderable": false, width: "150px", "className": "min-max-width-150-250" });
                        break;
                }
            }
        }
    }
}
function buildColumnsDetails(row, nodeId)
{
    
    var nodes = new CoreComponents.Lookup.Nodes().getTreeNodes(window.language);
    var node = $.grep(nodes, function (element, index)
    {
        return element.id.toString() === nodeId;
    })[0];
    var columns = node.columns !== "" && node.columns !== null ? JSON.parse(node.columns) : [];
    columns.sort((a, b) => (parseInt(a.order) > parseInt(b.order)) ? 1 : -1);
    var html = "";
    for (var i = 0; i < columns.length; i++)
    {
        var column = columns[i];
        if (column.isColumnDetail)
        {
            if (column.isCustom === true)
            {
                var customColumn = column.name;
                var customColumnKeyword = column.customFunctionName;

                var htmlCell = "";
                var customFunctionName = column.customFunctionName;
                //if (customFunctionName && typeof window[customFunctionName.replace("(", "").replace(")", "")] === 'function')
                //{
                //    var functionName = customFunctionName.split("(")[0];
                //    htmlCell = eval(functionName + "(" + JSON.stringify(row.data()) + ")");
                //}


                if (customColumnKeyword.includes("()")) {
                    if (customColumnKeyword == "getCommitteeName()") {
                        customColumnKeyword = "Meeting Type";
                    }

                    if (customColumnKeyword == "getMeetingLocation()") {
                        customColumnKeyword = "Meeting Location";
                    }
                }

                var customColumns = JSON.parse(row.data().documentForm.form);

                customColumns = customColumns[customColumn];

                if (customFunctionName.includes("()")) {
                    if (customFunctionName == "getCommitteeName()") {
                        customColumns = getCommitteeName(customColumns);
                    }

                    if (customFunctionName == "getMeetingLocation()") {
                        customColumns = getMeetingLocation(customColumns);
                    }
                }

                htmlCell = htmlCell == "" && customColumns != null ? (customColumns == undefined ? "" : customColumns) : htmlCell;

                html += '<tr><th style="width: 15%;padding:5px">' + (GetCustomAttributeTranslationByLangauge(customColumnKeyword)) + ':</th><td style="width: 85%;padding:5px;word-break: break-all;"">' + htmlCell + '</td></tr>';

            } else
            {
                
                switch (column.name)
                {
                   
                    case "Category":
                        var category = "";
                        var categories = new Categories().get(window.language);
                        if (categories && Array.isArray(categories)) {
                            var matchedCategory = categories.find(c => c.id === row.data().categoryId);
                            category = matchedCategory ? matchedCategory.text : "";
                        }
                        html += '<tr><th style="width: 10%;padding:5px">' + Resources.Category + ':</th>' +
                            '<td style="width: 85%;padding:5px;word-break: break-all;">' + category + '</td></tr>';
                        break;
                    case "ReferenceNumber":
                        html += '<tr><th style="width: 15%;padding:5px">' + Resources.ReferenceNumber + ':</th><td style="width: 85%;padding:5px;word-break: break-all;"">' + (row.data().referenceNumber || '') + '</td></tr>';
                        break;
                    case "Subject":
                        html += '<tr><th style="width: 15%;padding:5px">' + Resources.Subject + ':</th><td style="width: 85%;padding:5px;word-break: break-all;"">' + (row.data().subject || '') + '</td></tr>';
                        break;
                    
                    
                    case "From":
                        var from = "";
                        if (row.data().fromStructure)
                        {
                            from += row.data().fromStructure;
                        }
                        if (row.data().fromUser)
                        {
                            var user = row.data().fromUser;
                            from += from !== "" ? "/" + user : user;
                        }
                        html += '<tr><th style="width: 15%;padding:5px">' + Resources.From + ':</th><td style="width: 85%;padding:5px;word-break: break-all;"">' + from + '</td></tr>';
                        break;
                    case "TransferDate":
                        html += '<tr><th style="width: 15%;padding:5px">' + Resources.TransferDate + ':</th><td style="width: 85%;padding:5px;word-break: break-all;"">' + (DateConverter.toHijriFormated(row.data().transferDate, null, window.CalendarType) || '') + '</td></tr>';
                        break;
                    case "CreatedDate":
                        html += '<tr><th style="width: 15%;padding:5px">' + Resources.CreatedDate + ':</th><td style="width: 85%;padding:5px;word-break: break-all;"">' + (DateConverter.toHijriFormated(row.data().createdDate, null, window.CalendarType) || '') + '</td></tr>';
                        break;
                    case "SendingEntity":
                        html += '<tr><th style="width: 15%;padding:5px">' + Resources.SendingEntity + ':</th><td style="width: 85%;padding:5px;word-break: break-all;"">' + (row.data().sendingEntity || '') + '</td></tr>';
                        break;
                    case "ReceivingEntity":
                        html += '<tr><th style="width: 15%;padding:5px">' + Resources.ReceivingEntity + ':</th><td style="width: 85%;padding:5px;word-break: break-all;"">' + (row.data().receivingEntity || '') + '</td></tr>';
                        break;
                    case "Purpose":
                        var purpose = "";
                        var purposes = new CoreComponents.Lookup.Purposes().get(window.language);
                        for (let i = 0; i < purposes.length; i++)
                        {
                            if (purposes[i].id === row.data().purposeId)
                            {
                                purpose = purposes[i].text;
                            }
                        }
                        html += '<tr><th style="width: 15%;padding:5px">' + Resources.Purpose + ':</th><td style="width: 85%;padding:5px;word-break: break-all;"">' + purpose + '</td></tr>';
                        break;
                    case "Priority":
                        var priority = "";
                        var priorities = new CoreComponents.Lookup.Priorities().get(window.language);
                        for (let i = 0; i < priorities.length; i++)
                        {
                            if (priorities[i].id === row.data().priorityId)
                            {
                                priority = priorities[i].text;
                            }
                        }
                        html += '<tr><th style="width: 15%;padding:5px">' + Resources.Priority + ':</th><td style="width: 85%;padding:5px;word-break: break-all;"">' + priority + '</td></tr>';
                        break;
                    case "Privacy":
                        var privacy = "";
                        var privacies = new CoreComponents.Lookup.Privacies().get(window.language);
                        for (let i = 0; i < privacies.length; i++)
                        {
                            if (privacies[i].id === row.data().privacyId)
                            {
                                privacy = privacies[i].text;
                            }
                        }
                        html += '<tr><th style="width: 15%;padding:5px">' + Resources.Privacy + ':</th><td style="width: 85%;padding:5px;word-break: break-all;"">' + privacy + '</td></tr>';
                        break;
                    case "CreatedBy":
                        html += '<tr><th style="width: 15%;padding:5px">' + Resources.CreatedBy + ':</th><td style="width: 85%;padding:5px;word-break: break-all;"">' + (row.data().createdByUser || '') + '</td></tr>';
                        break;
                    case "OpenedDate":
                        html += '<tr><th style="width: 15%;padding:5px">' + Resources.OpenedDate + ':</th><td style="width: 85%;padding:5px;word-break: break-all;"">' + (DateConverter.toHijriFormated(row.data().openedDate, ConversionDateFormat.DateTimeFormat24_Slash, window.CalendarType) || '') + '</td></tr>';
                        break;
                    case "DueDate":
                        html += '<tr><th style="width: 15%;padding:5px">' + Resources.DueDate + ':</th><td style="width: 85%;padding:5px;word-break: break-all;"">' + (DateConverter.toHijriFormated(row.data().dueDate, ConversionDateFormat.DateTimeFormat24_Slash, window.CalendarType) || '') + '</td></tr>';
                        break;
                    case "DocumentDate":
                        html += '<tr><th style="width: 15%;padding:5px">' + Resources.DocumentDate + ':</th><td style="width: 85%;padding:5px;word-break: break-all;"">' + (row.data().documentDate || '') + '</td></tr>';
                        break;

                }
            }
        }
    }
    return html;
}
function sendToReceivingEntity(receivingEntities, broadcastIds, delegationId, transferToType, dueDate)
{
    let modalWrapper = $(".modal-window");
    let modelIndex = new SendToReceivingEntityIndex.SendToReceivingEntityIndex();
    modelIndex.purposes = new Helper().getPurpose();
    modelIndex.receivingEntities = receivingEntities;
    modelIndex.transferToType = transferToType;
    modelIndex.customAttributeDueDate = dueDate;
    modelIndex.broadcastIds = broadcastIds;
    modelIndex.isBroadcast = true;
    let sendToReceivingEntityIndexView = new SendToReceivingEntityIndex.SendToReceivingEntityIndexView(modalWrapper, modelIndex);
    sendToReceivingEntityIndexView.render();
    $("#modalSendToReceivingEntity").off("shown.bs.modal");
    $('#modalSendToReceivingEntity').on('shown.bs.modal', function ()
    {
        $("#hdSendDeligationId").val(delegationId);
        CKEDITOR.instances.txtAreaInstruction.focus();
    });
}

function formatChild(self, model, buttons, parentRow) {
    var parentData = parentRow.data();
    var childrenData = datatableParentsAndChildren
        .filter(function (item) {
            return item.parent.id === parentData.id;
        }).flatMap(function (item) {
            return item.children;
        });
    var securityMatrix = JSON.parse(new CoreComponents.Lookup.SecurityMatrix().get(window.language));// JSON.parse(sessionStorage.getItem("SecurityMatrix"));
    //var Buttons = SecurityMatrix.getToolbarActions(securityMatrix, self.model.nodeId, gTableName);
    var childrenColumns = [
        {
            visible: buttons.length > 0,
            title: '',
            width: '16px',
            "orderable": false,
            "render": function (data, type, row) {
                return !_isFollowUpNode ? "<input type='checkbox' onclick='event.stopPropagation();' data-id=" + row.id + " />" : "<input type='checkbox' parent=" + row.documentId + " data-id=" + row.id + " checked/>";
            }
        },
        { title: "Id", data: "id", visible: false, "orderable": false }];
    buildColumns(childrenColumns, self.model.nodeId, true)
    childrenColumns.push({
        "className": "text-right",
        "autoWidth": false,
        "bAutoWidth": false,
        width: "16px",
        'orderable': false,
        'sortable': false,
        'render': function (data, type, full, meta) {
            var html = "";
            var color = "";
            var privacies = new CoreComponents.Lookup.Privacies().get(window.language);

                for (var i = 0; i < privacies.length; i++) {
                    if (privacies[i].id === full.privacyId) {
                        color = privacies[i].color;
                        html += "<div class='mr-sm' title='" + Resources.Privacy + "' style='height: 24px'>" +
                            "<i class='fa fa-shield fa-lg' style='color: " + color + "'></i>&nbsp;" +
                            "</div>";
                    }
                }
       
         
            if (full.isOverDue) {
                html += "<div class='mr-sm' title='" + Resources.OverDue + "'><i class='fa fa-clock-o fa-lg text-danger'></i></div>&nbsp;";
            }
           
            if (full.isRead) {
                html += "<div class='mr-sm' title='" + Resources.Read + "' style='height: 24px'><i class='fa fa-envelope-open-o text-info'></i></div>&nbsp;";
            }


            
            if (!_isFollowUpNode) {
                if (!full.sentToUser || (full.sentToUser && model.fromStructureInbox)) {
                    //var delegatedUserIds = $("#hdDelegatedUserIds").val().split(window.Splitter);
                    var delegatedUser = model.delegationId !== null ? new DelegationUsers().getById(Number(model.delegationId)) : null;  //$("#hdDelegatedUserIds").val().split(window.Splitter);
                    var delegatedUserId = delegatedUser !== null ? delegatedUser.fromUserId : 0;
                    if (full.isLocked && (full.ownerUserId !== null && full.ownerUserId !== Number($("#hdUserId").val()) && model.delegationId === null)
                        || (full.ownerDelegatedUserId !== null && full.ownerDelegatedUserId !== Number($("#hdUserId").val()) && model.delegationId !== null)
                        || (full.ownerUserId !== null && delegatedUserId !== full.ownerUserId && model.delegationId !== null)) {
                        var lockedBy = full.lockedByDelegatedUser !== '' ? full.lockedByDelegatedUser + " " + Resources.OnBehalfOf + " " + full.lockedBy : full.lockedBy;
                        var titleLock = Resources.LockedBy + ": " + lockedBy + "\n" + Resources.LockedDate + ": " + DateConverter.toHijriFormated(full.lockedDate, ConversionDateFormat.DateTimeFormat24_Slash, window.CalendarType);
                        html += "<div class='mr-sm text-left infoDivIcon' title=''><i class='fa fa-lock fa-lg text-danger' title='" + titleLock + "'></i><div class='infoDiv font-13' style='opacity: 0;'>" +
                            titleLock + "</div></div>&nbsp;";
                    }
                }
                let category = $.grep(self.model.categories, function (e) {
                    return e.id === full.categoryId;
                });
                if (category[0] && category[0].isBroadcast) {
                    html += "<div class='mr-sm' title='" + Resources.Broadcast + "'><i class='fa fa-bullhorn text-primary'></i></div>&nbsp;";
                } else if (full.cced) {
                    html += "<div class='mr-sm' title='" + Resources.CarbonCopy + "'><i class='fa fa-cc text-warning'></i></div>&nbsp;";
                }
                if (full.sentToUser) {
                    html += "<div class='mr-sm' title='" + Resources.SentToUser + "'><i class='icon-user text-success'></i></div>&nbsp;";
                }
                if (full.sentToStructure) {
                    html += "<div class='mr-sm' title='" + Resources.SentToStructure + "'><i class='fa fa-building-o text-primary'></i></div>&nbsp;";
                }
            } else {
                var requestToCompleteStatus = new CoreComponents.Lookup.Statuses().findById(full.transferStatusId, 'en');
                if (requestToCompleteStatus) {
                    if (requestToCompleteStatus.text.replace(/\s/g, '').toLowerCase() === "requesttocomplete") {
                        //if (requestToCompleteStatus.id === 7) {
                        html += "<div class='mr-sm' title='" + Resources.RequestToComplete + "'><i class='fa fa-check-square-o mr-sm text-info'></i></div>&nbsp;";
                    }
                }
            }

            if (full.hasAttachment) {
                html += "<div class='mr-sm' title='" + (full.attachmentCount != null ? full.attachmentCount.toString() : "0") + " " + Resources.Attachments + "'><i class='fa fa-paperclip text-info'></i></div>&nbsp;";
            }

            return "<div id='divLock" + full.id + "' style='display: inline-flex;align-items: center'>" + html + "</div>";
        }
    });
    childrenColumns.push({
        "className": "text-right",
        "autoWidth": false,
        "bAutoWidth": false,
        width: "16px",
        'orderable': false,
        'sortable': false,
        'render': function (data, type, full, meta) {
            var html = "";
            var lockedByMe = false;
            //var delegatedUserIds = $("#hdDelegatedUserIds").val().split(window.Splitter);
            var delegatedUser = model.delegationId !== null ? new DelegationUsers().getById(Number(model.delegationId)) : null;  //$("#hdDelegatedUserIds").val().split(window.Splitter);
            var delegatedUserId = delegatedUser !== null ? delegatedUser.fromUserId : 0;
            if (full.isLocked && (full.ownerUserId !== null && full.ownerUserId === Number($("#hdUserId").val()) && model.delegationId === null)
                || (full.ownerDelegatedUserId !== null && full.ownerDelegatedUserId === Number($("#hdUserId").val())
                    && delegatedUserId === full.ownerUserId && model.delegationId !== null)
                || (full.ownerUserId !== null && delegatedUserId === full.ownerUserId && model.delegationId !== null)) {
                lockedByMe = true;
            }
            
            if ((((!lockedByMe && !full.sentToUser) || full.cced) /*&& !self.model.isExported*/) || self.model.fromStructureInbox) {
                let btnView = document.createElement("button");
                if ((lockedByMe || full.ownerUserId === null) && !full.cced) {
                    btnView.setAttribute("class", "btn btn-xs btn-warning mr-sm view");
                } else {
                    btnView.setAttribute("class", "btn btn-xs btn-warning  view");
                }

                btnView.setAttribute("title", Resources.View);
                btnView.setAttribute("clickattr", "openDocumentViewMode(" + full.id + "," + (full.openedDate !== "") + "," + model.delegationId + "," + full.cced + ", " + self.model.nodeId + ")");
                btnView.innerHTML = "<i class='fa fa-eye fa-white'/>";
                html += btnView.outerHTML;
            }
            //if (self.model.isExported) {
            //    let btnView = document.createElement("button");
            //    btnView.setAttribute("class", "btn btn-xs btn-warning mr-sm view")
            //    btnView.setAttribute("title", Resources.View);
            //    btnView.setAttribute("clickattr", "openDocumentViewMode(" + full.id + "," + (full.openedDate !== "") + "," + model.delegationId + "," + full.cced + ", " + self.model.nodeId + ")");
            //    btnView.innerHTML = "<i class='fa fa-eye fa-white'/>";
            //    html += btnView.outerHTML;
            //}

            if ((!full.sentToUser || (full.sentToUser && self.model.fromStructureInbox)) && lockedByMe && !self.model.isExported) {
                var lockedBy = '';
                if (full.ownerDelegatedUserId !== null) {
                    if (full.ownerDelegatedUserId === Number($("#hdUserId").val())) {
                        lockedBy = Resources.You + " " + Resources.OnBehalfOf + " " + full.lockedBy;
                    } else {
                        lockedBy = full.lockedByDelegatedUser + " " + Resources.OnBehalfOf + " " + Resources.You;
                    }
                } else {
                    if (full.ownerUserId === Number($("#hdUserId").val())) {
                        lockedBy = Resources.You;
                    } else {
                        lockedBy = full.lockedBy;
                    }
                }
                var title = Resources.LockedBy + ": " + lockedBy + "</br>" + Resources.LockedDate + ": " + DateConverter.toHijriFormated(full.lockedDate, ConversionDateFormat.DateTimeFormat24_Slash, window.CalendarType);
                let btnLock = document.createElement("button");
                btnLock.setAttribute("id", 'btnUnLock');
                btnLock.setAttribute("title", '');
                btnLock.setAttribute("class", "btn btn-xs btn-success mr-sm unlock infoDivIcon");
                btnLock.innerHTML = "<i class='fa fa-unlock fa-white'></i><div class='infoDiv font-13 text-left' style='opacity: 0;'>" + title + "</div>";
                btnLock.setAttribute("clickattr", "unlock(this, " + full.id + " , " + model.delegationId + " , " + self.model.nodeId + ", self)");
                html += btnLock.outerHTML;
            }
            if (((lockedByMe || full.ownerUserId === null) && !full.cced && !self.model.isExported) || (self.model.fromStructureInbox && (lockedByMe || full.ownerUserId === null) )) {
                let btn = document.createElement("button");
                btn.setAttribute("class", "btn btn-xs btn-primary edit");
                btn.setAttribute("title", Resources.Edit);
                btn.setAttribute("clickattr", "openDocumentEditMode(" + full.id + "," + lockedByMe + "," + full.sentToUser + "," + model.delegationId + ", " + self.model.nodeId + " , " + self.model.fromStructureInbox + " , " + full.isLocked + ")");
                btn.innerHTML = "<i class='fa fa-edit fa-white'/>";
                html += btn.outerHTML;
            }
           
            if (self.model.isExported) {
                let btn = document.createElement("button");
                btn.setAttribute("class", "btn btn-xs btn-primary mr-sm accept");
                btn.setAttribute("title", Resources.Receive);
                btn.setAttribute("clickattr", "acceptRequest(" + full.id + "," + model.delegationId + ")");
                btn.innerHTML = "<i class='fa fa-check'/>";
                html += btn.outerHTML;
            }
            if (self.model.isExported) {
                let btn = document.createElement("button");
                btn.setAttribute("class", "btn btn-xs btn-danger reject");
                btn.setAttribute("title", Resources.Return);
                btn.setAttribute("clickattr", "rejectRequest(" + full.id + "," + model.delegationId + ")");
                btn.innerHTML = "<i class='fa fa-close'/>";
                html += btn.outerHTML;
            }
            if (!_isFollowUpNode && !self.model.isExported)
            {
                const visualtrackingBtn = document.createElement("button");
                visualtrackingBtn.setAttribute("class", "btn btn-xs btn-success ml-sm visualtracking");
                visualtrackingBtn.setAttribute("title", Resources.ViewVisualTracking);
                visualtrackingBtn.setAttribute("clickattr", "VisualTracking.openVisualTracking(" + full.documentId + "," + model.delegationId + ")");
                visualtrackingBtn.innerHTML = "<i class='fa fa-sitemap fa-white'/>";
                html += visualtrackingBtn.outerHTML;
            }
            return "<div style='display: inline-flex;'>" + html + "</div>";
        }
    });

    SecurityMatrix.getRowActions(securityMatrix, childrenColumns, self.model.nodeId);
    var childTable = $('<table id="childTable_' + parentData.id + '" class="table table-striped table-hover followUpTasks" style="border: 1px solid black;width: 100%;"></table>');

    //childTable.DataTable({
    //    data: childrenData,
    //    columns: childrenColumns,
    //    paging: false,
    //    searching: false,
    //    info: false

    //});
    childTable.DataTable({
        "createdRow": function (row, data, dataIndex) {
            
            if (!data.isRead) {
                $(row).css('font-weight', 'bold');
            }
            $(row).css('cursor', 'pointer');
        },
        data: childrenData,
        columns: childrenColumns,
        paging: false,
        searching: false,
        info: false

    });
    childTable.on('click', 'td.details-control', function (event) {
        ;
        event.stopPropagation(); // Stop event propagation to parent table
        var tr = $(this).closest('tr');
        var row = childTable.DataTable().row(tr);
        if (row.child.isShown()) {
            row.child.hide();
            tr.removeClass('shown');
        } else {
            row.child(format(row, self.model.nodeId)).show();
            tr.addClass('shown');
        }
    });

    childTable.find('tbody').find('.edit,.view,.unlock,.visualtracking,.accept ,.reject').on('click', function () {
        var onclick = $(this).attr("clickattr");
        eval(onclick);
    });
    childTable.find('tbody').on('dblclick', 'tr', function (event) {
        if (!gLocked) {
            gLocked = true;
            try {
                var onclick = $(this).find(".edit").attr("clickattr");
                if (!onclick) {
                    onclick = $(this).find(".view").attr("clickattr");
                }
                eval(onclick);
            } catch (e) {
                gLocked = false;
            }
        }
    });

    childTable.find('tbody').find('tr').on('click', function (event) {
            event.stopPropagation(); // Stop event propagation to parent table

        if ($(event.target).is('td.details-control')) {
            var tr = $(this).closest('tr');
            var row = childTable.DataTable().row(tr);
            if (row.child.isShown()) {
                row.child.hide();
                tr.removeClass('shown');
            } else {
                row.child(format(row, self.model.nodeId)).show();
                tr.addClass('shown');
            }


        }
        else {
            var tr = $(this)[0];
            var data = childTable.DataTable().row(tr).data();
            if ($('[data-id=' + data.id + ']').prop('checked')) {

                $('[data-id=' + data.id + ']').prop("checked", false);
                $('[data-id=' + data.id + ']').trigger("change");

            }
            else {
                $('[data-id=' + data.id + ']').prop("checked", true);
                $('[data-id=' + data.id + ']').trigger("change");
            }
        }
    });

    childTable.on('click', 'td.followUpTask', function (event) {
        event.stopPropagation();
        let tr = $(this).closest('tr');
        var data = childTable.DataTable().row(tr).data();
        if ($('[data-id=' + data.id + ']').prop('checked')) {
            var isAllChecked = 0;
            $('[parent=' + data.id + ']').each(function () {
                if (!this.checked)
                    isAllChecked = 1;
            });
            if (isAllChecked == 0) {
                $('[data-id=' + data.id + ']').prop("checked", true);
                $('[data-id=' + data.id + ']').trigger("change");
            }
        }
        else {
            $('[data-id=' + data.id + ']').prop("checked", false);
            $('[data-id=' + data.id + ']').trigger("change");
        }
    });
    return childTable
}

var gTableName = "grdInboxItems";
var transferComponent;
var gLocked = false;
var dataTableParents = [];
var datatableParentsAndChildren = [];
//var IsInboxModeWithGrouping;

var IsInboxModeWithGrouping = window.InboxMode === "InboxDefaultWithGrouping";
var _isFollowUpNode = false;
var wrapperParent;
var gfirstTime = true;
var gDataChanged = true;
var gAction = "";
var gFilterChanged = false;
var gFilterStructure = "";
var gFilterFromDate = "";
var gFilterToDate = "";
var gFilterUser = "";
var gFilterOverdue = "";
var gOverdue = false;
var clear = false;
var fromStructureInbox = false;
var gReportName = "Reports Inbox ";
class DocumentInboxView extends Intalio.View
{
    constructor(element, model)
    {
        wrapperParent = model;

        super(element, "inbox", model);
    }
    render()
{
        
    //    Common.ajaxGet('/UserParameter/Get', { "Keyword": "InboxMode" },
    //        function (data) {
    //            console.log(data);
    //            if (data != undefined && data != null) {
    //                window.InboxMode = data.content;
    //                IsInboxModeWithGrouping = window.InboxMode === "InboxDefaultWithGrouping";
    //            }

    //        }, null, false);

        var self = this;
        var model = this.model;
      

        fromStructureInbox = model.fromStructureInbox;
        if (self.model.nodeId == window.AssignedToMeNode)
            $('.content-heading').text(Resources.AssignedToMe);

        if (fromStructureInbox) {
            $("#liStructureInbox" + model.nodeId).off("click")
            $("#liStructureInbox" + model.nodeId).on("click", function () {
                GridCommon.Refresh(gTableName);
            });
        } else {

            $("#liInbox" + model.nodeId).off("click")
            $("#liInbox" + model.nodeId).on("click", function () {
                GridCommon.Refresh(gTableName);
            });
        }




        IsInboxModeWithGrouping = window.InboxMode === "InboxDefaultWithGrouping";
        //IsInboxModeWithGrouping = model.IsInboxModeWithGrouping;
        var categoryModels = new CategoryModel().getFullCategories();
        if (categoryModels.length > 0)
        {
            for (var i = 0; i < categoryModels.length; i++)
            {
                if (categoryModels[i].basicAttribute !== "" && categoryModels[i].basicAttribute !== null)
                {
                    let basicAttributes = JSON.parse(categoryModels[i].basicAttribute);
                    if (basicAttributes.length > 0)
                    {
                        let receivingEntityObj = $.grep(basicAttributes, function (e)
                        {
                            return e.Name === "ReceivingEntity";
                        });
                        if (receivingEntityObj[0].BroadcastReceivingEntity)
                        {
                            for (var j = 0; j < self.model.categories.length; j++)
                            {
                                if (self.model.categories[j].id == categoryModels[i].id)
                                {
                                    self.model.categories[j].isBroadcast = true;
                                    self.model.categories[j].isInternalBroadcast = receivingEntityObj[0].Type === "internal";
                                }
                            }
                        }
                    }
                }
            }
        }
        _isFollowUpNode = isFollowUpNode(self.model.nodeId);
        $.fn.select2.defaults.set("theme", "bootstrap");
        var followUpCategoryIndex = self.model.categories.findIndex(item => item.id == window.FollowUpCategory);
        self.model.categories.splice(followUpCategoryIndex, 1);
        buildFilters(self.model.nodeId, self.model.categories);
        Common.gridCommon();

        var reportTitle;

        var nodes = new CoreComponents.Lookup.Nodes().get(window.language);
        var currentNode = $.grep(nodes, function (element, index) {
            return element.id.toString() === self.model.nodeId;
        })[0];
        if (currentNode)
            reportTitle = currentNode.text + (currentNode.parentName ? '_' + currentNode.parentName : '');


        $('.toggleVIP').on('click', function () {
            if (window.InboxMode ==="InboxDefault") {
                window.InboxMode = "LocalVIPView";
            } else if (window.InboxMode === "LocalInboxDefaultView") {
                window.InboxMode = "InboxVIPView";
            }
            
            let wrapper = $(".content-wrapper");
            let VIPmodel = new VipDocumentInbox.VipDocumentInbox();
            VIPmodel.fromStructureInbox = fromStructureInbox;
            VIPmodel.nodeId = wrapperParent.nodeId;
            VIPmodel.delegationId = wrapperParent.delegationId;
            VIPmodel.categories = new Categories().get(window.language, wrapperParent.delegationId);
            VIPmodel.privacies = new CoreComponents.Lookup.Privacies().get(window.language);
            VIPmodel.title = $('.content-heading').text();
            VIPmodel.isExported = wrapperParent.isExported;
            let documentView = new VipDocumentInbox.VipDocumentInboxView(wrapper, VIPmodel);
            documentView.render();     
        })
        let categoriesIds = undefined;
        for (var i = 0; i < model.categories.length; i++) {
            if (i == 0) {
                categoriesIds = model.categories[i].id;

            }
            else {
                categoriesIds += "_" + model.categories[i].id;
            }

        }
        var securityMatrix = JSON.parse(new CoreComponents.Lookup.SecurityMatrix().get(window.language));// JSON.parse(sessionStorage.getItem("SecurityMatrix"));
        for (var i = 0; i < 7; i++) {
            

            for (var j = 0; j < securityMatrix.SecurityNodes[i+1].Actions?.length; j++) {
                if (securityMatrix.SecurityNodes[i + 1].Actions[j].Name == "DismissCustom") {
                    securityMatrix.SecurityNodes[i + 1].Actions[j].JsFunction = securityMatrix.SecurityNodes[i + 1].Actions[j]. JsFunction 
                        .replace("$delegationId", model.delegationId)
                        .replace("$categories", categoriesIds)
                }
            }

        }
        //var buttons = SecurityMatrix.getToolbarActions(securityMatrix, self.model.nodeId, gTableName);
        var buttons = SecurityMatrix.getToolbarActions(securityMatrix, self.model.nodeId, gTableName).map(button => {
            button.className = (button.className || '') + ' hidden conditional-buttons';
            return button;
        });
    


        var exportButton = [{
            className: 'btn-sm btn-primary',

            extend: 'print',
            text: Resources.Print,
            title: function () {
                return Resources.reportInbox;
            },
            filename: function () {
                var currentDate = new Date();

                var formattedDate = currentDate.toISOString().split('T')[0];

                var formattedTime = currentDate.toTimeString().split(' ')[0].replace(/:/g, '-');
                
                return Resources.reportInbox + '_' + formattedDate + '_' + formattedTime;
            },

            exportOptions: {
                columns: [ ':visible',2,3]

            },
            customize: CTSReportDatatblesCustomComponents.CustomDatatableReport.reportInbox.print
        },
        {
            className: 'btn-sm btn-primary',

            extend: 'excelHtml5',
            title: function () {
               
                return Resources.reportInbox;;
            },
            filename: function () {
                var currentDate = new Date();

                var formattedDate = currentDate.toISOString().split('T')[0];

                var formattedTime = currentDate.toTimeString().split(' ')[0].replace(/:/g, '-');

              

                //if (self.model.nodeId == '21')
                //    reportTitle = Resources.PersonalInbox;
                //else if (self.model.nodeId == '46')
                //    reportTitle = Resources.InternalPersonalInbox;
                //else if (self.model.nodeId == '33')
                //    reportTitle = Resources.IncomingPersonalInbox;
                //else if (self.model.nodeId == '38')
                //    reportTitle = Resources.OutgoingPersonalInbox;
                //else if (self.model.nodeId == '31')
                //    reportTitle = Resources.ReadyForTransferReport;
                //else if (self.model.nodeId == '36')
                //    reportTitle = Resources.AcceptReject;
                //else if (self.model.nodeId == '40')
                //    reportTitle = Resources.ReadyToExportReport;
                //else if (self.model.nodeId == '41')
                //    reportTitle = Resources.StructureReadyForExport;
                //else if (self.model.nodeId == '22')
                //    reportTitle = Resources.DepartmentInbox;
                //else if (self.model.nodeId == '47')
                //    reportTitle = Resources.InternalDepartmentInbox;
                //else if (self.model.nodeId == '34')
                //    reportTitle = Resources.IncomingDepartmentInbox;
                //else if (self.model.nodeId == '39')
                //    reportTitle = Resources.OutgoingDepartmentInbox;
                

                return reportTitle + '_' + formattedDate + '_' + formattedTime;
            },


            exportOptions: {
                columns: [':visible',2, 3]

            },
            customize: CTSReportDatatblesCustomComponents.CustomDatatableReport.reportInbox.excelHTML5
        },
            {
                className: 'btn-sm btn-primary',

            extend: 'pdfHtml5',
            title: function () {
                //return Resources.reportInbox;
                //var reportTitle;

                //if (self.model.nodeId == '21')
                //    reportTitle = Resources.PersonalInbox;
                //else if (self.model.nodeId == '46')
                //    reportTitle = Resources.InternalPersonalInbox;
                //else if (self.model.nodeId == '33')
                //    reportTitle = Resources.IncomingPersonalInbox;
                //else if (self.model.nodeId == '38')
                //    reportTitle = Resources.OutgoingPersonalInbox;
                //else if (self.model.nodeId == '31')
                //    reportTitle = Resources.ReadyForTransferReport;
                //else if (self.model.nodeId == '36')
                //    reportTitle = Resources.AcceptReject;
                //else if (self.model.nodeId == '40')
                //    reportTitle = Resources.ReadyToExportReport;
                //else if (self.model.nodeId == '41')
                //    reportTitle = Resources.StructureReadyForExport;
                //else if (self.model.nodeId == '22')
                //    reportTitle = Resources.DepartmentInbox;
                //else if (self.model.nodeId == '47')
                //    reportTitle = Resources.InternalDepartmentInbox;
                //else if (self.model.nodeId == '34')
                //    reportTitle = Resources.IncomingDepartmentInbox;
                //else if (self.model.nodeId == '39')
                //    reportTitle = Resources.OutgoingDepartmentInbox;
                //else
                //    reportTitle = Resources.reportInbox

                return reportTitle;
            },
            filename: function () {
                var currentDate = new Date();

                var formattedDate = currentDate.toISOString().split('T')[0];

                var formattedTime = currentDate.toTimeString().split(' ')[0].replace(/:/g, '-');

                //var reportTitle;

                //if (self.model.nodeId == '21')
                //    reportTitle = Resources.PersonalInbox;
                //else if (self.model.nodeId == '46')
                //    reportTitle = Resources.InternalPersonalInbox;
                //else if (self.model.nodeId == '33')
                //    reportTitle = Resources.IncomingPersonalInbox;
                //else if (self.model.nodeId == '38')
                //    reportTitle = Resources.OutgoingPersonalInbox;
                //else if (self.model.nodeId == '31')
                //    reportTitle = Resources.ReadyForTransferReport;
                //else if (self.model.nodeId == '36')
                //    reportTitle = Resources.AcceptReject;
                //else if (self.model.nodeId == '40')
                //    reportTitle = Resources.ReadyToExportReport;
                //else if (self.model.nodeId == '41')
                //    reportTitle = Resources.StructureReadyForExport;
                //else if (self.model.nodeId == '22')
                //    reportTitle = Resources.DepartmentInbox;
                //else if (self.model.nodeId == '47')
                //    reportTitle = Resources.InternalDepartmentInbox;
                //else if (self.model.nodeId == '34')
                //    reportTitle = Resources.IncomingDepartmentInbox;
                //else if (self.model.nodeId == '39')
                //    reportTitle = Resources.OutgoingDepartmentInbox;
                //else
                //    reportTitle = Resources.reportInbox
                return reportTitle + '_' + formattedDate + '_' + formattedTime;
            },
           
            exportOptions: {
                columns: [':visible',2,3] 
            },
            customize: CTSReportDatatblesCustomComponents.CustomDatatableReport.reportInbox.pdfHTML5
            }, {
                className: 'btn-sm btn-primary',
                text: Resources.CustomizeColumns,
                action: function (e, dt, node, config) {

                    var wrapper = $(".modal-window");
                    var customeModel = new CustomizeNodeColomns.CustomizeNodeColomns();
                    customeModel.nodeId = model.nodeId;
                    customeModel.text = "Inbox";
                    var CustomizeNodeColomnsViews = new CustomizeNodeColomns.CustomizeNodeColomnsView(wrapper, customeModel);
                    CustomizeNodeColomnsViews.render();

                    $("#nodeColomnsModal").parsley().reset();

                    $('#nodeColomnsModal').modal('show');

                    $("#nodeColomnsModal").off("hidden.bs.modal");
                    $("#nodeColomnsModal").off("shown.bs.modal");

                    $('#nodeColomnsModal').on('shown.bs.modal', function () {
                    });

                    $('#nodeColomnsModal').on('hidden.bs.modal', function () {
                        $('#formPostNode').parsley().reset();
                        $('#nodeColomnsModal').remove();
                    });
                }
            }
        ];

        var allButtons = [exportButton, ...buttons];

        var columns = [
            {
                title: "", visible: true, width: '0px', "orderable": false,
                "render": function (data, type, row) {
                    return "<div class='hidden'  data-id=" + row.id + " />";

                }
            },
            {
            visible: buttons.length > 0, title: '<input id="chkAll" type="checkbox" />', width: '16px', "orderable": false,
            "render": function (data, type, row) {
                if (!IsInboxModeWithGrouping) {

                    return "<input type='checkbox' onclick='event.stopPropagation();' data-id=" + row.id + " />";
                } else {
                    return null;
                }
            }
        },{ title: "Id", data: "id", visible: false, "orderable": false }];


        buildColumns(columns, self.model.nodeId, false);
        //if (self.model.isExported) {
        //    columns.push({
        //        visible: true,
        //        title: Resources.Type,
        //        data: "",
        //        render: function (data, type, full, meta) {
        //            if (full.document.documentReceiverEntity[0].exportIsOriginal == true || full.document.documentReceiverEntity[0].exportIsOriginal == null) {

        //                return Resources.OriginalExported;
        //            }
        //            else {

        //                return Resources.CcedExported;
        //            }


        //        }
        //    });
        //}
        
        columns.push({
            'visible': !IsInboxModeWithGrouping,
            "className": "text-right",
            "autoWidth": false,
            "bAutoWidth": false,
            width: "16px",
            'orderable': false,
            'sortable': false,
            'render': function (data, type, full, meta)
            {
                var html = "";
                var text = "";
                var color = "";


                if (self.model.isExported) {
                    if (full.document.documentReceiverEntity[0].exportIsOriginal == false || full.document.documentReceiverEntity[0].exportIsOriginal == null) {
                        html += "<div class='mr-sm' title='" + Resources.CarbonCopy + "'><i class='fa fa-cc text-warning'></i></div>";
                    }
                }

                var privacies = new CoreComponents.Lookup.Privacies().get(window.language);
                    for (var i = 0; i < privacies.length; i++) {
                        if (privacies[i].id === full.privacyId) {
                            color = privacies[i].color;
                            text = Resources.Privacy + ": " + privacies[i].text;
                            html += "<div class='mr-sm' title='" + text + "' style='height: 24px'>" +
                                "<i class='fa fa-shield fa-lg' style='color: " + color + "'></i>&nbsp;" +
                                "</div>";
                            break;
                        }
                    }
           
             
                if (full.isOverDue)
                {
                    html += "<div class='mr-sm' title='" + Resources.OverDue + "'><i class='fa fa-clock-o fa-lg text-danger'></i></div>&nbsp;";
                }
                if (full.isRead) {
                    html += "<div class='mr-sm' title='" + Resources.Read + "' style='height: 24px'>" +
                        "<i class='fa fa-envelope-open-o text-info'></i>&nbsp;" +
                        "</div>";
                    /*TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);*/
                }
                var categories = new Categories().get(window.language);
                var matchedCategory , categoryName;
                
                if (full.categoryName == "Incoming") {
                    matchedCategory = categories.find(c => c.id === full.categoryId);

                    categoryName = matchedCategory ? matchedCategory.text : "";

                    html += "<div class='mr-sm' title='" + categoryName + "' style='height: 24px; color: black'>" +
                        "<i class=' fa fa fa-sign-in '></i>&nbsp;" +
                        "</div>";
                }
                else if (full.categoryName == "Outgoing") {
                    matchedCategory = categories.find(c => c.id === full.categoryId);

                    categoryName = matchedCategory ? matchedCategory.text : "";

                    html += "<div class='mr-sm' title='" + categoryName + "' style='height: 24px ; color: black'>" +
                        "<i class=' fa fa-share-square-o '></i>&nbsp;" +
                        "</div>";
                } else {
                    matchedCategory = categories.find(c => c.id === full.categoryId);

                    categoryName = matchedCategory ? matchedCategory.text : "";

                    html += "<div class='mr-sm' title='" + categoryName + "' style='height: 24px; color: black'>" +
                        "<i class=' fa fa fa-inbox '></i>&nbsp;" +
                        "</div>";
                }
                
            
                      
               
               


                if (!full.sentToUser || (full.sentToUser && model.fromStructureInbox)) 
                {
                    //var delegatedUserIds = $("#hdDelegatedUserIds").val().split(window.Splitter);
                    var delegatedUser = model.delegationId !== null ? new DelegationUsers().getById(Number(model.delegationId)) : null;  //$("#hdDelegatedUserIds").val().split(window.Splitter);
                    var delegatedUserId = delegatedUser !== null ? delegatedUser.fromUserId : 0;
                    if (full.isLocked && (full.ownerUserId !== null && full.ownerUserId !== Number($("#hdUserId").val()) && model.delegationId === null)
                        || (full.ownerDelegatedUserId !== null && full.ownerDelegatedUserId !== Number($("#hdUserId").val()) && model.delegationId !== null)
                        || (full.ownerUserId !== null && delegatedUserId !== full.ownerUserId && model.delegationId !== null))
                    {
                        var lockedBy = full.lockedByDelegatedUser !== '' ? full.lockedByDelegatedUser + " " + Resources.OnBehalfOf + " " + full.lockedBy : full.lockedBy;
                        var titleLock = Resources.LockedBy + ": " + lockedBy + "\n" + Resources.LockedDate + ": " + DateConverter.toHijriFormated(full.lockedDate, ConversionDateFormat.DateTimeFormat24_Slash, window.CalendarType);
                        html += "<div class='mr-sm text-left infoDivIcon' title=''><i class='fa fa-lock fa-lg text-danger' title='" + titleLock + "'></i><div class='infoDiv font-13' style='opacity: 0;'>" +
                            titleLock + "</div></div>&nbsp;";
                    }
                }
                let category = $.grep(self.model.categories, function (e)
                {
                    return e.id === full.categoryId;
                });
                if (category[0] && category[0].isBroadcast)
                {
                    html += "<div class='mr-sm' title='" + Resources.Broadcast + "'><i class='fa fa-bullhorn text-primary'></i></div>&nbsp;";
                } else if (full.cced)
                {
                    html += "<div class='mr-sm' title='" + Resources.CarbonCopy + "'><i class='fa fa-cc text-warning'></i></div>&nbsp;";
                }
                if (full.sentToUser)
                {
                    html += "<div class='mr-sm' title='" + Resources.SentToUser + "'><i class='icon-user text-success'></i></div>&nbsp;";
                }
                if (full.sentToStructure)
                {
                    html += "<div class='mr-sm' title='" + Resources.SentToStructure + "'><i class='fa fa-building-o text-primary'></i></div>&nbsp;";
                }
                if (full.hasAttachment) {
                    html += "<div class='mr-sm' title='" + (full.attachmentCount != null ? full.attachmentCount.toString() : "0") + " " + Resources.Attachments  + "'><i class='fa fa-paperclip text-info'></i></div>&nbsp;";
                }

                return "<div id='divLock" + full.id + "' style='display: inline-flex;align-items: center'>" + html + "</div>";
            }
        });
        columns.push({
            'visible': !IsInboxModeWithGrouping,
            "className": "text-right",
            "autoWidth": false,
            "bAutoWidth": false,
            width: "16px",
            'orderable': false,
            'sortable': false,
            'render': function (data, type, full, meta)
            {

                var html = "";
                var lockedByMe = false;
                //var delegatedUserIds = $("#hdDelegatedUserIds").val().split(window.Splitter);
                var delegatedUser = model.delegationId !== null ? new DelegationUsers().getById(Number(model.delegationId)) : null;  //$("#hdDelegatedUserIds").val().split(window.Splitter);
                var delegatedUserId = delegatedUser !== null ? delegatedUser.fromUserId : 0;
                if (full.isRead && !full.lockedDate) {
                    let readBtn = document.createElement("button");
                    readBtn.setAttribute("title", Resources.UnRead);
                    readBtn.setAttribute("class", "btn btn-xs btn-light mr-sm Readbtn");
                    readBtn.innerHTML = "<i class='fa fa-envelope text-warning'></i>&nbsp;";
                    readBtn.setAttribute("clickattr",
                        "markAsUnread(" + full.id + "," + (full.openedDate !== '') + "," + model.delegationId + "," +
                        full.cced + ", " + self.model.nodeId + "," + (full.lockedDate !== '') + ",'" + full.lockedBy + "')");
                    html += readBtn.outerHTML;

                } 
                if (full.isLocked && (full.ownerUserId !== null && full.ownerUserId === Number($("#hdUserId").val()) && model.delegationId === null)
                    || (full.ownerDelegatedUserId !== null && full.ownerDelegatedUserId === Number($("#hdUserId").val())
                        && delegatedUserId === full.ownerUserId && model.delegationId !== null)
                    || (full.ownerUserId !== null && delegatedUserId === full.ownerUserId && model.delegationId !== null))
                {
                    lockedByMe = true;
                }
                if ((((!lockedByMe && !full.sentToUser) || full.cced) /*&& !self.model.isExported*/) || self.model.fromStructureInbox)
                {
                    let btnView = document.createElement("button");
                    if ((lockedByMe || full.ownerUserId === null) && !full.cced)
                    {
                        btnView.setAttribute("class", "btn btn-xs btn-warning mr-sm view");
                    } else
                    {
                        btnView.setAttribute("class", "btn btn-xs btn-warning view");
                    }
                    btnView.setAttribute("title", Resources.View);
                    btnView.setAttribute("clickattr", "openDocumentViewMode(" + full.id + "," + (full.openedDate !== "") + "," + model.delegationId + "," + full.cced + ", " + self.model.nodeId + ")");
                    btnView.innerHTML = "<i class='fa fa-eye fa-white'/>";
                    html += btnView.outerHTML;
                }

                //if (self.model.isExported) {
                //    let btnView = document.createElement("button");
                //    btnView.setAttribute("class", "btn btn-xs btn-warning mr-sm view")
                //    btnView.setAttribute("title", Resources.View);
                //    btnView.setAttribute("clickattr", "openDocumentViewMode(" + full.id + "," + (full.openedDate !== "") + "," + model.delegationId + "," + full.cced + ", " + self.model.nodeId + "," + true + ")");
                //    btnView.innerHTML = "<i class='fa fa-eye fa-white'/>";
                //    html += btnView.outerHTML;
                //}
                if ((!full.sentToUser || (full.sentToUser && self.model.fromStructureInbox)) && lockedByMe && !self.model.isExported)
                {
                    var lockedBy = '';
                    if (full.ownerDelegatedUserId !== null)
                    {
                        if (full.ownerDelegatedUserId === Number($("#hdUserId").val()))
                        {
                            lockedBy = Resources.You + " " + Resources.OnBehalfOf + " " + full.lockedBy;
                        } else
                        {
                            lockedBy = full.lockedByDelegatedUser + " " + Resources.OnBehalfOf + " " + Resources.You;
                        }
                    } else
                    {
                        if (full.ownerUserId === Number($("#hdUserId").val()))
                        {
                            lockedBy = Resources.You;
                        } else
                        {
                            lockedBy = full.lockedBy;
                        }
                    }
                    var title = Resources.LockedBy + ": " + lockedBy + "</br>" + Resources.LockedDate + ": " +
                        DateConverter.toHijriFormated(full.lockedDate, ConversionDateFormat.DateTimeFormat24_Slash, window.CalendarType);
                    let btnLock = document.createElement("button");
                    btnLock.setAttribute("id", 'btnUnLock');
                    btnLock.setAttribute("title", '');
                    btnLock.setAttribute("class", "btn btn-xs btn-success mr-sm unlock infoDivIcon");
                    btnLock.innerHTML = "<i class='fa fa-unlock fa-white'></i><div class='infoDiv font-13 text-left' style='opacity: 0;'>" + title + "</div>";
                    btnLock.setAttribute("clickattr", "unlock(this , " + full.id + " , " + model.delegationId + " , " + self.model.nodeId + " , self)");

                    html += btnLock.outerHTML;
                }
                let isOwnedOrUnlocked = lockedByMe || full.ownerUserId === null;
                if ((isOwnedOrUnlocked && !full.cced && !self.model.isExported) || (self.model.fromStructureInbox && isOwnedOrUnlocked))
                {
                    let btn = document.createElement("button");
                    btn.setAttribute("class", "btn btn-xs btn-primary edit");
                    btn.setAttribute("title", Resources.Edit);
                    btn.setAttribute("clickattr", "openDocumentEditMode(" + full.id + "," + lockedByMe + "," + full.sentToUser + "," + model.delegationId + ", " + self.model.nodeId + " , " + self.model.fromStructureInbox + " , " + full.isLocked + ")");
                    btn.innerHTML = "<i class='fa fa-edit fa-white'/>";
                    html += btn.outerHTML;
                }
                if (self.model.isExported) {
                    let btn = document.createElement("button");
                    btn.setAttribute("class", "btn btn-xs btn-primary mr-sm accept");
                    btn.setAttribute("title", Resources.Receive);
                    btn.setAttribute("clickattr", "acceptRequest(" + full.id + "," + model.delegationId + ")");
                    btn.innerHTML = "<i class='fa fa-check'/>";
                    html += btn.outerHTML;
                }
                if (self.model.isExported) {
                    let btn = document.createElement("button");
                    btn.setAttribute("class", "btn btn-xs btn-danger reject");
                    btn.setAttribute("title", Resources.Return);
                    btn.setAttribute("clickattr", "rejectRequest(" + full.id + "," + model.delegationId + ")");
                    btn.innerHTML = "<i class='fa fa-close'/>";
                    html += btn.outerHTML;
                }
               
                if (!_isFollowUpNode && !self.model.isExported)
                {
                    const visualtrackingBtn = document.createElement("button");
                    visualtrackingBtn.setAttribute("class", "btn btn-xs btn-success ml-sm visualtracking");
                    visualtrackingBtn.setAttribute("title", Resources.ViewVisualTracking);
                    visualtrackingBtn.setAttribute("clickattr", "VisualTracking.openVisualTracking(" + full.documentId + "," + model.delegationId + ")");
                    visualtrackingBtn.innerHTML = "<i class='fa fa-sitemap fa-white'/>";
                    html += visualtrackingBtn.outerHTML;
                }
                return "<div style='display: inline-flex;'>" + html + "</div>";
            }
        });
        
        if (!IsInboxModeWithGrouping) {
            SecurityMatrix.getRowActions(securityMatrix, columns, self.model.nodeId);
        }
        let table = $("#" + gTableName)
            .DataTable({
                "createdRow": function (row, data, dataIndex , textColor)
                {
                    var color = "";
                    var priorities = new CoreComponents.Lookup.Priorities().get(window.language);
              
                        for (var i = 0; i < priorities.length; i++) {
                            if (priorities[i].id === data.priorityId) {
                                color = priorities[i].color;
                            }
                        }

                    if (color !== "")
                    {
                        $(row).attr('style', "color:" + color);
                    }
                    if (!data.isRead) {
                        $(row).css('font-weight', 'bold');
                        
                    }
                    
                    $(row).css('cursor', 'pointer');

                },
                processing: true,
                ordering: true,
                serverSide: true,
                // The attributes data of this datatable dropdown comes by default from the appComponent file from the ($.fn.dataTable.defaults >> values) and we can override any datatable attribute value here
                "paging": true, // Enable pagination
                pageLength: 10,  // The default number of transfer rows per page
                //lengthMenu: [10, 25, 50, 100, 200],  // Dropdown options coming by default from the appComponent for rows per page
                "ajax": {
                    "url": "/Transfer/ListInbox",
                    "type": "POST",
                    "datatype": "json",
                    data: function (d)
                    {
                        d.__RequestVerificationToken = $('input[name="__RequestVerificationToken"]').val();
                        d.NodeId = self.model.nodeId;
                        d.DelegationId = model.delegationId;
                        d.PriorityId = $("#cmbFilterInboxPriority").val() !== null && typeof $("#cmbFilterInboxPriority").val() !== "undefined" ? $("#cmbFilterInboxPriority").val() : "0";
                        d.PrivacyId = $("#cmbFilterInboxPrivacy").val() !== null && typeof $("#cmbFilterInboxPrivacy").val() !== "undefined" ? $("#cmbFilterInboxPrivacy").val() : "0";
                        d.PurposeId = $("#cmbFilterInboxPurpose").val() !== null && typeof $("#cmbFilterInboxPurpose").val() !== "undefined" ? $("#cmbFilterInboxPurpose").val() : "0";
                        d.CategoryId = $("#cmbFilterInboxCategory").val() !== null && typeof $("#cmbFilterInboxCategory").val() !== "undefined" ? $("#cmbFilterInboxCategory").val() : "0";
                        d.ReferenceNumber = $("#txtFilterInboxReferenceNumber").val() !== "" && typeof $("#txtFilterInboxReferenceNumber").val() !== "undefined" ? $("#txtFilterInboxReferenceNumber").val() : "";
                        d.FromDate = $("#filterInboxFromDate").val() !== "" && typeof $("#filterInboxFromDate").val() !== "undefined" ? $("#filterInboxFromDate").val() : "";
                        d.ToDate = $("#filterInboxToDate").val() !== "" && typeof $("#filterInboxToDate").val() !== "undefined" ? $("#filterInboxToDate").val() : "";
                        d.Read = $("#chkFilterInboxRead").is(':checked');
                        d.Locked = $("#chkFilterInboxLocked").is(':checked');
                        d.Overdue = $("#chkFilterInboxOverdue").is(':checked');
                        d.Subject = $("#txtFilterInboxSubject").val() !== "" && typeof $("#txtFilterInboxSubject").val() !== "undefined" ? $("#txtFilterInboxSubject").val() : "";
                        d.StructureIds = $("#cmbFilterInboxStructure").val() !== null && typeof $("#cmbFilterInboxStructure").val() !== "undefined" ? $("#cmbFilterInboxStructure").val() : [];
                        d.UserIds = $("#cmbFilterInboxUser").val() !== null && typeof $("#cmbFilterInboxUser").val() !== "undefined" ? $("#cmbFilterInboxUser").val() : [];
                        d.structureId = window.EnablePerStructure ? $('#hdLoggedInStructureId').val() : $("#hdStructureId").val();
                        d.FromStructureInbox = fromStructureInbox;
                        d.Period = $("#cmbFilterInboxPeriod").val() !== null && typeof $("#cmbFilterInboxPeriod").val() !== "undefiened" ? $("#cmbFilterInboxPeriod").val() : "0";
                        return d;
                    }, "dataSrc": function (response) {
                        
                        if (response.data && response.data != "") {
                            var result = [];
                            response.data.map(function (obj) {
                                var value = {
                                    key: obj.documentId.toString(),
                                    arr: response.data.filter((item) => item.documentId == obj.documentId)
                                }
                                if (result.length == 0 || (result.length > 0 && !result.filter((element) => element.key == obj.documentId).length > 0))
                                    result.push(value);
                            }, {});
                            datatableParentsAndChildren = result.map(function (item) {
                                return {
                                    parent: item.arr[0],
                                    children: item.arr
                                };
                            });
                            dataTableParents = datatableParentsAndChildren.map(function (item) {
                                return item.parent;
                            });
                        }
                        var returnValue = IsInboxModeWithGrouping ? dataTableParents : response.data;
                        return returnValue;
                    }
                },
                "order": [],
                "columns": columns,
                dom: '<"html5buttons "B>ltr<"pull-left"p><"pull-right"i>',

                "fnInitComplete": function (settings, json)
                {
                    $('[data-toggle="tooltip"]').tooltip();
                },
                buttons: allButtons,

            });

        table.on('draw.dt', function () {
            GridCommon.AddCheckBoxEvents(gTableName, true);
            $('#' + gTableName + " td input[type='checkbox']").on('click', function () {
               
                if ($(this).is(":checked") && !self.model.isExported)
                  
                    $(".conditional-buttons").removeClass("hidden");

                // $(".html5buttons").removeClass("hidden");
                else if (GridCommon.GetSelectedRows(gTableName).length == 1)
                    $(".conditional-buttons").addClass("hidden");

            });

            $('#grdItems tbody tr td').each(function () {
                this.setAttribute('title', $(this).text());
            });
            GridCommon.CheckSelectedRows(gTableName);

            if ($('#expandAll')[0] != undefined) {
                $('#expandAll')[0].classList.add("expand");
                $('#expandAll')[0].classList.remove("colllapse");
            }


        })
            
        table.$('td').tooltip({
            "delay": 0,
            "track": true,
            "fade": 250
        });

        table.on('init.dt', function () {
            if (model.transferId && model.transferId != null) {

                var targetTr = $('#' + gTableName + ' tbody div[data-id="' + model.transferId.toString() + '"]').closest('tr');;
                if (targetTr.length) {
                    var onclick = targetTr.find('.edit').attr('clickattr');
                    if (onclick) eval(onclick);
                }
            }
        })
        GridCommon.AddCheckBoxEvents(gTableName);
        if (window.AllowRowSelection == "True") {
            $('#' + gTableName + ' tbody').on('click', 'tr', function () {

                UpdateButtonVisibility(gTableName);
            });
        }
        function UpdateButtonVisibility() {
            var selectedRows = GridCommon.GetSelectedRows(gTableName).length;
            if (selectedRows > 0 && !self.model.isExported) {
                $(".conditional-buttons").removeClass("hidden");
            } else {
                $(".conditional-buttons").addClass("hidden");
            }
        }

        function UpdateButtonVisibility(tableId) {
            var selectedRows = GridCommon.GetSelectedRows(tableId).length;

            // Show buttons if at least one row is selected
            if (selectedRows > 0 && !self.model.isExported) {
                //$(".html5buttons").removeClass("hidden");
                $(".conditional-buttons").removeClass("hidden");

                //table.buttons.add(allButtons);

            } else if (selectedRows == 0) {
                $(".conditional-buttons").addClass("hidden");

            //    $(".html5buttons").addClass("hidden");
            }
        }
        if (!IsInboxModeWithGrouping) {
            $('#' + gTableName + ' tbody').on('click', ".edit,.view,.unlock,.visualtracking,.Readbtn,.accept,.reject", function () {
            var onclick = $(this).attr("clickattr");
            eval(onclick);
        });
            $('#' + gTableName + ' tbody').on('dblclick', 'tr', function () {
                
                if (!gLocked) {
                    gLocked = true;
                    try {
                        var onclick = $(this).find(".edit").attr("clickattr");
                        if (!onclick) {
                            onclick = $(this).find(".view").attr("clickattr");
                        }
                        eval(onclick);
                    } catch (e) {
                        gLocked = false;
                    }
                }
            });
            $('#' + gTableName + ' tbody').on('click', '.subjectSpan', function () {
                if (!gLocked) {
                    gLocked = true;
                    try {
                        var onclick = $(this).closest('tr').find(".edit").attr("clickattr");
                        if (!onclick) {
                            onclick = $(this).closest('tr').find(".view").attr("clickattr");
                        }
                        eval(onclick);
                    } catch (e) {
                        gLocked = false;
                    }
                }
            });
        }
        else {
            

            $('#' + gTableName + ' tbody').on('dblclick', 'tr', function () {
                
                $(this).find('td.details-control').trigger('click');

            });
        }

        $('#expandAll').on('click', function () {
            var expandAllbtn = this;

            table.rows().eq(0).each(function (index) {
                var row = table.row(index);
                var tr = row.node();
                if (expandAllbtn.classList.contains("expand")) {
                    if (row.child.isShown()) {
                        return;
                    }

                    if (IsInboxModeWithGrouping) {

                        row.child(formatChild(self, model, buttons, row)).show();
                    } else {

                        row.child(format(row, self.model.nodeId)).show();
                    }
                    tr.classList.add("shown");
                }
                else if (expandAllbtn.classList.contains("colllapse")) {
                    row.child.hide();
                    tr.classList.remove("shown")
                }
            });
            // use word colllapse not collapse to avoid hide the expandAll btn 
            if (expandAllbtn.classList.contains("expand")) {
                expandAllbtn.classList.add("colllapse");
                expandAllbtn.classList.remove("expand");

            } else if (expandAllbtn.classList.contains("colllapse")) {
                expandAllbtn.classList.add("expand");
                expandAllbtn.classList.remove("colllapse");
            }
        });
        $('#' + gTableName + ' tbody').on('click', 'td.details-control', function ()
        {
            let tr = $(this).closest('tr');
            let row = table.row(tr);
            if (row.child.isShown())
            {
                row.child.hide();
                tr.removeClass('shown');
            }
            else
            {
                if (IsInboxModeWithGrouping) {

                    row.child(formatChild(self, model, buttons, row)).show();
                } else {

                    row.child(format(row, model.nodeId)).show();
                }

                tr.addClass('shown');
            }
            //if (_isFollowUpNode) {
            //    $('[data-documentId=' + row.data().documentId + ']').prop('checked', !row.child.isShown());
            //    $('[data-documentId=' + row.data().documentId + ']').trigger("change");
            //}
        });
        $('#' + gTableName + ' tbody').on('click', 'td.parentCheckAll', function (event) {
            event.stopPropagation();
            let tr = $(this).closest('tr');
            var data = table.row(tr).data();
            $('[parent=' + data.id + ']').prop('checked', $('[data-id=' + data.id + ']').prop('checked'));
            $('[parent=' + data.id + ']').trigger("change");
        });
        SecurityMatrix.InitToolbarColor();
        SecurityMatrix.InitContextMenu(securityMatrix, self.model.nodeId);
        $('#filtersContainer input').off('keydown', function (e) { });
        $('#filtersContainer input').on('keydown', function (e)
        {
            var code = e.keyCode || e.which;            
            if (code === 13)
            {
                $("#btnFilterInboxSearch").trigger('click');
            }
        });

        $('#' + gTableName).on('change', 'input[type="checkbox"]:not(#chkAll)', function () {
            let isChecked = $(this).is(":checked");
            let table = $('#' + gTableName).DataTable();
            let pageNodes = table.rows({ page: 'current' }).nodes();
            let totalCheckboxes = $('input[type="checkbox"]:not(#chkAll)', pageNodes).length;
            let checkedCheckboxes = $('input[type="checkbox"]:not(#chkAll):checked', pageNodes).length;

            $('#chkAll').prop('checked', totalCheckboxes === checkedCheckboxes);

            if (checkedCheckboxes > 0) {
                $(".conditional-buttons").removeClass("hidden");
            } else {
                $(".conditional-buttons").addClass("hidden");
            }
        });



        table.on('draw', function () {
            $('#chkAll').prop('checked', false);

            let pageNodes = table.rows({ page: 'current' }).nodes();
            $('input[type="checkbox"]', pageNodes).prop('checked', false);

            $(".html5buttons .btn-danger").addClass("hidden");
            $(".conditional-buttons").addClass("hidden");
        });

       



    }

    
}
 export default { DocumentInbox, DocumentInboxView };