# CTS Development Guidelines

## Table of Contents
- [Core Development Constraints](#core-development-constraints)
- [Package Management](#package-management)
- [Code Organization](#code-organization)
- [Naming Conventions](#naming-conventions)
- [Best Practices](#best-practices)
- [Build and Deployment](#build-and-deployment)
- [Testing Guidelines](#testing-guidelines)

## Core Development Constraints

### 🚫 Prohibited Practices

#### 1. No Global Variables
- **Rule**: Never use global variables in the CTS codebase
- **Reason**: Maintains code quality, testability, and prevents side effects
- **Alternative**: Use proper scoping and dependency injection

```csharp
// ❌ WRONG - Global variable
public static class GlobalState
{
    public static string CurrentUser = "";
}

// ✅ CORRECT - Dependency injection
public class DocumentController : BaseController
{
    private readonly IUserService _userService;
    
    public DocumentController(IUserService userService)
    {
        _userService = userService;
    }
}
```

#### 2. No Manual Package File Editing
- **Rule**: Never manually edit package configuration files
- **Files to avoid editing**: `package.json`, `requirements.txt`, `Cargo.toml`, `go.mod`, etc.
- **Reason**: Package managers handle dependencies, versions, and conflicts automatically

#### 3. No Entity Framework Migrations
- **Rule**: All database changes must be done via SQL scripts
- **Reason**: CTS uses a SQL-first approach for database management
- **Location**: All SQL scripts go in the `Database/` directory

### ✅ Required Practices

#### 1. Use Package Managers
Always use appropriate package managers for dependency management:

```bash
# JavaScript/Node.js
npm install package-name
npm uninstall package-name

# .NET
dotnet add package PackageName
dotnet remove package PackageName

# Python (if applicable)
pip install package-name
poetry add package-name
```

#### 2. Follow Architecture Pattern
Always follow the Controllers → Managers → Entities → Database Context pattern:

```csharp
// Controller calls Manager
public IActionResult CreateDocument([FromBody] DocumentModel model)
{
    var result = ManageDocument.CreateDocument(model, UserId);
    return Ok(result);
}

// Manager calls Entity
public static DocumentResult CreateDocument(DocumentModel model, long userId)
{
    var document = new Document();
    // Business logic here
    return document.Create();
}
```

## Package Management

### Supported Package Managers by Technology

| Technology | Primary Package Manager | Alternative |
|------------|------------------------|-------------|
| .NET/C# | `dotnet` CLI | NuGet Package Manager |
| JavaScript/Node.js | `npm` | `yarn`, `pnpm` |
| Python | `pip` | `poetry`, `conda` |

### Package Management Commands

#### .NET Projects
```bash
# Add package
dotnet add package Microsoft.EntityFrameworkCore

# Remove package
dotnet remove package PackageName

# Restore packages
dotnet restore

# Update packages
dotnet list package --outdated
dotnet add package PackageName --version x.x.x
```

#### JavaScript/Node.js Projects
```bash
# Install dependencies
npm install

# Add new package
npm install package-name
npm install --save-dev package-name  # Dev dependency

# Remove package
npm uninstall package-name

# Update packages
npm update
```

## Code Organization

### Directory Structure Standards
```
CTS/
├── Intalio.CTS/                    # Main web application
│   ├── Controllers/                # Web API controllers
│   ├── Views/                      # Razor views
│   ├── wwwroot/                    # Static files
│   │   ├── components/             # Frontend components
│   │   ├── js/                     # JavaScript files
│   │   └── templates/              # Handlebars templates
├── Intalio.CTS.Core/              # Core business logic
│   ├── API/                        # Manager classes
│   ├── DAL/                        # Data access layer
│   ├── Model/                      # Data models
│   └── Utility/                    # Utility classes
└── Database/                       # SQL scripts
```

### File Organization Rules

1. **Controllers**: One controller per entity, named `{Entity}Controller.cs`
2. **Managers**: One manager per entity, named `Manage{Entity}.cs`
3. **Entities**: One entity per database table, named `{Entity}.cs`
4. **SQL Scripts**: Descriptive names with developer prefix (e.g., `Ibrahim_AddFollowUpFeature.sql`)

## Naming Conventions

### C# Code Conventions

#### Classes and Methods
```csharp
// Classes: PascalCase
public class DocumentController : BaseController
public class ManageDocument
public class Document

// Methods: PascalCase
public IActionResult UpdateDocumentStatus()
public static void CreateDocument()
public Document FindById(long id)

// Properties: PascalCase
public long Id { get; set; }
public string ReferenceNumber { get; set; }

// Private fields: camelCase with underscore
private readonly CTSContext _ctx;
private readonly IUserService _userService;

// Parameters: camelCase
public void UpdateStatus(long documentId, DocumentStatus status)
```

#### Database-Related Conventions
```csharp
// Entity classes match table names exactly
public class Document          // Maps to Document table
public class ActivityLog       // Maps to ActivityLog table
public class TranslatorDictionary  // Maps to TranslatorDictionary table

// Navigation properties: PascalCase
public virtual Category Category { get; set; }
public virtual User CreatedByUser { get; set; }
```

### SQL Script Conventions
```sql
-- File naming: {Developer}_{Description}.sql
-- Examples:
-- Ibrahim_AddFollowUpFeature.sql
-- BashirHatoum_TranslationFix.sql
-- Maha_AddExportFeature.sql

-- Translation scripts pattern:
IF NOT EXISTS (SELECT 1 FROM TranslatorDictionary WHERE Keyword = N'KeywordName')
BEGIN
    INSERT [dbo].[TranslatorDictionary] ([Keyword], [EN], [FR], [AR], [IsSystem]) 
    VALUES (N'KeywordName', 
            N'English Text', 
            N'French Text', 
            N'Arabic Text', 
            1)
END
```

### Frontend Conventions

#### JavaScript/Backbone.js
```javascript
// Route definitions in appComponent.js
routes: {
    'inbox/:nodeId': 'inboxRoute',
    'draft/:nodeId': 'draftRoute',
    'completed/:nodeId': 'completedRoute'
}

// Component naming: camelCase
var documentComponent = new DocumentComponent();
var transferComponent = new TransferComponent();
```

#### Handlebars Templates
```handlebars
<!-- Template files: lowercase with descriptive names -->
<!-- document.handlebars, inbox.handlebars, transfer.handlebars -->

<!-- Helper usage -->
{{#if readonly}}
{{#each basicAttributes}}
{{#ifEquals Name "Subject"}}
```

## Best Practices

### Code Quality Standards

#### 1. Error Handling
```csharp
// Always implement proper error handling
try
{
    var result = ManageDocument.UpdateStatus(documentId, status);
    return Ok(result);
}
catch (Exception ex)
{
    Log.Error(ex, "Error updating document status for ID: {DocumentId}", documentId);
    return StatusCode(500, "Internal server error");
}
```

#### 2. Logging
```csharp
// Use structured logging with Serilog
Log.Information("Document {DocumentId} status updated to {Status} by user {UserId}", 
    documentId, status, userId);

Log.Warning("Invalid document status transition from {OldStatus} to {NewStatus}", 
    oldStatus, newStatus);

Log.Error(ex, "Failed to update document {DocumentId}", documentId);
```

#### 3. Validation
```csharp
// Validate inputs at controller level
[HttpPost]
public IActionResult UpdateDocument([FromBody] DocumentModel model)
{
    if (!ModelState.IsValid)
    {
        return BadRequest(ModelState);
    }
    
    // Additional business validation in manager
    var result = ManageDocument.UpdateDocument(model, UserId);
    return Ok(result);
}
```

#### 4. Resource Management
```csharp
// Implement IDisposable for database contexts
public partial class Document : IDbObject<Document>, IDisposable
{
    private CTSContext _ctx;
    
    public void Dispose()
    {
        _ctx?.Dispose();
    }
    
    // Use using statements for context management
    public Document Find(long id)
    {
        using (var ctx = new CTSContext())
        {
            return ctx.Document.FirstOrDefault(d => d.Id == id);
        }
    }
}
```

### Performance Guidelines

#### 1. Database Queries
```csharp
// Use efficient queries with proper filtering
public List<Document> GetUserDocuments(long userId, int pageSize, int pageNumber)
{
    using (var ctx = new CTSContext())
    {
        return ctx.Document
            .Where(d => d.CreatedByUserId == userId)
            .OrderByDescending(d => d.CreatedDate)
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToList();
    }
}
```

#### 2. Async Operations
```csharp
// Use async/await for I/O operations
[HttpPost]
public async Task<IActionResult> ExportToG2G([FromQuery] int documentId)
{
    try
    {
        var result = await ManageDocument.ExportToG2GAsync(documentId, UserId);
        return Ok(result);
    }
    catch (Exception ex)
    {
        Log.Error(ex, "G2G export failed for document {DocumentId}", documentId);
        return BadRequest();
    }
}
```

## Build and Deployment

### Build Requirements

#### 1. Handlebars Template Changes
- **Important**: Any changes to `.hbs` files require a full project rebuild
- **Reason**: Templates are compiled into the application
- **Process**: 
  1. Make changes to `.hbs` files
  2. Rebuild entire solution
  3. Deploy updated application

#### 2. Frontend Asset Changes
```bash
# After JavaScript/CSS changes
npm run build          # If using npm build process
dotnet build           # Rebuild .NET application
```

### Deployment Checklist

1. ✅ Run all unit tests
2. ✅ Execute database scripts in correct order
3. ✅ Rebuild application if Handlebars templates changed
4. ✅ Update translation files if new text added
5. ✅ Verify package dependencies are restored
6. ✅ Check configuration settings for target environment

## Testing Guidelines

### Unit Testing Standards

#### 1. Test Structure
```csharp
[TestClass]
public class ManageDocumentTests
{
    [TestMethod]
    public void UpdateDocumentStatus_ValidInput_UpdatesStatus()
    {
        // Arrange
        var document = new Document { Id = 1, StatusId = 1 };
        var newStatus = DocumentStatus.Completed;
        
        // Act
        ManageDocument.UpdateDocumentStatus(document, newStatus);
        
        // Assert
        Assert.AreEqual((short)newStatus, document.StatusId);
    }
}
```

#### 2. Test Coverage Requirements
- All manager methods must have unit tests
- Controllers should have integration tests
- Database operations should have integration tests
- Critical business logic requires 100% test coverage

### Testing Best Practices

1. **Arrange-Act-Assert** pattern for all tests
2. **Descriptive test names** that explain the scenario
3. **Mock external dependencies** (database, web services)
4. **Test both success and failure scenarios**
5. **Use test data builders** for complex object creation

## Quick Reference

### Common Commands
```bash
# Build solution
dotnet build

# Run tests
dotnet test

# Add package
dotnet add package PackageName

# Database script execution
sqlcmd -S server -d database -i script.sql
```

### Key Constraints Summary
- ❌ No global variables
- ❌ No manual package file editing  
- ❌ No Entity Framework migrations
- ✅ Use package managers
- ✅ Follow architecture pattern
- ✅ SQL scripts for database changes
- ✅ Rebuild for Handlebars changes
