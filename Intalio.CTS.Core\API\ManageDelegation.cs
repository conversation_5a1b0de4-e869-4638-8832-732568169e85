﻿using Intalio.Core;
using Intalio.Core.API;
using Intalio.Core.Model;
using Intalio.CTS.Core.DAL;
using Intalio.CTS.Core.Model;
using Newtonsoft.Json;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using DelegationModel = Intalio.CTS.Core.Model.DelegationModel;
using DelegationViewModel = Intalio.CTS.Core.Model.DelegationViewModel;
using DelegationListViewModel = Intalio.CTS.Core.Model.DelegationListViewModel;
using DelegationMenuModel = Intalio.CTS.Core.Model.DelegationMenuModel;
using SystemDelegationViewModel = Intalio.CTS.Core.Model.SystemDelegationViewModel;
using SystemDelegationListViewModel = Intalio.CTS.Core.Model.SystemDelegationListViewModel;

namespace Intalio.CTS.Core.API
{
    public static class ManageDelegation
    {
        #region Fields

        public static event UserHandler UserDelegationHandler;

        private static ConcurrentDictionary<long, Intalio.CTS.Core.Model.DelegationModel> DelegationList
        {
            get
            {
                return new CacheUtility().GetCachedItem<ConcurrentDictionary<long, Intalio.CTS.Core.Model.DelegationModel>>(
                typeof(ManageDelegation).Name) ??
                new CacheUtility()
                .InsertCachedItem<ConcurrentDictionary<long, Intalio.CTS.Core.Model.DelegationModel>>(
                    new ConcurrentDictionary<long, Intalio.CTS.Core.Model.DelegationModel>(),
                    typeof(ManageDelegation).Name);
            }
        }

        public static void ClearAndLoad()
        {
            DelegationList.Keys.ToList().ForEach(key => DelegationList.TryRemove(key, out _));
        }

        #endregion

        #region Public Methods

        public static void Create(long userId, Language language, DelegationViewModel model)
        {
            Delegation item = new Delegation
            {
                FromUserId = userId,
                ToUserId = model.ToUserId,
                FromDate = Convert.ToDateTime(model.FromDate).Date,
                ToDate = Convert.ToDateTime(model.ToDate).Date.AddHours(23).AddMinutes(59).AddSeconds(59),
                PrivacyId = model.PrivacyId,
                AllowSign = model.AllowSign,
                ShowOldCorespondence= model.ShowOldCorespondence,
                StartDate = model.StartDate != null? Convert.ToDateTime(model.StartDate).Date:null,
                Note= model.Note,
                DraftInbox = model.DraftInbox,
            };
            item.CategoryIds = !model.CategoryIds.IsNullOrEmpty() ? string.Join(Constants.SPLITTER, model.CategoryIds) : string.Empty;

            Intalio.Core.Model.UserModel toUser = new Intalio.Core.Model.UserModel();
            if (UserDelegationHandler != null)
            {
                toUser = UserDelegationHandler.Invoke(model.ToUserId);
                //do Provision
                ManageUser.Provision(new UserViewModel { Id = toUser.Id, Firstname = toUser.FirstName, Lastname = toUser.LastName, RoleId = toUser.ApplicationRoleId }, false);
            }

            item.Insert();
            
            if (Configuration.EnableEmailNotification)
            {
                //send email
                var fromUser = new Intalio.Core.DAL.User().Find(userId);
                DelegationModel delegationModel = new Delegation().Find(item.Id);
                delegationModel.CategoryNames = new List<string>();
                foreach (var category in ManageSecurityMatrix.ListCategories(language: language))
                {
                    if (delegationModel.CategoryIds.Contains((int)category.Id))
                    {
                        delegationModel.CategoryNames.Add(category.Text);
                    }
                }
                delegationModel.ToUserName = string.Format("{0} {1}", toUser.FirstName, toUser.LastName);
                SendEmail(toUser?.Email, string.Format("{0} {1}", fromUser.Firstname, fromUser.Lastname), delegationModel);
            }

            model.Id = item.Id;
        }

        public static bool Edit(long userId, Language language, DelegationViewModel model)
        {
            bool retValue = false;
            if (model.Id != null)
            {
                Delegation item = new Delegation().Find(model.Id.Value);
                if (item != null)
                {
                    item.ToUserId = model.ToUserId;
                    item.FromDate = Convert.ToDateTime(model.FromDate).Date;
                    item.ToDate =  Convert.ToDateTime(model.ToDate).Date.AddHours(23).AddMinutes(59).AddSeconds(59);
                    item.CategoryIds = !model.CategoryIds.IsNullOrEmpty() ?
                        string.Join(Constants.SPLITTER, model.CategoryIds) : string.Empty;
                    item.PrivacyId = model.PrivacyId;
                    item.AllowSign = model.AllowSign;
                    item.ShowOldCorespondence= model.ShowOldCorespondence;
                    item.StartDate = model.StartDate != null ? Convert.ToDateTime(model.StartDate).Date : null;
                    item.Note = model.Note;
                    item.DraftInbox = model.DraftInbox;

                    Intalio.Core.Model.UserModel toUser = new Intalio.Core.Model.UserModel();
                    if (UserDelegationHandler != null)
                    {
                        toUser = UserDelegationHandler.Invoke(model.ToUserId);
                        //do Provision
                        ManageUser.Provision(new UserViewModel { Id = toUser.Id, Firstname = toUser.FirstName, Lastname = toUser.LastName, RoleId = toUser.ApplicationRoleId }, false);
                    }
                    item.Update();

                    if (Configuration.EnableEmailNotification)
                    {
                        if (item.ToUserId != model.ToUserId)
                        {
                            //send email
                            var fromUser = new Intalio.Core.DAL.User().Find(userId);
                            DelegationModel delegationModel = item;
                            delegationModel.CategoryNames = new List<string>();
                            foreach (var category in ManageSecurityMatrix.ListCategories(language: language))
                            {
                                if (delegationModel.CategoryIds.Contains((int)category.Id))
                                {
                                    delegationModel.CategoryNames.Add(category.Text);
                                }
                            }
                            SendEmail(toUser?.Email, string.Format("{0} {1}", fromUser.Firstname, fromUser.Lastname), delegationModel);
                        }
                    }

                    DelegationList.TryRemove(item.Id, out _);

                    retValue = true;
                }
            }
            return retValue;
        }

        public static (int, List<DelegationListViewModel>) List(int startIndex, int pageSize, long userId, ExpressionBuilderFilters filter,
              List<SortExpression> sortExpression)
        {
            using (Delegation item = new Delegation())
            {
                var filterExp = ExpressionBuilder
                    .GetExpression<Delegation>(filter, ExpressionBuilderOperator.And);
                var countResult = item.GetCount(userId, filterExp);
                var itemList = item.List(startIndex, pageSize, userId, filterExp,
                    sortExpression.OrderByExpression<Delegation>());
                return (countResult.Result, itemList.Select(t =>
                (DelegationListViewModel)t).ToList());
            }
        }

        public static void Delete(long userId, List<long> ids)
        {
            foreach (var id in ids)
            {
                var item = new Delegation().Find(id);
                if (item != null && item.FromUserId == userId)
                {
                    item.Delete();
                }
            }
        }

        public static (bool Exists, int Code) CheckUnique(long userId, DelegationViewModel model)
        {
            return new Delegation().CheckUnique(model.Id, model.CategoryIds, userId, model.ToUserId, Convert.ToDateTime(model.FromDate), Convert.ToDateTime(model.ToDate));
        }

        public static List<DelegationMenuModel> ListDelegatedToUser(long userId)
        
        {
            return new Delegation().ListDelegatedToUser(userId).Select(t => (DelegationMenuModel)t).ToList();
        }

        public static string ListDelegationToUserIds(long userId)
        {
            return new Delegation().ListDelegationToUserIds(userId);
        }
        public static string ListDelegationToUserIdsWithDelegationId(long userId)
        {
            return new Delegation().ListDelegationToUserIdsWithDelegationId(userId);
        }
        public static DelegationModel CheckUserDelegation(long userId, int documentTypeId)
        {
            return new Delegation().CheckUserDelegation(userId, documentTypeId);
        }

        public static DelegationModel GetByDelegationId(long userId, long delegationId,DateTime? transferDate = null)
        {
            DelegationModel retValue;
            DelegationList.TryGetValue(delegationId, out retValue);
            //get item from db
            if (retValue == null)
            {
                retValue = new Delegation().Find(delegationId);
                DelegationList.TryAdd(delegationId, retValue);
            }
            if (retValue != null)
            {
                if (retValue.StructureIds == null || retValue.GroupIds == null)
                {
                    if (UserDelegationHandler != null)
                    {
                        var user = UserDelegationHandler.Invoke(retValue.FromUserId);
                        if (user != null)
                        {
                            retValue.StructureIds = user.StructureIds;
                            retValue.GroupIds = user.Groups.Select(t => t.Id).ToList();
                            retValue.RoleId = Convert.ToInt32(user.ApplicationRoleId);

                            if (!user.Attributes.IsNullOrEmpty())
                            {
                                retValue.IsStructureReceiver = user.Attributes.Where(t => t.Text == Configuration.UserStructureReceiver).Select(t => Convert.ToBoolean(t.Value)).FirstOrDefault();
                                retValue.IsStructureSender = user.Attributes.Where(t => t.Text == Configuration.UserStructureSender).Select(t => Convert.ToBoolean(t.Value)).FirstOrDefault();


                            }
                        }
                    }
                }
                if (retValue.ToUserId != userId || retValue.ToDate.Date < DateTime.Now.Date)
                {
                    return null;
                }
                if (transferDate != null)
                {
                    var delegationStartDate = retValue.FromDate.Date;
                    if(retValue.ShowOldCorespondence && retValue.StartDate.HasValue)
                    {
                        delegationStartDate= retValue.StartDate.Value;
                    }
                    var returnNull = true;

                    if (retValue.ShowOldCorespondence && !retValue.StartDate.HasValue)
                        returnNull = false;

                    if(transferDate.Value.Date >= delegationStartDate.Date && transferDate.Value.Date <= retValue.ToDate.Date)
                        returnNull = false;

                    if (returnNull)
                    {
                        return null;
                    }
                    

                }
            }
            return retValue;
        }

        public static DelegationModel GetByDelegationId(long userId, long delegationId, List<Transfer> transfers)
        {
            DelegationModel retValue;
            DelegationList.TryGetValue(delegationId, out retValue);
            //get item from db
            if (retValue == null)
            {
                retValue = new Delegation().Find(delegationId);
                DelegationList.TryAdd(delegationId, retValue);
            }
            if (retValue != null)
            {
                if (retValue.StructureIds == null || retValue.GroupIds == null)
                {
                    if (UserDelegationHandler != null)
                    {
                        var user = UserDelegationHandler.Invoke(retValue.FromUserId);
                        if (user != null)
                        {
                            retValue.StructureIds = user.StructureIds;
                            retValue.GroupIds = user.Groups.Select(t => t.Id).ToList();
                            retValue.RoleId = Convert.ToInt32(user.ApplicationRoleId);

                            if (!user.Attributes.IsNullOrEmpty())
                            {
                                retValue.IsStructureReceiver = user.Attributes.Where(t => t.Text == Configuration.UserStructureReceiver).Select(t => Convert.ToBoolean(t.Value)).FirstOrDefault();
                                retValue.IsStructureSender = user.Attributes.Where(t => t.Text == Configuration.UserStructureSender).Select(t => Convert.ToBoolean(t.Value)).FirstOrDefault();


                            }
                        }
                    }
                }
                if (retValue.ToUserId != userId || retValue.ToDate.Date < DateTime.Now.Date)
                {
                    return null;
                }
                if (transfers != null && transfers.Count>0)
                {
                    foreach (var transfer in transfers)
                    {
                        var delegationStartDate = retValue.FromDate.Date;
                        if (retValue.ShowOldCorespondence && retValue.StartDate.HasValue)
                        {
                            delegationStartDate = retValue.StartDate.Value;
                        }
                        var returnNull = true;

                        if (retValue.ShowOldCorespondence && !retValue.StartDate.HasValue)
                            returnNull = false;

                        if (transfer.CreatedDate.Date >= delegationStartDate.Date && transfer.CreatedDate.Date <= retValue.ToDate.Date)
                            returnNull = false;

                        if (returnNull)
                        {
                            return null;
                        }
                    }
                    


                }
            }
            return retValue;
        }

        public static List<DelegationModel> GetByToUserId(long userId)
        {
            List<DelegationModel> retValue = new List<DelegationModel>();
            //DelegationList.TryGetValue(userId, out retValue);
            //get item from db
            var lstDelegation = new List<Delegation>();
            if (retValue == null)
            {
                lstDelegation = new Delegation().FindByUserTo(userId);
                //DelegationList.TryAdd(userId, retValue);
            }
            if (retValue != null)
            {
                foreach (var item in lstDelegation)
                {

                    if (UserDelegationHandler != null)
                    {
                        DelegationModel delegationModel = item;
                        var user = UserDelegationHandler.Invoke(item.FromUserId);
                        if (user != null)
                        {
                            delegationModel.StructureIds = user.StructureIds;
                            delegationModel.GroupIds = user.Groups.Select(t => t.Id).ToList();
                            delegationModel.RoleId = Convert.ToInt32(user.ApplicationRoleId);
                            if (!user.Attributes.IsNullOrEmpty())
                            {
                                delegationModel.IsStructureReceiver = user.Attributes.Where(t => t.Text == Configuration.UserStructureReceiver).Select(t => Convert.ToBoolean(t.Value)).FirstOrDefault();
                                delegationModel.IsStructureSender = user.Attributes.Where(t => t.Text == Configuration.UserStructureSender).Select(t => Convert.ToBoolean(t.Value)).FirstOrDefault();

                            }
                            retValue.Add(delegationModel);
                        }
                    }

                }


            }
            return retValue;
        }

        #region Admin

        public static (bool Exists, int Code) CheckUnique(SystemDelegationViewModel model)
        {
            return new Delegation().CheckUnique(model.Id, model.CategoryIds, model.FromUserId, model.ToUserId, Convert.ToDateTime(model.FromDate), Convert.ToDateTime(model.ToDate));
        }

        public static void CreateSystemDelegation(long userId, Language language, SystemDelegationViewModel model)
        {
            Delegation item = new Delegation
            {
                FromUserId = model.FromUserId,
                ToUserId = model.ToUserId,
                FromDate = Convert.ToDateTime(model.FromDate).Date,
                ToDate = Convert.ToDateTime(model.ToDate).Date,
                CreatedByUserId = userId,
                PrivacyId = model.PrivacyId,
                StartDate= model.StartDate != null ? Convert.ToDateTime(model.StartDate).Date : null,
                ShowOldCorespondence = model.ShowOldCorespondence,
                AllowSign = model.AllowSign,
                DraftInbox = model.DraftInbox,
                Note= model.Note,
            };
            item.CategoryIds = !model.CategoryIds.IsNullOrEmpty() ? string.Join(Constants.SPLITTER, model.CategoryIds) : string.Empty;

            Intalio.Core.Model.UserModel toUser = new Intalio.Core.Model.UserModel();
            Intalio.Core.Model.UserModel fromUser = new Intalio.Core.Model.UserModel();
            if (UserDelegationHandler != null)
            {
                //do Provision
                toUser = UserDelegationHandler.Invoke(model.ToUserId);
                ManageUser.Provision(new UserViewModel { Id = toUser.Id, Firstname = toUser.FirstName, Lastname = toUser.LastName, RoleId = toUser.ApplicationRoleId }, false);

                fromUser = UserDelegationHandler.Invoke(model.FromUserId);
                ManageUser.Provision(new UserViewModel { Id = fromUser.Id, Firstname = fromUser.FirstName, Lastname = fromUser.LastName, RoleId = fromUser.ApplicationRoleId }, false);
            }

            item.Insert();
            ManageAudit.Add(userId, AuditModule.SystemDelegation, AuditAction.Add, originalValue: JsonConvert.SerializeObject(new AuditDelegationModel
            {
                FromUserId = item.FromUserId,
                ToUserId = item.ToUserId,
                CategoryIds = item.CategoryIds,
                FromDate = item.FromDate.ToString(Constants.DATE_FORMAT),
                ToDate = item.ToDate.ToString(Constants.DATE_FORMAT)
            }));

            if (Configuration.EnableEmailNotification)
            {
                //send email
                DelegationModel delegationModel = new Delegation().Find(item.Id);
                delegationModel.CategoryNames = new List<string>();
                foreach (var category in ManageSecurityMatrix.ListCategories(language: language))
                {
                    if (delegationModel.CategoryIds.Contains((int)category.Id))
                    {
                        delegationModel.CategoryNames.Add(category.Text);
                    }
                }
                SendEmail(toUser?.Email, string.Format("{0} {1}", fromUser.FirstName, fromUser.LastName), delegationModel);
            }

            model.Id = item.Id;
        }

        public static bool EditSystemDelegation(long userId, Language language, SystemDelegationViewModel model)
        {
            bool retValue = false;
            if (model.Id != null)
            {
                Delegation item = new Delegation().Find(model.Id.Value);
                if (item != null)
                {
                    var orignalValue = JsonConvert.SerializeObject(new AuditDelegationModel
                    {
                        FromUserId = item.FromUserId,
                        ToUserId = item.ToUserId,
                        CategoryIds = item.CategoryIds,
                        FromDate = item.FromDate.ToString(Constants.DATE_FORMAT),
                        ToDate = item.ToDate.ToString(Constants.DATE_FORMAT)
                    });
                    item.FromUserId = model.FromUserId;
                    item.ToUserId = model.ToUserId;
                    item.FromDate = Convert.ToDateTime(model.FromDate).Date;
                    item.ToDate = Convert.ToDateTime(model.ToDate).Date;
                    item.CategoryIds = !model.CategoryIds.IsNullOrEmpty() ? string.Join(Constants.SPLITTER, model.CategoryIds) : string.Empty;
                    item.PrivacyId = model.PrivacyId;
                    item.StartDate = model.StartDate != null ? Convert.ToDateTime(model.StartDate).Date : null;
                    item.ShowOldCorespondence = model.ShowOldCorespondence;
                    item.AllowSign = model.AllowSign;
                    item.DraftInbox = model.DraftInbox;
                    item.Note = model.Note;
                    Intalio.Core.Model.UserModel toUser = new Intalio.Core.Model.UserModel();
                    if (UserDelegationHandler != null)
                    {
                        toUser = UserDelegationHandler?.Invoke(model.ToUserId);
                        //do Provision
                        ManageUser.Provision(new UserViewModel { Id = toUser.Id, Firstname = toUser.FirstName, Lastname = toUser.LastName, RoleId = toUser.ApplicationRoleId }, false);
                    }
                    item.Update();
                    var newValue = JsonConvert.SerializeObject(new AuditDelegationModel
                    {
                        FromUserId = item.FromUserId,
                        ToUserId = item.ToUserId,
                        CategoryIds = item.CategoryIds,
                        FromDate = item.FromDate.ToString(Constants.DATE_FORMAT),
                        ToDate = item.ToDate.ToString(Constants.DATE_FORMAT)
                    });
                    ManageAudit.Add(userId, AuditModule.SystemDelegation, AuditAction.Edit, orignalValue, newValue);

                    if (Configuration.EnableEmailNotification)
                    {
                        if (item.ToUserId != model.ToUserId)
                        {
                            //send email
                            var fromUser = new Intalio.Core.DAL.User().Find(model.FromUserId);
                            DelegationModel delegationModel = item;
                            delegationModel.CategoryNames = new List<string>();
                            foreach (var category in ManageSecurityMatrix.ListCategories(language: language))
                            {
                                if (delegationModel.CategoryIds.Contains((int)category.Id))
                                {
                                    delegationModel.CategoryNames.Add(category.Text);
                                }
                            }
                            SendEmail(toUser?.Email, string.Format("{0} {1}", fromUser.Firstname, fromUser.Lastname), delegationModel);
                        }
                    }

                    retValue = true;
                }
            }
            return retValue;
        }

        public static (int, List<SystemDelegationListViewModel>) ListSystemDelegation(int startIndex, int pageSize, ExpressionBuilderFilters filter,
              List<SortExpression> sortExpression)
         {
            using (Delegation item = new Delegation())
            {
                var filterExp = ExpressionBuilder.GetExpression<Delegation>(filter, ExpressionBuilderOperator.And);
                var countResult = item.GetCountSystemDelegation(filterExp);
                var itemList = item.ListSystemDelegation(startIndex, pageSize, filterExp, sortExpression.OrderByExpression<Delegation>());
                return (countResult.Result, itemList.Select(t => (SystemDelegationListViewModel)t).ToList());
            }
        }

        public static void DeleteSystemDelegation(long userId, List<long> ids)
        {
            foreach (var id in ids)
            {
                var item = new Delegation().Find(id);
                if (item != null)
                {
                    var orignalValue = JsonConvert.SerializeObject(new AuditDelegationModel
                    {
                        FromUserId = item.FromUserId,
                        ToUserId = item.ToUserId,
                        CategoryIds = item.CategoryIds,
                        FromDate = item.FromDate.ToString(Constants.DATE_FORMAT),
                        ToDate = item.ToDate.ToString(Constants.DATE_FORMAT)
                    });
                    item.Delete();
                    ManageAudit.Add(userId, AuditModule.SystemDelegation, AuditAction.Delete, orignalValue);
                }
            }
        }

        #endregion

        #endregion

        #region Private Methods

        private static void SendEmail(string email, string fromUser, DelegationModel delegation = null)
        {
            Dictionary<string, string> bookmark = new Dictionary<string, string>();
            var categories = string.Join(Constants.SPLITTER + " ", delegation.CategoryNames);
            bookmark.Add("[Categories]", categories);
            bookmark.Add("[DocumentTypes]", categories);
            bookmark.Add("[User]", fromUser);
            bookmark.Add("[ReceiverName]", delegation.ToUserName);
            ManageNotificationTemplate.Send(email, NotificationTemplateEnum.OnDelegation.ToString(), bookmark);
        }

        #endregion
    }
}
