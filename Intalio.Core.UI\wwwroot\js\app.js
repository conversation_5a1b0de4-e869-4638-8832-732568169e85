﻿window.Seperator = "/";
window.Splitter = ",";
window.TimeStep = "5";
window.DefaultColor = "#00AE8D";
//For hijri support
var DAYS_IN_GR_MONTH = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];
var gFirstRefForUAQ = new Date(1882, 10, 12, 0, 0, 0, 0);
var gLastRefForUAQ = new Date(2174, 10, 25, 23, 59, 59, 999);
var ONE_DAY = 86400000;
var ONE_HOUR = 3600000;
var civilMonth12Length = [
    354,
    355,
    354,
    354,
    355,
    354,
    355,
    354,
    354,
    355,
    354,
    354,
    355,
    354,
    354,
    355,
    354,
    355,
    354,
    354,
    355,
    354,
    354,
    355,
    354,
    355,
    354,
    354,
    355,
    354,
];
var UAQ_MONTH_LENGTH = [
    "101010101010",
    "110101010100",
    "111011001001",
    "011011010100",
    "011011101010",
    "001101101100",
    "101010101101",
    "010101010101",
    "011010101001",
    "011110010010",
    "101110101001",
    "010111010100",
    "101011011010",
    "010101011100",
    "110100101101",
    "011010010101",
    "011101001010",
    "101101010100",
    "101101101010",
    "010110101101",
    "010010101110",
    "101001001111",
    "010100010111",
    "011010001011",
    "011010100101",
    "101011010101",
    "001011010110",
    "100101011011",
    "010010011101",
    "101001001101",
    "110100100110",
    "110110010101",
    "010110101100",
    "100110110110",
    "001010111010",
    "101001011011",
    "010100101011",
    "101010010101",
    "011011001010",
    "101011101001",
    "001011110100",
    "100101110110",
    "001010110110",
    "100101010110",
    "101011001010",
    "101110100100",
    "101111010010",
    "010111011001",
    "001011011100",
    "100101101101",
    "010101001101",
    "101010100101",
    "101101010010",
    "101110100101",
    "010110110100",
    "100110110110",
    "010101010111",
    "001010010111",
    "010101001011",
    "011010100011",
    "011101010010",
    "101101100101",
    "010101101010",
    "101010101011",
    "010100101011",
    "110010010101",
    "110101001010",
    "110110100101",
    "010111001010",
    "101011010110",
    "100101010111",
    "010010101011",
    "100101001011",
    "101010100101",
    "101101010010",
    "101101101010",
    "010101110101",
    "001001110110",
    "100010110111",
    "010001011011",
    "010101010101",
    "010110101001",
    "010110110100",
    "100111011010",
    "010011011101",
    "001001101110",
    "100100110110",
    "101010101010",
    "110101010100",
    "110110110010",
    "010111010101",
    "001011011010",
    "100101011011",
    "010010101011",
    "101001010101",
    "101101001001",
    "101101100100",
    "101101110001",
    "010110110100",
    "101010110101",
    "101001010101",
    "110100100101",
    "111010010010",
    "111011001001",
    "011011010100",
    "101011101001",
    "100101101011",
    "010010101011",
    "101010010011",
    "110101001001",
    "110110100100",
    "110110110010",
    "101010111001",
    "010010111010",
    "101001011011",
    "010100101011",
    "101010010101",
    "101100101010",
    "101101010101",
    "010101011100",
    "010010111101",
    "001000111101",
    "100100011101",
    "101010010101",
    "101101001010",
    "101101011010",
    "010101101101",
    "001010110110",
    "100100111011",
    "010010011011",
    "011001010101",
    "011010101001",
    "011101010100",
    "101101101010",
    "010101101100",
    "101010101101",
    "010101010101",
    "101100101001",
    "101110010010",
    "101110101001",
    "010111010100",
    "101011011010",
    "010101011010",
    "101010101011",
    "010110010101",
    "011101001001",
    "011101100100",
    "101110101010",
    "010110110101",
    "001010110110",
    "101001010110",
    "111001001101",
    "101100100101",
    "101101010010",
    "101101101010",
    "010110101101",
    "001010101110",
    "100100101111",
    "010010010111",
    "011001001011",
    "011010100101",
    "011010101100",
    "101011010110",
    "010101011101",
    "010010011101",
    "101001001101",
    "110100010110",
    "110110010101",
    "010110101010",
    "010110110101",
    "001011011010",
    "100101011011",
    "010010101101",
    "010110010101",
    "011011001010",
    "011011100100",
    "101011101010",
    "010011110101",
    "001010110110",
    "100101010110",
    "101010101010",
    "101101010100",
    "101111010010",
    "010111011001",
    "001011101010",
    "100101101101",
    "010010101101",
    "101010010101",
    "101101001010",
    "101110100101",
    "010110110010",
    "100110110101",
    "010011010110",
    "101010010111",
    "010101000111",
    "011010010011",
    "011101001001",
    "101101010101",
    "010101101010",
    "101001101011",
    "010100101011",
    "101010001011",
    "110101000110",
    "110110100011",
    "010111001010",
    "101011010110",
    "010011011011",
    "001001101011",
    "100101001011",
    "101010100101",
    "101101010010",
    "101101101001",
    "010101110101",
    "000101110110",
    "100010110111",
    "001001011011",
    "010100101011",
    "010101100101",
    "010110110100",
    "100111011010",
    "010011101101",
    "000101101101",
    "100010110110",
    "101010100110",
    "110101010010",
    "110110101001",
    "010111010100",
    "101011011010",
    "100101011011",
    "010010101011",
    "011001010011",
    "011100101001",
    "011101100010",
    "101110101001",
    "010110110010",
    "101010110101",
    "010101010101",
    "101100100101",
    "110110010010",
    "111011001001",
    "011011010010",
    "101011101001",
    "010101101011",
    "010010101011",
    "101001010101",
    "110100101001",
    "110101010100",
    "110110101010",
    "100110110101",
    "010010111010",
    "101000111011",
    "010010011011",
    "101001001101",
    "101010101010",
    "101011010101",
    "001011011010",
    "100101011101",
    "010001011110",
    "101000101110",
    "110010011010",
    "110101010101",
    "011010110010",
    "011010111001",
    "010010111010",
    "101001011101",
    "010100101101",
    "101010010101",
    "101101010010",
    "101110101000",
    "101110110100",
    "010110111001",
    "001011011010",
    "100101011010",
    "101101001010",
    "110110100100",
    "111011010001",
    "011011101000",
    "101101101010",
    "010101101101",
    "010100110101",
    "011010010101",
    "110101001010",
    "110110101000",
    "110111010100",
    "011011011010",
    "010101011011",
    "001010011101",
    "011000101011",
    "101100010101",
    "101101001010",
    "101110010101",
    "010110101010",
    "101010101110",
    "100100101110",
    "110010001111",
    "010100100111",
    "011010010101",
    "011010101010",
    "101011010110",
    "010101011101",
    "001010011101",
];
//end for hijri support
var Common = (function (E)
{
    E = {};
    if (window.navigator.msSaveBlob)
    {
        $.ajaxSetup({ cache: false });
    }
    E.format = function ()
    {
        var s = arguments[0];
        for (var i = 0; i < arguments.length - 1; i++)
        {
            var reg = new RegExp("\\{" + i + "\\}", "gm");
            s = s.replace(reg, arguments[i + 1]);
        }
        return s;
    };
    E.getPosition = function (element)
    {
        var xPosition = 0;
        var yPosition = 0;
        while (element)
        {
            xPosition += (element.offsetLeft - element.scrollLeft + element.clientLeft);
            yPosition += (element.offsetTop - element.scrollTop + element.clientTop);
            element = element.offsetParent;
        }
        return { x: xPosition, y: yPosition };
    };
    E.mask = function ()
    {
        var maskHTML = "<div class=\"loading-mask-image\" style=\"display: block;\"><i class=\"fa fa-spinner fa-pulse fa-2x fa - fw\" /></div>";
        var div = document.createElement("div");
        if (arguments[1] === ("body-mask"))
        {
            div.setAttribute("class", "loading-mask-container");
        } else
        {
            div.setAttribute("class", "loading-mask-container maskDivHeight");
        }
        div.setAttribute("id", arguments[1]);
        div.innerHTML = maskHTML;
        var el = arguments[0];
        el.appendChild(div);
        var elPos = E.getPosition(el);
        var mask = document.getElementById(arguments[1]);
        mask.style.top = "0px";
        mask.style.height = $(document).height() + 'px';
        mask.style.width = "100%";
    };
    E.unmask = function ()
    {
        var div = document.getElementById(arguments[0]);
        if (div !== null)
        {
            div.parentNode.removeChild(div);
        }
    };
    E.showConfirmMsg = function (msg, callback, cancelCallback, additionalMsg, closeOnConfirm)
    {
        if (typeof additionalMsg !== "undefined")
        {
            msg += '<br/>' + additionalMsg;
        }
        swal({
            title: "",
            text: msg,
            showCancelButton: true,
            confirmButtonClass: "btn-info btn",
            confirmButtonText: Resources.Yes,
            cancelButtonText: Resources.No,
            closeOnConfirm: typeof closeOnConfirm !== "undefined" ? closeOnConfirm : true,
            closeOnCancel: true,
            html: typeof additionalMsg !== "undefined" ? true : false
        },
            function (isConfirm)
            {
                if (isConfirm)
                {
                    callback();
                }
                else
                {
                    if (typeof cancelCallback === 'function')
                    {
                        cancelCallback();
                    }
                }
            });
    };
    E.alertMsg = function (msg, callback)
    {
        swal({
            title: "",
            text: msg,
            showCancelButton: false,
            confirmButtonClass: "btn-info btn",
            confirmButtonText: Resources.OK,
            closeOnConfirm: true,
            closeOnCancel: true,
            html: (typeof (additionalMsg) !== "undefined" ? additionalMsg : "")
        }, function ()
        {
            if (typeof callback === 'function')
            {
                callback();
            }
        });
    };
    E.ajaxPost = function (url, params, successHandler, errorHandler, showMask, id) //id of the mask location
    {
        if (showMask)
        {
            if (id !== undefined && id !== null)
            {
                var element = document.getElementById(id);
                Common.mask(element, id + "-mask");
            }
            else
            {
                Common.mask(document.body, "body-mask");
            }
        }
        $.ajax({
            url: url,
            type: 'POST',
            data: params
        }).done(function (data, textStatus, request)
        {
            if (request.getResponseHeader('LoginPage') !== null)
            {
                location.reload(true);
            }
            if (typeof successHandler === "function") { successHandler(data); }
        }).fail(function (jqXHR, textStatus, errorThrown)
        {
            if (typeof errorHandler === "function") { errorHandler(errorThrown, jqXHR); }
        })
            .always(function ()
            {
                if (showMask)
                {
                    if (id !== undefined && id !== null)
                    {
                        Common.unmask(id + "-mask");
                    }
                    else
                    {
                        Common.unmask("body-mask");
                    }
                }
            });
    };
    E.objectToFormData = function (formData, params, name)
    {
        name = name || '';
        if (typeof params === 'object' && !(params instanceof File) && !(params instanceof Blob))
        {
            $.each(params, function (key, value)
            {
                if (name === '')
                {
                    Common.objectToFormData(formData, value, key);
                } else
                {
                    Common.objectToFormData(formData, value, name + '[' + key + ']');
                }
            });
        }
        else
        {
            if (params instanceof Blob)
            {
                formData.append(name, params, params.name);
            } else
            {
                formData.append(name, params);
            }
        }
    };
    E.ajaxPostWithFile = function (url, params, successHandler, errorHandler, showMask, id)
    {//id of the mask location
        if (showMask)
        {
            if (id !== undefined && id !== null)
            {
                var element = document.getElementById(id);
                Common.mask(element, id + "-mask");
            }
            else
            {
                Common.mask(document.body, "body-mask");
            }
        }
        var model = new FormData();
        Common.objectToFormData(model, params);
        $.ajax({
            url: url,
            type: 'POST',
            processData: false,
            contentType: false,
            data: model
        }).done(function (data, textStatus, request)
        {
            if (request.getResponseHeader('LoginPage') !== null)
            {
                location.reload(true);
            }
            if (typeof successHandler === "function") { successHandler(data); };
        }).fail(function (jqXHR, textStatus, errorThrown)
        {
            if (typeof errorHandler === "function") { errorHandler(errorThrown, jqXHR); };
        }).always(function ()
        {
            if (showMask)
            {
                if (id !== undefined && id !== null)
                {
                    Common.unmask(id + "-mask");
                }
                else
                {
                    Common.unmask("body-mask");
                }
            }
        });
    };
    E.showScreenSuccessMsg = function (message)
    {
        toastr.options.positionClass = "toast-bottom-right";
        if (window.language === "ar")
        {
            toastr.options.positionClass = "toast-bottom-left";
            toastr.options.rtl = true;
        }
        toastr.success(message === undefined ? Resources.UpdateSuccessMsg : message);

        //var template = "<div class=\"form-group alert alert-success fade in text-center mb0\"> <a href=\"#\" class=\"close\" data-dismiss=\"alert\">&times;</a><strong>{0}</strong></div>";
        //$('#' + id).html(Common.format(template, message === undefined ? Resources.UpdateSuccessMsg : message));
    };
    E.showScreenWarningMsg = function (message)
    {
        toastr.options.positionClass = "toast-bottom-right";
        if (window.language === "ar")
        {
            toastr.options.positionClass = "toast-bottom-left";
            toastr.options.rtl = true;
        }
        toastr.warning(message);
    };
    E.showScreenErrorMsg = function (message)
    {
        toastr.options.positionClass = "toast-bottom-right";
        if (window.language === "ar")
        {
            toastr.options.positionClass = "toast-bottom-left";
            toastr.options.rtl = true;
        }
        toastr.error(message === undefined ? Resources.ErrorOccured : message);
        //var template = "<div class=\"form-group alert alert-danger fade in text-center mb0\"> <a href=\"#\" class=\"close\" data-dismiss=\"alert\">&times;</a><strong>{0}</strong></div>";
        //$('#' + id).html(Common.format(template, message === undefined ? Resources.ErrorOccured : message));
    };
    E.ajaxGet = function (url, params, successHandler, errorHandler, showMask, id, async) //id of the mask location
    {
        if (typeof async === "undefined" || async === null)
        {
            async = true;
        }
        if (showMask)
        {
            if (id !== undefined && id !== null)
            {
                var element = document.getElementById(id);
                Common.mask(element, id + "-mask");
            }
            else
            {
                Common.mask(document.body, "body-mask");
            }
        }
        $.ajax({
            url: url,
            type: 'GET',
            data: params,
            async: async
        }).done(function (data, textStatus, request)
        {
            if (request.getResponseHeader('LoginPage') !== null)
            {
                location.reload(true);
            }
            if (typeof successHandler === "function") { successHandler(data); }
        }).fail(function (jqXHR, textStatus, errorThrown)
        {
            if (typeof errorHandler === "function") { errorHandler(errorThrown, jqXHR); }
        })
            .always(function ()
            {
                if (showMask)
                {
                    if (id !== undefined && id !== null)
                    {
                        Common.unmask(id + "-mask");
                    }
                    else
                    {
                        Common.unmask("body-mask");
                    }
                }
            });
    };
    E.ajaxGetWithHeaders = function (url, params, successHandler, errorHandler, showMask, id, headers, async) //id of the mask location
    {
        if (typeof async === "undefined" || async === null)
        {
            async = true;
        }
        if (showMask)
        {
            if (id !== undefined)
            {
                var element = document.getElementById(id);
                Common.mask(element, id + "-mask");
            }
            else
            {
                Common.mask(document.body, "body-mask");
            }
        }
        $.ajax({
            url: url,
            type: 'GET',
            data: params,
            async: async,
            headers: typeof headers !== "undefined" ? headers : ""
        }).done(function (data, textStatus, request)
        {
            if (request.getResponseHeader('LoginPage') !== null)
            {
                location.reload(true);
            } else
            {
                if (typeof successHandler === "function") { successHandler(data); }
            }
        }).fail(function (jqXHR, textStatus, errorThrown)
        {
            if (jqXHR.getResponseHeader('LoginPage') !== null || jqXHR.status === 401)
            {
                location.reload(true);
            } else
            {
                if (typeof errorHandler === "function") { errorHandler(errorThrown, jqXHR); }
            }
        }).always(function (jqXHR, textStatus)
        {
            if (showMask)
            {
                if (id !== undefined && id !== null)
                {
                    Common.unmask(id + "-mask");
                }
                else
                {
                    Common.unmask("body-mask");
                }
            }
        });
    };
    E.gridCommon = function ()
    {
        //$.fn.dataTable.ext.errMode = 'none';
        if (window.language === "ar")
        {
            $.fn.DataTable.defaults.oLanguage.sProcessing = "جارٍ التحميل...";
            $.fn.DataTable.defaults.oLanguage.sLengthMenu = "أظهر _MENU_ مدخلات";
            $.fn.DataTable.defaults.oLanguage.sZeroRecords = "لا يوجد معلومات";
            $.fn.DataTable.defaults.oLanguage.sEmptyTable = "لا يوجد معلومات";
            $.fn.DataTable.defaults.oLanguage.sInfo = "إظهار _START_ إلى _END_ من أصل _TOTAL_";
            $.fn.DataTable.defaults.oLanguage.sInfoEmpty = "يعرض 0 إلى 0 من أصل 0 سجل";
            $.fn.DataTable.defaults.oLanguage.sInfoFiltered = "(منتقاة من مجموع _MAX_ مُدخل)";
            $.fn.DataTable.defaults.oLanguage.sSearch = "ابحث:";
            $.fn.DataTable.defaults.oLanguage.oPaginate.sFirst = "الأول";
            $.fn.DataTable.defaults.oLanguage.oPaginate.sPrevious = "السابق";
            $.fn.DataTable.defaults.oLanguage.oPaginate.sNext = "التالي";
            $.fn.DataTable.defaults.oLanguage.oPaginate.sLast = "الأخير";
        }
        if (window.language === "fr")
        {
            $.fn.DataTable.defaults.oLanguage.sProcessing = "Traitement...";
            $.fn.DataTable.defaults.oLanguage.sLengthMenu = "Afficher _MENU_ éléments";
            $.fn.DataTable.defaults.oLanguage.sZeroRecords = "Aucun élément correspondant trouvé";
            $.fn.DataTable.defaults.oLanguage.sEmptyTable = "Aucune donnée disponible dans le tableau";
            $.fn.DataTable.defaults.oLanguage.sInfo = "Affichage de l'élément _START_ à _END_ sur _TOTAL_ éléments";
            $.fn.DataTable.defaults.oLanguage.sInfoEmpty = "Affichage de l'élément 0 à 0 sur 0 élément";
            $.fn.DataTable.defaults.oLanguage.sInfoFiltered = "(filtré à partir de _MAX_ éléments au total)";
            $.fn.DataTable.defaults.oLanguage.sSearch = "Rechercher :";
            $.fn.DataTable.defaults.oLanguage.oPaginate.sFirst = "Premier";
            $.fn.DataTable.defaults.oLanguage.oPaginate.sPrevious = "Précédent";
            $.fn.DataTable.defaults.oLanguage.oPaginate.sNext = "Suivant";
            $.fn.DataTable.defaults.oLanguage.oPaginate.sLast = "Dernier";
        }
        if (screen.width <= 450)
        {
            $.fn.DataTable.ext.pager.numbers_length = 4;
            $.fn.DataTable.defaults.oLanguage.oPaginate.sNext = '<i class="fa fa-angle-right"></i>';
            $.fn.DataTable.defaults.oLanguage.oPaginate.sPrevious = '<i class="fa fa-angle-left"></i>';
        }
        $.fn.DataTable.ext.errMode = function (settings, helpPage, message)
        {
            Common.showScreenErrorMsg();
        }
    };
    E.b64toBlob = function (b64Data, contentType, sliceSize)
    {
        contentType = contentType || '';
        sliceSize = sliceSize || 512;

        var byteCharacters = atob(b64Data);
        var byteArrays = [];

        for (var offset = 0; offset < byteCharacters.length; offset += sliceSize)
        {
            var slice = byteCharacters.slice(offset, offset + sliceSize);

            var byteNumbers = new Array(slice.length);
            for (var i = 0; i < slice.length; i++)
            {
                byteNumbers[i] = slice.charCodeAt(i);
            }

            var byteArray = new Uint8Array(byteNumbers);

            byteArrays.push(byteArray);
        }

        var blob = new Blob(byteArrays, { type: contentType });
        return blob;
    };
    E.dataURItoBlob = function (dataURI)
    {
        // convert base64 to raw binary data held in a string
        // doesn't handle URLEncoded DataURIs - see SO answer #6850276 for code that does this
        var byteString = atob(dataURI.split(',')[1]);

        // separate out the mime component
        var mimeString = dataURI.split(',')[0].split(':')[1].split(';')[0]

        // write the bytes of the string to an ArrayBuffer
        var ab = new ArrayBuffer(byteString.length);
        var ia = new Uint8Array(ab);
        for (var i = 0; i < byteString.length; i++)
        {
            ia[i] = byteString.charCodeAt(i);
        }

        // write the ArrayBuffer to a blob, and you're done
        var bb = new Blob([ab]);
        return bb;
    };
    E.isPlatformIOS = function ()
    {
        var iOS = /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;
        return iOS;
    };
    E.isIEBrowser = function ()
    {
        var ua = window.navigator.userAgent;
        var msie = ua.indexOf("MSIE ");
        if (msie > 0 || !!navigator.userAgent.match(/Trident.*rv\:11\./))  // If Internet Explorer, return version number
        {
            return true;
        }
        return false;
    };
    E.removeHashFromUrl = function ()
    {
        var uri = window.location.toString();
        if (uri.indexOf("#") > 0)
        {
            var clean_uri = uri.substring(0, uri.indexOf("#"));
            window.history.replaceState({}, document.title, clean_uri);
        }
    };
    E.setActiveSidebarMenu = function (id)
    {
        let collapseIn = $('.sidebar-subnav');
        collapseIn.removeClass("in");
        let active = $('.sidebar li[class="active"]');
        active.attr('class', '');
        let item = $('#' + id);
        if (item.length > 0)
        {
            item.attr('class', 'active');
            var parentOfParentElement = getParentOfParentElement($("#" + id));
            while (!parentOfParentElement.hasClass("sidebar"))
            {
                item.parent().addClass("in");
                parentOfParentElement.parent().addClass("in");
                parentOfParentElement.addClass('active');
                parentOfParentElement = getParentOfParentElement(parentOfParentElement);
            }
        }
    };
    E.reloadCurrentPage = function (delegationId)
    {
        var currentLocationHash = document.location.hash;
        if (typeof delegationId !== "undefined")
        {
            currentLocationHash = document.location.hash.split("/")[0] + "/" + delegationId;
        }
        Backbone.history.fragment = null;
        Backbone.history.navigate(currentLocationHash, true);
    };
    E.disableModelClose = function (modelId)
    {
        $(modelId).data('bs.modal').options.keyboard = false;
        $(modelId).data('bs.modal').options.backdrop = 'static';
    };
    E.enableModelClose = function (modelId)
    {
        $(modelId).data('bs.modal').options.keyboard = true;
        $(modelId).data('bs.modal').options.backdrop = 'true';
    };
    E.translate = function (keyword)
    {
        var translatedKeyword = Resources[keyword];
        if (typeof translatedKeyword === "undefined")
        {
            Common.ajaxGet('/TranslatorDictionary/Translate', { keyword: keyword }, function (data) { translatedKeyword = data; }, null, null, null, false);
            Resources[keyword] = translatedKeyword;
        }
        return translatedKeyword;
    };
    E.getRandomColor = function (index)
    {
        let predefinedColors = ['#2b957a', '#434348', '#1797be', '#7cb5ec', '#90ed7d', '#f7a35c'];
        if (index !== undefined && index < predefinedColors.length)
        {
            return predefinedColors[index];
        }
        let letters = '0123456789ABCDEF';
        let color = '#';
        for (let i = 0; i < 6; i++)
        {
            color += letters[Math.floor(Math.random() * 16)];
        }
        return color;
    };
    E.translateHighchartsExport = function ()
    {
        Highcharts.setOptions({
            lang: {
                viewFullscreen: Resources.ViewFullscreen,
                printChart: Resources.PrintChart,
                downloadPNG: Resources.DownloadPNG,
                downloadPDF: Resources.DownloadPDF,
                downloadSVG: Resources.DownloadSVG,
                downloadCSV: Resources.DownloadCSV,
                downloadXLS: Resources.DownloadXLS,
                downloadJPEG: Resources.DownloadJPEG,
                viewData: Resources.ViewData,
                exitFullscreen: Resources.ExitFullscreen
            }
        });
    };
    E.ajaxDelete = function (url, params, successHandler, errorHandler, showMask, id) //id of the mask location
    {
        if (showMask)
        {
            if (id !== undefined && id !== null)
            {
                var element = document.getElementById(id);
                Common.mask(element, id + "-mask");
            }
            else
            {
                Common.mask(document.body, "body-mask");
            }
        }
        $.ajax({
            url: url,
            type: 'DELETE',
            data: params
        }).done(function (data, textStatus, request)
        {
            if (request.getResponseHeader('LoginPage') !== null)
            {
                location.reload(true);
            }
            if (typeof successHandler === "function") { successHandler(data); }
        }).fail(function (jqXHR, textStatus, errorThrown)
        {
            if (typeof errorHandler === "function") { errorHandler(errorThrown, jqXHR); }
        })
            .always(function ()
            {
                if (showMask)
                {
                    if (id !== undefined && id !== null)
                    {
                        Common.unmask(id + "-mask");
                    }
                    else
                    {
                        Common.unmask("body-mask");
                    }
                }
            });
    };
    E.removeUnUsedDomElementsOnRoute = function ()
    {
        GridCommon.Destroy();
        $('.sweet-alert').remove();
        $('.sweet-overlay').remove();
        $('.modal-backdrop').remove();
        $('body').removeClass('modal-open');
    };
    E.ajaxPostWithHeaders = function (url, params, successHandler, errorHandler, showMask, id, headers, async) //id of the mask location
    {
        if (typeof async === "undefined" || async === null)
        {
            async = true;
        }
        if (showMask)
        {
            if (id !== undefined && id !== null)
            {
                var element = document.getElementById(id);
                Common.mask(element, id + "-mask");
            }
            else
            {
                Common.mask(document.body, "body-mask");
            }
        }
        $.ajax({
            url: url,
            type: 'POST',
            data: params,
            headers: typeof headers !== "undefined" ? headers : "",
            async: async,
        }).done(function (data, textStatus, request)
        {
            if (request.getResponseHeader('LoginPage') !== null)
            {
                location.reload(true);
            }
            if (typeof successHandler === "function") { successHandler(data); }
        }).fail(function (jqXHR, textStatus, errorThrown)
        {
            if (typeof errorHandler === "function") { errorHandler(errorThrown, jqXHR); }
        })
            .always(function ()
            {
                if (showMask)
                {
                    if (id !== undefined && id !== null)
                    {
                        Common.unmask(id + "-mask");
                    }
                    else
                    {
                        Common.unmask("body-mask");
                    }
                }
            });
    };
    E.ajaxPostJSON = function (url, params, successHandler, errorHandler, showMask, id) //id of the mask location
    {
        if (showMask)
        {
            if (id !== undefined && id !== null)
            {
                var element = document.getElementById(id)
                    ;
                Common.mask(element, id + "-mask");
            }
            else
            {
                Common.mask(document.body, "body-mask");
            }
        }
        $.ajax({
            url: url,
            type: 'POST',
            data: params,
            contentType: "application/json; charset=utf-8",
            dataType: "json",
        }).done(function (data, textStatus, request)
        {
            if (request.getResponseHeader('LoginPage') !== null)
            {
                location.reload(true);
            }
            if (typeof successHandler === "function") { successHandler(data); }
        }).fail(function (jqXHR, textStatus, errorThrown)
        {
            if (typeof errorHandler === "function") { errorHandler(errorThrown, jqXHR); }
        })
            .always(function ()
            {
                if (showMask)
                {
                    if (id !== undefined && id !== null)
                    {
                        Common.unmask(id + "-mask");
                    }
                    else
                    {
                        Common.unmask("body-mask");
                    }
                }
            });
    };
    E.sanatize = function (text)
    {
        return text.replace(/"/gi, '&quot;').replace(/</gi, '&lt;').replace(/>/gi, '&gt;');
    };
    E.sanatizeTree = function (data)
    {
        for (var i = 0; i < data.length; i++)
        {
            data[i].text = Common.sanatize(data[i].text);
            if (data[i].children && data[i].children.length > 0)
            {
                data[i].children = Common.sanatizeTree(data[i].children);
            }
        }
        return data;
    };
    E.ajaxError = function (jqXHR, status, error, errorHandler)
    {
        if (jqXHR.getResponseHeader('LoginPage') !== null)
        {
            location.reload(true);
        }
        Common.showScreenErrorMsg();
        if (typeof errorHandler === "function") { errorHandler(jqXHR.responseJSON); }
    };
    E.getMonths = function ()
    {
        var months = [Resources.January, Resources.February, Resources.March, Resources.April, Resources.May, Resources.June, Resources.July, Resources.August, Resources.September, Resources.October, Resources.November, Resources.December];
        if (window.CalendarType && window.CalendarType !== window.CalendarTypes.None)
        {
            months = [Resources.Muharram, Resources.Safar, Resources.RabiAlAwwal, Resources.RabiAthani, Resources.JumadaAlAwwal, Resources.JumadaAthani, Resources.Rajab, Resources.Shaban, Resources.Ramadan, Resources.Shawwal, Resources.DhuAlQadah, Resources.DhuAlHijjan];
        }
        return months;
    };
    return E;
}(Common));
$(document).ready(function ()
{
    if (window.language === 'ar')
    {
        flatpickr.localize(flatpickr.l10ns.ar);
    } else
    {
        flatpickr.localize(flatpickr.l10ns.fr);
    }
    flatpickr(".flatpickr-input");
    if (window.language === 'ar')
    {
        flatpickr.setDefaults(
            {
                position: "auto right"
            }
        );
    }
    $(document).on('show.bs.modal', '.modal', function ()
    {
        var zIndex = 1050 + 10 * $('.modal:visible').length;
        $(this).css('z-index', zIndex);
        setTimeout(function ()
        {
            $('.modal-backdrop').not('.modal-stack').css('z-index', zIndex - 1).addClass('modal-stack');
        }, 0);
    });
    Handlebars.registerHelper('Localizer', function (str)
    {
        return (Resources[str] !== undefined ? Resources[str] : str);
    });
    Handlebars.registerHelper('Format', function (str, array)
    {
        array = eval(array);
        var retValue = str;
        for (var i = 0; i < array.length; i++)
        {
            var reg = new RegExp("\\{" + i + "\\}", "gm");
            retValue = retValue.replace(reg, array[i]);
        }
        return retValue;
    });
    Handlebars.registerHelper('ifEquals', function (arg1, arg2, options)
    {
        return (arg1 === arg2) ? options.fn(this) : options.inverse(this);
    });
});
function getParentOfParentElement(parentOfParentElement)
{
    return $(parentOfParentElement.parent().parent());
}
var isMobile = {
    Android: function ()
    {
        return navigator.userAgent.match(/Android/i);
    },
    BlackBerry: function ()
    {
        return navigator.userAgent.match(/BlackBerry/i);
    },
    iOS: function ()
    {
        return navigator.userAgent.match(/iPhone|iPad|iPod/i);
    },
    Opera: function ()
    {
        return navigator.userAgent.match(/Opera Mini/i);
    },
    Windows: function ()
    {
        return navigator.userAgent.match(/IEMobile/i);
    },
    any: function ()
    {
        return (isMobile.Android() || isMobile.BlackBerry() || isMobile.iOS() || isMobile.Opera() || isMobile.Windows());
    }
};
var TypeMenu = (function (E)
{
    E = {};
    E.TopNavBar = "1";
    E.SideBar = "2";
    return E;
}(TypeMenu));
var TypeAction = (function (E)
{
    E = {};
    E.Row = "1";
    E.Toolbar = "2";
    E.Context = "3";
    E.Tab = "4";
    return E;
}(TypeAction));
var MenuType = (function (E)
{
    E = {};
    E.Url = "1";
    E.JsFunction = "2";
    E.Html = "3";
    return E;
}(MenuType));
var UrlType = (function (E)
{
    E = {};
    E.Target = "1";
    E.Self = "2";
    E.Frame = "3";
    E.Modal = "4";
    return E;
}(UrlType));
var TabType = (function (E)
{
    E = {};
    E.Url = "1";
    E.JavascriptFunction = "2";
    E.HTML = "3";
    return E;
}(TabType));
var MenuSecurityActions = (function (E)
{
    E = {};
    E.renderHtmlToWrapper = function (id, title, openInModal)
    {
        var htmlContent = $("#" + id).val();
        let wrapper = null;
        if (openInModal)
        {
            wrapper = document.getElementsByClassName('modal-window')[0];
            MenuSecurityActions.buildModalWindow(wrapper, htmlContent, title);
        } else
        {
            wrapper = document.getElementsByClassName('content-wrapper')[0];
            if (title)
            {
                MenuSecurityActions.buildHtmlPage(wrapper, htmlContent, title);
            } else
            {
                wrapper.innerHTML = "";
                wrapper.innerHTML = htmlContent;
            }
        }
    }
    E.buildFromUrlCall = function (url, title, openInModal, openInIframe, iframeHeight)
    {
        if (!openInIframe)
        {
            Common.ajaxGet(url, null, function (response)
            {
                let wrapper = null;
                if (openInModal)
                {
                    wrapper = document.getElementsByClassName('modal-window')[0];
                    MenuSecurityActions.buildModalWindow(wrapper, response, title);
                } else
                {
                    wrapper = document.getElementsByClassName('content-wrapper')[0];
                    if (title)
                    {
                        MenuSecurityActions.buildHtmlPage(wrapper, response, title);
                    } else
                    {
                        wrapper.innerHTML = "";
                        wrapper.innerHTML = response;
                    }
                }
            });
        } else
        {
            let wrapper = null;
            iframeHeight = iframeHeight !== '' ? iframeHeight : "100%";
            if (openInModal)
            {
                wrapper = document.getElementsByClassName('modal-window')[0];
                MenuSecurityActions.buildModalWindowWithIframe(wrapper, url, title, iframeHeight);
            } else
            {
                wrapper = document.getElementsByClassName('content-wrapper')[0];
                MenuSecurityActions.buildHtmlPageWithIframe(wrapper, url, title, iframeHeight);
            }
        }
    }
    E.buildModalWindow = function (div, html, title)
    {
        var modalDiv = document.createElement('div');
        modalDiv.setAttribute("id", "modal");
        modalDiv.setAttribute("tabindex", "-1");
        modalDiv.setAttribute("role", "dialog");
        modalDiv.setAttribute("aria-hidden", "true");
        modalDiv.setAttribute("class", "modal fade");

        var modalDialogDiv = document.createElement('div');
        modalDialogDiv.setAttribute("class", "modal-dialog modal-xl");

        var modalContentDiv = document.createElement('div');
        modalContentDiv.setAttribute("class", "modal-content");

        var modalHeaderDiv = document.createElement('div');
        modalHeaderDiv.setAttribute("class", "modal-header");

        var modalHeaderButton = document.createElement('button');
        modalHeaderButton.setAttribute("type", "button");
        modalHeaderButton.setAttribute("class", "close");
        modalHeaderButton.setAttribute("data-dismiss", "modal");
        modalHeaderButton.innerHTML = "&times;";

        var modalHeaderTitle = document.createElement('h4');
        modalHeaderTitle.setAttribute("class", "modal-title");
        modalHeaderTitle.innerHTML = title;

        var modalBodyDiv = document.createElement('div');
        modalBodyDiv.setAttribute("class", "modal-body");
        modalBodyDiv.innerHTML = html;

        modalHeaderDiv.appendChild(modalHeaderButton);
        if (title)
        {
            modalHeaderDiv.appendChild(modalHeaderTitle);
        }

        modalContentDiv.appendChild(modalHeaderDiv);
        modalContentDiv.appendChild(modalBodyDiv);

        modalDialogDiv.appendChild(modalContentDiv);
        modalDiv.appendChild(modalDialogDiv);

        div.appendChild(modalDiv);
        $('#modal').modal('show');
        $("#modal").off("hidden.bs.modal");
        $("#modal").off("shown.bs.modal");
        $('#modal').on('hidden.bs.modal', function ()
        {
            $('#modal').remove();
        });
    }
    E.buildHtmlPage = function (div, html, title)
    {
        div.innerHTML = "";
        var header = document.createElement('h3');
        header.innerHTML = title;

        var rowDiv = document.createElement('div');
        rowDiv.setAttribute("class", "row");

        var colDiv = document.createElement('div');
        colDiv.setAttribute("class", "col-md-12");
        colDiv.innerHTML = html;

        rowDiv.appendChild(colDiv);

        div.appendChild(header);
        div.appendChild(rowDiv);
    }
    E.buildModalWindowWithIframe = function (div, src, title, iframeHeight)
    {
        var modalDiv = document.createElement('div');
        modalDiv.setAttribute("id", "modal");
        modalDiv.setAttribute("tabindex", "-1");
        modalDiv.setAttribute("role", "dialog");
        modalDiv.setAttribute("aria-hidden", "true");
        modalDiv.setAttribute("class", "modal fade");

        var modalDialogDiv = document.createElement('div');
        modalDialogDiv.setAttribute("class", "modal-dialog modal-xl");

        var modalContentDiv = document.createElement('div');
        modalContentDiv.setAttribute("class", "modal-content");

        var modalHeaderDiv = document.createElement('div');
        modalHeaderDiv.setAttribute("class", "modal-header");

        var modalHeaderButton = document.createElement('button');
        modalHeaderButton.setAttribute("type", "button");
        modalHeaderButton.setAttribute("class", "close");
        modalHeaderButton.setAttribute("data-dismiss", "modal");
        modalHeaderButton.innerHTML = "&times;";

        var modalHeaderTitle = document.createElement('h4');
        modalHeaderTitle.setAttribute("class", "modal-title");
        modalHeaderTitle.innerHTML = title;

        var modalBodyDiv = document.createElement('div');
        modalBodyDiv.setAttribute("class", "modal-body");
        modalBodyDiv.setAttribute("style", "padding:0px");

        var modalBodyIframe = document.createElement('iframe');
        modalBodyIframe.setAttribute("style", "width:100%;height:" + iframeHeight + ";border:0;");
        modalBodyIframe.src = src;

        modalBodyDiv.appendChild(modalBodyIframe);

        modalHeaderDiv.appendChild(modalHeaderButton);
        if (title)
        {
            modalHeaderDiv.appendChild(modalHeaderTitle);
        }

        modalContentDiv.appendChild(modalHeaderDiv);
        modalContentDiv.appendChild(modalBodyDiv);

        modalDialogDiv.appendChild(modalContentDiv);
        modalDiv.appendChild(modalDialogDiv);

        div.appendChild(modalDiv);
        $('#modal').modal('show');
        $("#modal").off("hidden.bs.modal");
        $("#modal").off("shown.bs.modal");
        $('#modal').on('hidden.bs.modal', function ()
        {
            $('#modal').remove();
        });
    }
    E.buildHtmlPageWithIframe = function (div, src, title, iframeHeight)
    {
        div.innerHTML = "";
        var header = document.createElement('h3');
        header.innerHTML = title;

        var rowDiv = document.createElement('div');
        rowDiv.setAttribute("class", "row");

        var colDiv = document.createElement('div');
        colDiv.setAttribute("class", "col-md-12");

        var divIframe = document.createElement('iframe');
        divIframe.setAttribute("style", "width:100%;height:" + iframeHeight + ";border:0;");
        divIframe.src = src;

        colDiv.appendChild(divIframe);
        rowDiv.appendChild(colDiv);

        div.appendChild(header);
        div.appendChild(rowDiv);
    }
    return E;
}(MenuSecurityActions));
var SecurityMatrix = (function (E)
{
    function getToolbarSubActions(divId, actions, id)
    {
        var buttons = [];
        var subactions = $.grep(actions, function (element)
        {
            return element.ParentActionId === id;
        });
        if (subactions.length > 0)
        {
            for (var j = 0; j < subactions.length; j++)
            {
                var subaction = subactions[j];
                let tooltipAttr = subaction.Tooltip !== null ? subaction.Tooltip : '';
                var subbutton = {
                    text: (subaction.Icon ? '<i class="fa ' + subaction.Icon + ' mr-sm"></i>' : '') + '<span data-color="' + subaction.Color + '">' + subaction.Name + '</span>',
                    titleAttr: tooltipAttr,
                    attr: {
                        divId: divId,
                        function: subaction.JsFunction,
                        id: subaction.Id
                    }
                };
                var subbuttons = getToolbarSubActions(divId, actions, subaction.Id);
                if (subbuttons.length > 0)
                {
                    subbutton.extend = 'collection';
                    subbutton.buttons = subbuttons;
                } else
                {
                    subbutton.action = toolBarCallBack;
                }
                buttons.push(subbutton);
            }
        }
        return buttons;
    }
    function getContextSubActions(actions, items, id)
    {
        var subactions = $.grep(actions, function (element)
        {
            return element.ParentActionId === id;
        });
        if (subactions.length > 0)
        {
            items[id].items = {};
            for (var j = 0; j < subactions.length; j++)
            {
                var subaction = subactions[j];
                let tooltipAttr = subaction.Tooltip !== null ? subaction.Tooltip : '';
                items[id].items[subaction.Id] = {
                    name: "<span class='customConext' data-function='" + subaction.JsFunction + "' title='" + tooltipAttr + "'>" + subaction.Name + "</span>",
                    isHtmlName: true,
                    order: subaction.Order,
                    icon: subaction.Icon !== "" && subaction.Icon !== null ? subaction.Icon.replace("fa ", "") : "",
                    function: subaction.JsFunction,
                    callback: function (key, options, e)
                    {
                        var jsfunction = $(e.target).find(".customConext").data("function");
                        let tr = $(this).closest('tr');
                        let table = $(this).parents("table").attr("id");
                        let srow = $('#' + table).DataTable().row(tr);
                        var data = srow.data();
                        if (jsfunction)
                        {
                            jsfunction = jsfunction.split("(")[0];
                            eval(jsfunction + "(" + JSON.stringify(data) + ")");
                        }
                    }
                };
                getContextSubActions(actions, items[id].items, subaction.Id);
                optionSortedArray = Object.keys(items[id].items).sort(function (a, b) { return items[id].items[a].order - items[id].items[b].order }).map(function (key) { return items[id].items[key] });
                items[id].items = {};
                for (var i = 0; i < optionSortedArray.length; i++)
                {
                    items[id].items[i] = optionSortedArray[i];
                }
            }
        }
    }
    function getRowSubActions(actions, item, id)
    {
        var subactions = $.grep(actions, function (element)
        {
            return element.ParentActionId === id;
        });
        if (subactions.length > 0)
        {
            item.items = {};
            for (var j = 0; j < subactions.length; j++)
            {
                var subaction = subactions[j];
                let tooltipAttr = subaction.Tooltip !== null ? subaction.Tooltip : '';
                item.items[subaction.Id] = {
                    name: "<span class='customConext' data-function='" + subaction.JsFunction + "' title='" + tooltipAttr + "'>" + subaction.Name + "</span>",
                    isHtmlName: true,
                    order: subaction.Order,
                    icon: subaction.Icon !== "" && subaction.Icon !== null ? subaction.Icon.replace("fa ", "") : "",
                    function: subaction.JsFunction,
                    callback: function (key, options, e)
                    {
                        var jsfunction = $(e.target).find(".customConext").data("function");
                        let tr = $(this).closest('tr');
                        let table = $(this).parents("table").attr("id");
                        let srow = $('#' + table).DataTable().row(tr);
                        var data = srow.data();
                        if (jsfunction)
                        {
                            jsfunction = jsfunction.split("(")[0];
                            eval(jsfunction + "(" + JSON.stringify(data) + ")");
                        }
                    }
                };
                getRowSubActions(actions, item.items[subaction.Id], subaction.Id);
            }
            optionSortedArray = Object.keys(item.items).sort(function (a, b) { return item.items[a].order - item.items[b].order }).map(function (key) { return item.items[key] });
            item.items = {};
            for (var i = 0; i < optionSortedArray.length; i++)
            {
                item.items[i] = optionSortedArray[i];
            }
        }
    }
    function getTabSubActions(actions, item, id)
    {
        var subactions = $.grep(actions, function (element)
        {
            return element.ParentActionId === id;
        });
        if (subactions.length > 0)
        {
            item.items = {};
            for (var j = 0; j < subactions.length; j++)
            {
                var subaction = subactions[j];
                let tooltipAttr = subaction.Tooltip !== null ? subaction.Tooltip : '';
                item.items[subaction.Id] = {
                    name: "<span class='customConext' data-function='" + subaction.JsFunction + "' title='" + tooltipAttr + "'>" + subaction.Name + "</span>",
                    isHtmlName: true,
                    order: subaction.Order,
                    icon: subaction.Icon !== "" && subaction.Icon !== null ? subaction.Icon.replace("fa ", "") : "",
                    function: subaction.JsFunction,
                    callback: function (key, options, e)
                    {
                        var jsfunction = $(e.target).find(".customConext").data("function");
                        eval(jsfunction);
                    }
                };
                getTabSubActions(actions, item.items[subaction.Id], subaction.Id);
            }
            optionSortedArray = Object.keys(item.items).sort(function (a, b) { return item.items[a].order - item.items[b].order }).map(function (key) { return item.items[key] });
            item.items = {};
            for (var i = 0; i < optionSortedArray.length; i++)
            {
                item.items[i] = optionSortedArray[i];
            }
        }
    }
    function searchObject(list, key)
    {
        var retValue = null;
        if (typeof list !== "undefined" && list[key])
        {
            retValue = list[key];
        } else
        {
            for (item in list)
            {
                retValue = searchObject(list[item].items, key);
            }
        }
        return retValue;
    }
    var toolBarCallBack = function (e, dt, node, config)
    {
        var ids = GridCommon.GetSelectedRows(config.attr.divId);
        if (ids.length > 0)
        {
            if (config.attr.function)
            {
                var jsfunction = config.attr.function.split("(")[0];
                eval(jsfunction + "(" + JSON.stringify(ids) + ")");
            }
        }
        else
        {
            Common.alertMsg(Resources.NoRowSelected);
        }
    };
    E = {};
    E.CategoryIdPropertyName = "categoryId";
    E.Menus = "1";
    E.Actions = "2";
    E.Tabs = "3";
    E.Nodes = "4";
    E.InitToolbarColor = function ()
    {
        $("[data-color]").each(function ()
        {
            var color = $(this).data("color");
            $(this).parents(".btn-default").attr("style", "background-color:" + color);
        });
    };
    E.InitContextMenu = function (securityMatrix, node)
    {
        $.contextMenu('destroy', '.custom-tab-action');
        $.contextMenu('destroy', '.dataTable td');
        $.contextMenu('destroy', '.row-action');
        $.contextMenu({
            selector: '.dataTable td',
            trigger: isMobile.any() ? 'left' : 'right',
            build: function ($trigger, e)
            {
                if (!$trigger.hasClass("hasChildren"))
                {
                    let tr = $trigger.closest('tr');
                    let table = $trigger.parents("table").attr("id");
                    let srow = $('#' + table).DataTable().row(tr);
                    var data = srow.data();
                    if (data && typeof data[E.CategoryIdPropertyName] !== "undefined")
                    {
                        var options = { items: {} };
                        SecurityMatrix.getContextActions(securityMatrix, options, node, data[E.CategoryIdPropertyName]);
                        return !jQuery.isEmptyObject(options.items) ? options : false;
                    }
                }
                return false;
            },
            rtl: true
        });
        $.contextMenu({
            selector: '.row-action',
            trigger: 'left',
            build: function ($trigger, e)
            {
                let id = $trigger.data("id");
                let tr = $trigger.closest('tr');
                let table = $trigger.parents("table").attr("id");
                let srow = $('#' + table).DataTable().row(tr);
                var data = srow.data();
                if (id)
                {
                    var options = { items: {} };
                    SecurityMatrix.getRowSubActions(securityMatrix, options, node, data[E.CategoryIdPropertyName], id);
                    return !jQuery.isEmptyObject(options.items) ? options : false;
                }
                return false;
            }
        });
    };
    E.InitTabContextMenu = function (actions)
    {
        $.contextMenu('destroy', '.custom-tab-action');
        $.contextMenu({
            selector: '.custom-tab-action',
            trigger: 'left',
            build: function ($trigger, e)
            {
                let id = $trigger.data("id");
                if (id)
                {
                    var options = { items: {} };
                    SecurityMatrix.getTabSubActions(actions, options, id);
                    return options;
                }
                return false;
            }
        });
    };
    E.getToolbarActions = function (securityMatrix, node, divId)
    {
        var buttons = [];
        var actions = $.grep(securityMatrix.SecurityNodes[Number(node)].Actions, function (element, index)
        {
            return element.Type === Number(TypeAction.Toolbar);
        });
        for (var i = 0; i < actions.length; i++)
        {
            var action = actions[i];
            if (action.ParentActionId === null)
            {
                let tooltipAttr = action.Tooltip !== null ? action.Tooltip : '';

                var button = {
                    className: 'btn-sm btn-primary',
                    text: (action.Icon ? '<i class="fa ' + action.Icon + ' mr-sm"></i>' : '') + '<span data-color="' + action.Color + '">' + action.Name + '</span>',
                    titleAttr: tooltipAttr,
                    attr: {
                        divId: divId,
                        function: action.JsFunction,
                        id: action.Id
                    }
                };
                var subbuttons = getToolbarSubActions(divId, actions, action.Id);
                if (subbuttons.length > 0)
                {
                    button.extend = 'collection';
                    button.buttons = subbuttons;
                } else
                {
                    button.action = toolBarCallBack;
                }
                buttons.push(button);
            }
        }
        return buttons;
    };
    E.getRowActions = function (securityMatrix, columns, node)
    {
        var actions = $.grep(securityMatrix.SecurityNodes[Number(node)].Actions, function (element, index)
        {
            return element.Type === Number(TypeAction.Row);
        });
        for (var i = 0; i < actions.length; i++)
        {
            var action = actions[i];
            if (action.ParentActionId === null)
            {
                let tooltipAttr = action.Tooltip !== null ? action.Tooltip : '';
                let btn = document.createElement("button");
                btn.setAttribute("title", tooltipAttr);
                btn.setAttribute("data-id", action.Id);
                btn.innerHTML = "<i class='fa " + (action.Icon !== "" ? action.Icon : "fa-ellipsis-h") + " fa-white'></i> ";// + action.Name;
                var subactions = $.grep(actions, function (element)
                {
                    return element.ParentActionId === action.Id;
                });
                var className = "text-right";
                if (action.Color)
                {
                    btn.setAttribute("style", "background-color:" + action.Color + ";border-color:" + action.Color);
                }
                if (subactions.length > 0)
                {
                    className += " hasChildren";
                    btn.setAttribute("class", "btn btn-xs btn-primary row-action");
                } else
                {
                    btn.setAttribute("class", "btn btn-xs btn-primary custom-action");
                    btn.setAttribute("clickattr", action.JsFunction);
                }
                columns.push({
                    "actionName": action.Name,
                    "className": className,
                    "autoWidth": false,
                    "bAutoWidth": false,
                    width: "16px",
                    'orderable': false,
                    'sortable': false,
                    'defaultContent': btn.outerHTML,
                    "createdCell": function (td, cellData, rowData, row, col)
                    {
                        var button = td.children[0];
                        var id = button !== null && typeof button !== "undefined" ? $(button).attr("data-id") : "0";
                        var actions = $.grep(securityMatrix.SecurityNodes[Number(node)].SecurityCategories[Number(rowData[E.CategoryIdPropertyName])].Actions,
                            function (element)
                            {
                                return element.Type === Number(TypeAction.Row) && element.Id === Number(id);
                            });
                        if (actions.length === 0)
                        {
                            $(td).empty();
                        }
                    }
                });
            }
        }
        $(document).off("click", ".custom-action");
        $(document).on("click", ".custom-action", function ()
        {
            var onclick = $(this).attr("clickattr");
            let tr = $(this).closest('tr');
            let table = $(this).parents("table").attr("id");
            let srow = $('#' + table).DataTable().row(tr);
            var data = srow.data();
            if (onclick)
            {
                var jsfunction = onclick.split("(")[0];
                eval(jsfunction + "(" + JSON.stringify(data) + ")");
            }
        });
        return columns;
    };
    E.getRowSubActions = function (securityMatrix, options, node, categoryId, id)
    {
        var actions = $.grep(securityMatrix.SecurityNodes[Number(node)].SecurityCategories[Number(categoryId)].Actions,
            function (element)
            {
                return element.Type === Number(TypeAction.Row);
            });
        getRowSubActions(actions, options, id);
        return options;
    };
    E.getTabSubActions = function (actions, options, id)
    {
        getTabSubActions(actions, options, id);
        return options;
    };
    E.getContextActions = function (securityMatrix, options, node, categoryId)
    {
        var actions = $.grep(securityMatrix.SecurityNodes[Number(node)].SecurityCategories[Number(categoryId)].Actions,
            function (element)
            {
                return element.Type === Number(TypeAction.Context);
            });
        for (var i = 0; i < actions.length; i++)
        {
            var action = actions[i];
            if (action.ParentActionId === null)
            {
                let tooltipAttr = action.Tooltip !== null ? action.Tooltip : '';
                options.items[action.Id] = {
                    name: "<span class='customConext' data-function='" + action.JsFunction + "' title='" + tooltipAttr + "'>" + action.Name + "</span>",
                    isHtmlName: true,
                    order: action.Order,
                    icon: action.Icon !== "" && action.Icon !== null ? action.Icon.replace("fa ", "") : "fa-ellipsis-v",
                    function: action.JsFunction,
                    callback: function (key, options, e)
                    {
                        var jsfunction = $(e.target).find(".customConext").data("function");
                        let tr = $(this).closest('tr');
                        let table = $(this).parents("table").attr("id");
                        let srow = $('#' + table).DataTable().row(tr);
                        var data = srow.data();
                        if (jsfunction)
                        {
                            jsfunction = jsfunction.split("(")[0];
                            eval(jsfunction + "(" + JSON.stringify(data) + ")");
                        }
                    }
                };
                getContextSubActions(actions, options.items, action.Id);
            }
        }
        optionSortedArray = Object.keys(options.items).sort(function (a, b) { return options.items[a].order - options.items[b].order }).map(function (key) { return options.items[key] });
        options.items = {};
        for (var i = 0; i < optionSortedArray.length; i++)
        {
            options.items[i] = optionSortedArray[i];
        }
        return options;
    };
    E.getTabActions = function (actions, divId, hasContainer)
    {
        let buttonsDiv = document.createElement("div");
        buttonsDiv.setAttribute("class", "html5buttons btn-group");
        for (var i = 0; i < actions.length; i++)
        {
            var action = actions[i];
            if (action.ParentActionId === null)
            {
                let className = 'btn-primary';
                let tooltipAttr = action.Tooltip !== null ? action.Tooltip : '';
                let btn = document.createElement("button");
                btn.setAttribute("title", tooltipAttr);
                btn.innerHTML = "<i class='fa " + (action.Icon !== "" ? action.Icon : "fa-ellipsis-h") + " fa-white'></i> " + action.Name;
                var subactions = $.grep(actions, function (element)
                {
                    return element.ParentActionId === action.Id;
                });
                if (action.Color)
                {
                    btn.setAttribute("style", "background-color:" + action.Color + ";border-color:" + action.Color);
                    action.Color !== window.DefaultColor ? className = 'btn-info' : className = 'btn-primary';
                }
                if (subactions.length > 0)
                {
                    btn.setAttribute("data-id", action.Id);
                    btn.setAttribute("class", "btn btn-sm " + className + " custom-tab-action custom-toolbar");
                } else
                {
                    btn.setAttribute("class", "btn btn-sm " + className + " custom-action custom-toolbar");
                    btn.setAttribute("onclick", action.JsFunction);
                }
                if (!hasContainer)
                {
                    if (divId === "#myTaskActions")
                    {
                        $(divId).prepend(btn);
                    } else
                    {
                        buttonsDiv.appendChild(btn);
                    }
                }
                else
                {
                    $(divId).prepend(btn);
                }
            }
        }
        if (divId !== "#myTaskActions")
        {
            if (actions.length > 0)
            {
                if (!hasContainer)
                {
                    $(divId).prepend(buttonsDiv);
                }
            }
        }
    };
    return E;
}(SecurityMatrix));

//use refs instead of id
var GridCommonCore = (function (E)
{
    E = {};
    E.GridsObject = {};
    E.GridsWholeObject = {};
    E.AddCheckBoxEvents = function (self, gridname)
    {
        GridCommonCore.Clear(gridname);
        $($(self.refs[gridname]).find('tbody')).on('click', 'tr', function ()
        {
            var input = this.getElementsByTagName('input')[0];
            if (typeof input !== "undefined")
            {
                input.checked = input.checked ? false : true;
                var id = input.getAttribute("data-id");
                let tr = $(this).closest('tr');
                let rowData = $(self.refs[gridname]).DataTable().row(tr).data();
                if (input.checked)
                {
                    GridCommonCore.Add(gridname, id);
                    GridCommonCore.AddWholeRow(gridname, rowData);
                } else
                {
                    GridCommonCore.Remove(gridname, id);
                    GridCommonCore.RemoveWholeRow(gridname, rowData);
                }
            }
        });
        $($(self.refs[gridname]).find('#chkAll')).change(function ()
        {
            var checked = $(this).prop('checked');
            $($(self.refs[gridname]).find('tbody tr td input[type="checkbox"]')).prop('checked', checked);
            $($(self.refs[gridname]).find('tbody tr td input[type="checkbox"]')).each(function ()
            {
                var id = this.getAttribute("data-id");
                let tr = $(this).closest('tr');
                let rowData = $(self.refs[gridname]).DataTable().row(tr).data();
                if (checked)
                {
                    GridCommonCore.Add(gridname, id);
                    GridCommonCore.AddWholeRow(gridname, rowData);
                } else
                {
                    GridCommonCore.Remove(gridname, id);
                    GridCommonCore.RemoveWholeRow(gridname, rowData);
                }
            });
        });
        $($(self.refs[gridname]).find('tbody')).on('change', 'tr td input[type="checkbox"]', function ()
        {
            var checked = $(this).prop('checked');
            var id = this.getAttribute("data-id");
            let tr = $(this).closest('tr');
            let rowData = $(self.refs[gridname]).DataTable().row(tr).data();
            if (checked)
            {
                GridCommonCore.Add(gridname, id);
                GridCommonCore.AddWholeRow(gridname, rowData);
            } else
            {
                GridCommonCore.Remove(gridname, id);
                GridCommonCore.RemoveWholeRow(gridname, rowData);
            }
        });
    };
    E.Remove = function (gridname, id)
    {
        var selectedRows = GridCommonCore.GetSelectedRows(gridname);
        const index = selectedRows.indexOf(id);
        if (index > -1)
        {
            selectedRows.splice(index, 1);
            GridCommonCore.SetSelectedRows(gridname, selectedRows);
        }
    };
    E.Add = function (gridname, id)
    {
        var selectedRows = GridCommonCore.GetSelectedRows(gridname);
        const index = selectedRows.indexOf(id);
        if (index === -1)
        {
            selectedRows.push(id);
            GridCommonCore.SetSelectedRows(gridname, selectedRows);
        }
    };
    E.SetSelectedRows = function (gridname, selectedRows)
    {
        GridCommonCore.GridsObject[gridname] = selectedRows;
    };
    E.GetSelectedRows = function (gridname)
    {
        if (!GridCommonCore.GridsObject[gridname])
        {
            GridCommonCore.GridsObject[gridname] = [];
        }
        return GridCommonCore.GridsObject[gridname];
    };
    E.CheckSelectedRows = function (self, gridname)
    {
        var selectedRows = GridCommonCore.GetSelectedRows(gridname);
        $($(self.refs[gridname]).find('tbody')).find('input[type="checkbox"]').each(function (index, obj)
        {
            var id = obj.getAttribute('data-id');
            if (selectedRows.includes(id))
            {
                obj.setAttribute("checked", "checked");
            }
        });
    };
    E.Clear = function (gridname)
    {
        GridCommonCore.GridsObject[gridname] = [];
        GridCommonCore.GridsWholeObject[gridname] = [];
    };
    E.Refresh = function (self, gridname)
    {
        $(self.refs[gridname]).DataTable().ajax.reload();
        GridCommonCore.Clear(gridname);
    };
    E.RefreshCurrentPage = function (self, gridname, triggerBackBtn)
    {
        $(self.refs[gridname]).DataTable().ajax.reload(null, false);
        if (triggerBackBtn)
        {
            $('.btn-back').click();
        }
    };
    E.Destroy = function ()
    {
        GridCommonCore.GridsObject = {}
        GridCommonCore.GridsWholeObject = {}
    };
    E.RemoveWholeRow = function (gridname, row)
    {
        var selectedRows = GridCommonCore.GetWholeSelectedRows(gridname);
        const index = selectedRows.indexOf(row);
        if (index > -1)
        {
            selectedRows.splice(index, 1);
            GridCommonCore.SetWholeSelectedRows(gridname, selectedRows);
        }
    };
    E.AddWholeRow = function (gridname, row)
    {
        var selectedRows = GridCommonCore.GetWholeSelectedRows(gridname);
        const index = selectedRows.indexOf(row);
        if (index === -1)
        {
            selectedRows.push(row);
            GridCommonCore.SetWholeSelectedRows(gridname, selectedRows);
        }
    };
    E.SetWholeSelectedRows = function (gridname, selectedRows)
    {
        GridCommonCore.GridsWholeObject[gridname] = selectedRows;
    };
    E.GetWholeSelectedRows = function (gridname)
    {
        if (!GridCommonCore.GridsWholeObject[gridname])
        {
            GridCommonCore.GridsWholeObject[gridname] = [];
        }
        return GridCommonCore.GridsWholeObject[gridname];
    };
    return E;
}(GridCommonCore));
var AddressBookMode = (function (E)
{
    E = {};
    E.Internal = "1";
    E.External = "2";
    E.InternalWithUsers = "3";
    return E;
}(AddressBookMode));
var AddressBookSelectionMode = (function (E)
{
    E = {};
    E.Single = 1;
    E.Multiple = 2;
    return E;
}(AddressBookSelectionMode));
//hijri conversion
var ConversionDateFormat = (function (E)
{
    E = {};
    E.DateFormat_Slash = "dd/mm/yyyy";
    E.DateTimeFormat24_Slash = "dd/mm/yyyy hh:mm";
    E.DateTimeFormatSeconds24_Slash = "dd/mm/yyyy hh:mm:ss";
    E.DateTimeFormat_Slash = "dd/mm/yyyy hh:mm a";
    E.DateTimeFormatSeconds_Slash = "dd/mm/yyyy hh:mm:ss a";
    E.DateFormat_Dash = "dd-mm-yyyy";
    E.DateTimeFormat24_Dash = "dd-mm-yyyy hh:mm";
    E.DateTimeFormatSeconds24_Dash = "dd-mm-yyyy hh:mm:ss";
    E.DateTimeFormat_Dash = "dd-mm-yyyy hh:mm a";
    E.DateTimeFormatSeconds_Dash = "dd-mm-yyyy hh:mm:ss a";
    E.YearMonthDateFormat_Dash = "yyyy-mm-dd";
    return E;
}(ConversionDateFormat));
var CalendarTypes = (function (E)
{
    E = {};
    E.Tabular = "Tabular";
    E.UmmAlQura = "UmmAlQura";
    E.Civil = "Civil";
    E.None = "None";
    return E;
}(CalendarTypes));
var DateConverter = (function (E)
{
    E = {};
    E.toHijriFormated = function (strDate, format, calendarType)
    {
        if (strDate)
        {
            var retVal = strDate;
            if (calendarType !== CalendarTypes.None)
            {
                format = format ? format : "dd/mm/yyyy";
                var nFormat = format.replaceAll('d', 'D').replaceAll('m', 'M').replaceAll('y', 'Y').replace(':MM', ':mm');
                var date = new Date(moment(strDate, nFormat));
                retVal = convertDateToHijri(date, calendarType)
                var nYear = retVal.nYear, nMonth = pad(retVal.nMonth), nDay = pad(retVal.nDay), nHour = pad(retVal.nHour), nMinute = pad(retVal.nMinute), nSecond = pad(retVal.nSecond);
                retVal = getStrDate(format, nYear, nMonth, nDay, nHour, nMinute, nSecond);
            }
            return retVal;
        } else
        {
            return "";
        }
    };
    E.toHijri = function (date, calendarType)
    {
        if (calendarType !== CalendarTypes.None)
        {
            return convertDateToHijri(date, calendarType);
        }
        return date;
    };
    E.convertToGregorian = function (nYear, nMonth, nDay, type, tDate, dateFormat)
    {
        var date = new Date();
        if (tDate)
        {
            date.setHours(tDate.getHours(), tDate.getMinutes(), tDate.getSeconds());
        }
        var nDate = convertToNational(date, type);
        if (type === CalendarTypes.None)
        {
            date.setFullYear(nYear, nMonth, nDay);
            return date;
        }
        var i;
        var days = 0;
        var past = nYear < nDate.nYear ||
            (nYear === nDate.nYear && nMonth < nDate.nMonth) ||
            (nYear === nDate.nYear && nMonth === nDate.nMonth && nDay < nDate.nDay);
        if (Math.abs(nDate.nYear - nYear) > 1)
        {
            for (var y = Math.min(nDate.nYear, nYear) + 1; y < Math.max(nDate.nYear, nYear); y++)
            {
                if (type === CalendarTypes.Civil || type === CalendarTypes.Tabular)
                {
                    days += civilMonth12Length[(y - 1) % 30];
                }
                else
                {
                    for (i = 0; i < 12; i++)
                    {
                        days += 29 + parseInt(UAQ_MONTH_LENGTH[y - 1300][i]);
                    }
                }
            }
        }
        if (nYear !== nDate.nYear || nMonth !== nDate.nMonth)
        {
            if (!past)
            {
                days +=
                    nDay +
                    getDaysInNationalMonth(nDate.nMonth, nDate.nYear, type) -
                    nDate.nDay;
            }
            else
            {
                days += nDate.nDay + getDaysInNationalMonth(nMonth, nYear, type) - nDay;
            }
        }
        else
        {
            days += Math.abs(nDay - nDate.nDay);
        }
        if (nDate.nYear != nYear)
        {
            if (past)
            {
                for (i = 0; i < nDate.nMonth; i++)
                {
                    days += getDaysInNationalMonth(i, nDate.nYear, type);
                }
                for (i = 11; i > nMonth; i--)
                {
                    days += getDaysInNationalMonth(i, nYear, type);
                }
            }
            else
            {
                for (i = nDate.nMonth + 1; i < 12; i++)
                {
                    days += getDaysInNationalMonth(i, nDate.nYear, type);
                }
                for (i = 0; i < nMonth; i++)
                {
                    days += getDaysInNationalMonth(i, nYear, type);
                }
            }
        }
        else if (nDate.nMonth != nMonth)
        {
            if (past)
            {
                for (i = nMonth + 1; i < nDate.nMonth; i++)
                {
                    days += getDaysInNationalMonth(i, nDate.nYear, type);
                }
            }
            else
            {
                for (i = nDate.nMonth + 1; i < nMonth; i++)
                {
                    days += getDaysInNationalMonth(i, nDate.nYear, type);
                }
            }
        }
        if (dateFormat && dateFormat.toLowerCase().indexOf('h') < 0 && date.getHours() === 0)
        {
            date.setTime(date.getTime() + ONE_HOUR);
        }
        var direction = past ? -1 : 1;
        date.setTime(date.getTime() + days * ONE_DAY * direction);
        return date;
    };
    return E;
}(DateConverter));
function convertDateToHijri(gDate, type)
{
    if (gDate === undefined)
    {
        return {
            date: new Date(),
            nYear: "",
            nMonth: "",
            nDay: "",
            nHour: "",
            nMinute: "",
            nSecond: ""
        }
    }
    if (type === CalendarTypes.None || type === undefined)
    {
        return {
            date: gDate,
            nYear: gDate.getFullYear(),
            nMonth: gDate.getMonth + 1,
            nDay: gDate.getDate(),
            nHour: gDate.getHours(),
            nMinute: gDate.getMinutes(),
            nSecond: gDate.getSeconds()
        };
    }
    var CIVIL_EPOC = 1948439.5, ASTRONOMICAL_EPOC = 1948438.5, GREGORIAN_EPOCH = 1721425.5;
    var year = gDate.getFullYear(), month = gDate.getMonth(), day = gDate.getDate();
    var nYear, nMonth, nDay, julianDay, days;
    //for civil/tabular
    var isLeapYear = function (gYear)
    {
        return (gYear % 4 === 0 && gYear % 100 !== 0) || gYear % 400 === 0;
    };
    var startYear = function (gYear)
    {
        return (gYear - 1) * 354 + Math.floor((3 + 11 * gYear) / 30.0);
    };
    var startMonth = function (gYear, gMonth)
    {
        return (Math.ceil(29.5 * gMonth) +
            (gYear - 1) * 354 +
            Math.floor((3 + 11 * gYear) / 30.0));
    };
    //for uaq
    var checkDiapason = function (date)
    {
        if (date.getTime() < gFirstRefForUAQ.getTime() ||
            date.getTime() > gLastRefForUAQ.getTime())
        {
            console.log("You operate with the dates not suitable for current implementation of 'Umm al-Qura' calendar.\nCalendar is switched to 'Civil'");
            return false;
        }
        return true;
    };
    var getDiff = function (gDate)
    {
        var i;
        var days2 = 50;
        for (i = 1883; i < gDate.getFullYear(); i++)
        {
            days2 += isLeapYear(i) ? 366 : 365;
        }
        for (i = 0; i < gDate.getMonth(); i++)
        {
            days2 += DAYS_IN_GR_MONTH[i];
            if (i == 1 && isLeapYear(gDate.getFullYear()))
            {
                days2++;
            }
        }
        days2 += gDate.getDate();
        return days2;
    };
    if (type === CalendarTypes.Civil || type === CalendarTypes.Tabular)
    {
        julianDay =
            Math.floor(GREGORIAN_EPOCH -
                1 +
                365 * (year - 1) +
                Math.floor((year - 1) / 4) +
                -Math.floor((year - 1) / 100) +
                Math.floor((year - 1) / 400) +
                Math.floor((367 * (month + 1) - 362) / 12 +
                    (month + 1 <= 2 ? 0 : isLeapYear(year) ? -1 : -2) +
                    day)) + 0.5;
        if (type === CalendarTypes.Tabular)
        {
            days = julianDay - ASTRONOMICAL_EPOC;
        }
        else
        {
            days = julianDay - CIVIL_EPOC;
        }
        nYear = Math.floor((30 * days + 10646) / 10631.0);
        nMonth = Math.ceil((days - 29 - startYear(nYear)) / 29.5);
        nMonth = Math.min(nMonth, 11);
        nDay = Math.ceil(days - startMonth(nYear, nMonth) + 1);
    }
    else if (type === CalendarTypes.UmmAlQura)
    {
        if (!checkDiapason(gDate))
        {
            return convertDateToHijri(gDate, CalendarTypes.Civil);
        }
        var diff = getDiff(gDate);
        nYear = 1300;
        nDay = 0;
        nMonth = 0;
        var stop = false;
        for (var i = 0; i < UAQ_MONTH_LENGTH.length; i++, nYear++)
        {
            for (var j = 0; j < 12; j++)
            {
                days = parseInt(UAQ_MONTH_LENGTH[i][j]) + 29;
                if (diff <= days)
                {
                    nDay = diff /*+ 1*/;
                    if (nDay > days)
                    {
                        nDay = 1;
                        j++;
                    }
                    if (j > 11)
                    {
                        j = 0;
                        nYear++;
                    }
                    nMonth = j;
                    stop = true;
                    break;
                }
                diff -= days;
            }
            if (stop)
            {
                break;
            }
        }
    }
    return {
        date: new Date(nYear, nMonth, nDay, gDate.getHours(), gDate.getMinutes(), gDate.getSeconds()),
        nYear: nYear,
        nMonth: nMonth + 1,
        nDay: nDay,
        nHour: gDate.getHours(),
        nMinute: gDate.getMinutes(),
        nSecond: gDate.getSeconds()
    }
}
function pad(number, length)
{
    if (length === void 0)
    {
        length = 2;
    }
    return ("000" + number).slice(length * -1);
};
function getStrDate(format, nYear, nMonth, nDay, nHour, nMinute, nSecond)
{
    var retVal = nDay + "/" + nMonth + "/" + nYear;
    if (format.toLowerCase() === "dd/mm/yyyy hh:mm")
    {
        retVal = nDay + "/" + nMonth + "/" + nYear + " " + nHour + ":" + nMinute;
    } else if (format.toLowerCase() === "dd/mm/yyyy hh:mm:ss")
    {
        retVal = nDay + "/" + nMonth + "/" + nYear + " " + nHour + ":" + nMinute + ":" + nSecond;
    } else if (format.toLowerCase() === "dd/mm/yyyy hh:mm a")
    {
        var ampm = "";
        if (nHour > 12)
        {
            ampm = " PM";
            nHour = nHour - 12;
        } else
        {
            ampm = " AM";
        }
        retVal = nDay + "/" + nMonth + "/" + nYear + " " + nHour + ":" + nMinute + ampm;
    } else if (format.toLowerCase() === "dd/mm/yyyy hh:mm:ss a")
    {
        var ampm = "";
        if (nHour > 12)
        {
            ampm = " PM";
            nHour = nHour - 12;
        } else
        {
            ampm = " AM";
        }
        retVal = nDay + "/" + nMonth + "/" + nYear + " " + nHour + ":" + nMinute + ":" + nSecond + ampm;
    } else if (format.toLowerCase() === "dd-mm-yyyy")
    {
        retVal = nDay + "-" + nMonth + "-" + nYear;
    } else if (format.toLowerCase() === "dd-mm-yyyy hh:mm")
    {
        retVal = nDay + "-" + nMonth + "-" + nYear + " " + nHour + ":" + nMinute;
    } else if (format.toLowerCase() === "dd-mm-yyyy hh:mm:ss")
    {
        retVal = nDay + "-" + nMonth + "-" + nYear + " " + nHour + ":" + nMinute + ":" + nSecond;
    } else if (format.toLowerCase() === "dd-mm-yyyy hh:mm a")
    {
        var ampm = "";
        if (nHour > 12)
        {
            ampm = " PM";
            nHour = nHour - 12;
        } else
        {
            ampm = " AM";
        }
        retVal = nDay + "-" + nMonth + "-" + nYear + " " + nHour + ":" + nMinute + ampm;
    } else if (format.toLowerCase() === "dd-mm-yyyy hh:mm:ss a")
    {
        var ampm = "";
        if (nHour > 12)
        {
            ampm = " PM";
            nHour = nHour - 12;
        } else
        {
            ampm = " AM";
        }
        retVal = nDay + "-" + nMonth + "-" + nYear + " " + nHour + ":" + nMinute + ":" + nSecond + ampm;
    } else if (format.toLowerCase() === "yyyy-mm-dd")
    {
        retVal = nYear + "-" + nMonth + "-" + nDay;
    } else if (format.toLowerCase() === "yyyy-mm-dd hh:mm")
    {
        retVal = nYear + "-" + nMonth + "-" + nDay + " " + nHour + ":" + nMinute;
    } else if (format.toLowerCase() === "yyyy-mm-dd hh:mm:ss")
    {
        retVal = nYear + "-" + nMonth + "-" + nDay + " " + nHour + ":" + nMinute + ":" + nSecond;
    } else if (format.toLowerCase() === "yyyy-mm-dd hh:mm a")
    {
        var ampm = "";
        if (nHour > 12)
        {
            ampm = " PM";
            nHour = nHour - 12;
        } else
        {
            ampm = " AM";
        }
        retVal = nYear + "-" + nMonth + "-" + nDay + " " + nHour + ":" + nMinute + ampm;
    } else if (format.toLowerCase() === "yyyy-mm-dd hh:mm:ss a")
    {
        var ampm = "";
        if (nHour > 12)
        {
            ampm = " PM";
            nHour = nHour - 12;
        } else
        {
            ampm = " AM";
        }
        retVal = nYear + "-" + nMonth + "-" + nDay + " " + nHour + ":" + nMinute + ":" + nSecond + ampm;
    } else if (format.toLowerCase() === "mm/dd/yyyy")
    {
        retVal = nMonth + "/" + nDay + "/" + nYear;
    } else if (format.toLowerCase() === "mm/dd/yyyy hh:mm")
    {
        retVal = nMonth + "/" + nDay + "/" + nYear + " " + nHour + ":" + nMinute;
    } else if (format.toLowerCase() === "mm/dd/yyyy hh:mm:ss")
    {
        retVal = nMonth + "/" + nDay + "/" + nYear + " " + nHour + ":" + nMinute + ":" + nSecond;
    } else if (format.toLowerCase() === "mm/dd/yyyy hh:mm a")
    {
        var ampm = "";
        if (nHour > 12)
        {
            ampm = " PM";
            nHour = nHour - 12;
        } else
        {
            ampm = " AM";
        }
        retVal = nMonth + "/" + nDay + "/" + nYear + " " + nHour + ":" + nMinute + ampm;
    } else if (format.toLowerCase() === "mm/dd/yyyy hh:mm:ss a")
    {
        var ampm = "";
        if (nHour > 12)
        {
            ampm = " PM";
            nHour = nHour - 12;
        } else
        {
            ampm = " AM";
        }
        retVal = nMonth + "/" + nDay + "/" + nYear + " " + nHour + ":" + nMinute + ":" + nSecond + ampm;
    } else if (format.toLowerCase() === "mm-dd-yyyy")
    {
        retVal = nMonth + "-" + nDay + "-" + nYear;
    } else if (format.toLowerCase() === "mm-dd-yyyy hh:mm")
    {
        retVal = nMonth + "-" + nDay + "-" + nYear + " " + nHour + ":" + nMinute;
    } else if (format.toLowerCase() === "mm-dd-yyyy hh:mm:ss")
    {
        retVal = nMonth + "-" + nDay + "-" + nYear + " " + nHour + ":" + nMinute + ":" + nSecond;
    } else if (format.toLowerCase() === "mm-dd-yyyy hh:mm a")
    {
        var ampm = "";
        if (nHour > 12)
        {
            ampm = " PM";
            nHour = nHour - 12;
        } else
        {
            ampm = " AM";
        }
        retVal = nMonth + "-" + nDay + "-" + nYear + " " + nHour + ":" + nMinute + ampm;
    } else if (format.toLowerCase() === "mm-dd-yyyy hh:mm:ss a")
    {
        var ampm = "";
        if (nHour > 12)
        {
            ampm = " PM";
            nHour = nHour - 12;
        } else
        {
            ampm = " AM";
        }
        retVal = nMonth + "-" + nDay + "-" + nYear + " " + nHour + ":" + nMinute + ":" + nSecond + ampm;
    }

    return retVal;
}
function getDaysInNationalMonth(month, year, type)
{
    if (type === CalendarTypes.None)
    {
        return month === 1 &&
            year % 4 === 0 &&
            (year % 100 !== 0 || year % 400 === 0)
            ? 29
            : DAYS_IN_GR_MONTH[month];
    }
    else if (type === CalendarTypes.UmmAlQura)
    {
        return 29 + parseInt(UAQ_MONTH_LENGTH[year - 1300][month]);
    }
    if (month % 2 === 0)
    {
        //civil/tabular
        return 30;
    }
    else if (month === 11)
    {
        if (civilMonth12Length[(year - 1) % 30] === 355)
        {
            return 30;
        }
        else
        {
            return 29;
        }
    }
    else
    {
        return 29;
    }
}
function convertToNational(gDate, type)
{
    if (type === CalendarTypes.None || gDate === undefined)
    {
        return gDate;
    }
    var CIVIL_EPOC = 1948439.5, ASTRONOMICAL_EPOC = 1948438.5, GREGORIAN_EPOCH = 1721425.5;
    var year = gDate.getFullYear(), month = gDate.getMonth(), day = gDate.getDate();
    var nYear, nMonth, nDay, julianDay, days;
    //for civil/tabular
    var isLeapYear = function (gYear)
    {
        return (gYear % 4 === 0 && gYear % 100 !== 0) || gYear % 400 === 0;
    };
    var startYear = function (gYear)
    {
        return (gYear - 1) * 354 + Math.floor((3 + 11 * gYear) / 30.0);
    };
    var startMonth = function (gYear, gMonth)
    {
        return (Math.ceil(29.5 * gMonth) +
            (gYear - 1) * 354 +
            Math.floor((3 + 11 * gYear) / 30.0));
    };
    //for uaq
    var checkDiapason = function (date)
    {
        if (date.getTime() < gFirstRefForUAQ.getTime() ||
            date.getTime() > gLastRefForUAQ.getTime())
        {
            console.log("You operate with the dates not suitable for current implementation of 'Umm al-Qura' calendar.\nCalendar is switched to 'Civil'");
            return false;
        }
        return true;
    };
    var getDiff = function (gDate)
    {
        var i;
        var days2 = 50;
        for (i = 1883; i < gDate.getFullYear(); i++)
        {
            days2 += isLeapYear(i) ? 366 : 365;
        }
        for (i = 0; i < gDate.getMonth(); i++)
        {
            days2 += DAYS_IN_GR_MONTH[i];
            if (i == 1 && isLeapYear(gDate.getFullYear()))
            {
                days2++;
            }
        }
        days2 += gDate.getDate();
        return days2;
    };
    if (type === CalendarTypes.Civil || type === CalendarTypes.Tabular)
    {
        julianDay =
            Math.floor(GREGORIAN_EPOCH -
                1 +
                365 * (year - 1) +
                Math.floor((year - 1) / 4) +
                -Math.floor((year - 1) / 100) +
                Math.floor((year - 1) / 400) +
                Math.floor((367 * (month + 1) - 362) / 12 +
                    (month + 1 <= 2 ? 0 : isLeapYear(year) ? -1 : -2) +
                    day)) + 0.5;
        if (type === CalendarTypes.Tabular)
        {
            days = julianDay - ASTRONOMICAL_EPOC;
        }
        else
        {
            days = julianDay - CIVIL_EPOC;
        }
        nYear = Math.floor((30 * days + 10646) / 10631.0);
        nMonth = Math.ceil((days - 29 - startYear(nYear)) / 29.5);
        nMonth = Math.min(nMonth, 11);
        nDay = Math.ceil(days - startMonth(nYear, nMonth) + 1);
    }
    else if (type === CalendarTypes.UmmAlQura)
    {
        if (!checkDiapason(gDate))
        {
            return convertToNational(gDate, CalendarTypes.Civil);
        }

        var diff = getDiff(gDate);
        nYear = 1300;
        nDay = 0;
        nMonth = 0;
        var stop = false;
        for (var i = 0; i < UAQ_MONTH_LENGTH.length; i++, nYear++)
        {
            for (var j = 0; j < 12; j++)
            {
                days = parseInt(UAQ_MONTH_LENGTH[i][j]) + 29;
                if (diff <= days)
                {
                    nDay = diff /*+ 1*/;
                    if (nDay > days)
                    {
                        nDay = 1;
                        j++;
                    }
                    if (j > 11)
                    {
                        j = 0;
                        nYear++;
                    }
                    nMonth = j;
                    stop = true;
                    break;
                }
                diff -= days;
            }
            if (stop)
            {
                break;
            }
        }
    }
    var sDate = new Date(0);
    sDate.setTime(gDate.getTime());
    var nDate = saveNationalDate(sDate, nYear, nMonth, nDay);
    return nDate;
}
function saveNationalDate(date, year, month, day)
{
    var gDate = new Date(0);
    gDate.setTime(date.getTime());
    return {
        date: gDate,
        nYear: year,
        nMonth: month,
        nDay: day,
    };
}
//end hijri conversion
var AuditAction = (function (E)
{
    E = {};
    E.Add = "1";
    E.Edit = "2";
    E.Delete = "3";
    return E;
}(AuditAction));
var AuditModule = (function (E)
{
    E = {};
    E.Action = "1";
    E.Menu = "2";
    E.Node = "3";
    E.SearchAssignedSecurity = "4";
    E.SendingRule = "5";
    E.Tab = "6";
    E.User = "7";
    E.SystemDelegation = "8";
    E.Parameter = "9";
    return E;
}(AuditModule));