<div ref="{{ComponentId}}">
    <div ref="modalEvent" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog" aria-hidden="true" class="modal fade">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" ref="eventClose" class="close" data-dismiss="modal">&times;</button>
                    <h4 ref="modalEventTitle" class="modal-title"></h4>
                </div>
                <div class="modal-body">
                    <form ref="formIndexPost" method="post" data-parsley-validate="" novalidate="">
                        <div ref="userPostMessage"></div><br />

                        <!-- Name Field -->
                        <div class="form-group">
                            <label for="eventName" class="control-label field-required">{{Localizer 'Name'}}</label>
                            <input type="text" id="eventName" name="name" ref="eventName" class="form-control" required>
                        </div>

                        <!-- Location Field -->
                        <div class="form-group">
                            <label for="eventLocation" class="control-label field-required">{{Localizer 'Location'}}</label>
                            <input type="text" id="eventLocation" ref="eventLocation" name="location" class="form-control" required>
                        </div>

                        <!-- Description Field -->
                        <div class="form-group">
                            <label for="description" class="control-label field-required">{{Localizer 'Description'}}</label>
                            <textarea id="description" ref="eventDescription" name="description" class="form-control" rows="4" required></textarea>
                        </div>

                        <div class="row">
                            <!-- From Date Field -->
                            <div class="col-lg-6 col-md-6 col-sm-12">
                                <div class="form-group">
                                    <label class="control-label field-required">{{Localizer 'FromDate'}}</label>
                                    <div class="input-group date">
                                        <input ref="fromDate" type="text" tabindex="2" autocomplete="off" class="form-control" name="fromDate" data-parsley-errors-container="#fromDateError" required>
                                        <span class="input-group-addon dum" id="fromDate_img" ref="fromDate_img" style="cursor:pointer">
                                            <i class="fa fa-calendar" aria-hidden="true"></i>
                                        </span>
                                    </div>
                                    <div id="fromDateError"></div>
                                </div>
                            </div>

                            <!-- To Date Field -->
                            <div class="col-lg-6 col-md-6 col-sm-12">
                                <div class="form-group">
                                    <label class="control-label field-required">{{Localizer 'ToDate'}}</label>
                                    <div class="input-group date">
                                        <input ref="toDate" type="text" tabindex="3" autocomplete="off" class="form-control" name="toDate" data-parsley-errors-container="#toDateError" required>
                                        <span class="input-group-addon dum" ref="toDate_img" style="cursor:pointer">
                                            <i class="fa fa-calendar" aria-hidden="true"></i>
                                        </span>
                                    </div>
                                    <div id="toDateError"></div>
                                </div>
                            </div>
                        </div>

                    </form>
                </div>
                <div class="modal-footer">
                    <button tabindex="3" ref="btnSubmitEvent" type="button" class="btn btn-primary" data-loading-text="<i class='fa fa-spinner fa-spin'></i> {{Localizer 'Saving'}}">{{Localizer 'Submit'}}</button>
                    <button tabindex="4" ref="btnSubmitCloseEvent" type="button" class="btn btn-info">{{Localizer 'SubmitClose'}}</button>
                    <button tabindex="5" ref="btnCloseEvent" type="button" data-dismiss="modal" class="btn btn-default">{{Localizer 'Close'}}</button>
                </div>
            </div>
        </div>
    </div>
</div>
