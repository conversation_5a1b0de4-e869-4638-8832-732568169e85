﻿import ReAssign from './reAssign.js'
import Intalio from './common.js'
import LinkedCorrespondenceIndex from './linkedCorrespondenceIndex.js'
import DocumentDetails from './documentDetails.js'
import LinkedCorrespondenceDocument from './linkedCorrespondenceDocument.js'
import tab from './tab.js'
class  DocumentManageStructureUsersCorrespondences extends Intalio.Model {
    constructor() {
        super();
        this.UserInbox = null;
        this.UserSent = null;
        this.UserStructure = null;
    }
}
function openRecallReasonModal(callback) {
    // The modal has a header having >> "modal-header" div contains the closing button icon that closes the modal
    // The modal also has a textarea for notes, and two buttons: "Submit" and "Cancel".
    const modal = $(`
    <div data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog" aria-hidden="true" class="modal fade modalRecall" id="recallModal">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header" style="border-bottom:0px; padding-bottom:3px;">
                    <button type="button" ref="recallClose" id="recallModalClose" class="close" data-dismiss="modal">&times;</button>
                    <h4 ref="modalRecallTitle" class="modal-title"></h4>
                </div> 
                <div class="modal-body" style="padding-top: 2px;">
                    <form ref="formPost" method="post" data-parsley-validate="" novalidate="">
                        <div class="row">
                            <div class="col-md-12" ref="recallReasonContainer">
                                <label class="control-label field-required" style="font-size: medium;">${Resources.RecallReason}</label>
                                <textarea id="recallReason" rows="3" class="form-control" required></textarea>
                                <div class="invalid-feedback" style="display:none; color:red;">
                                    ${Resources.RequiredField}
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer" style="border-top:0px;">
                    <button type="button" class="btn btn-primary" id="submitReason">${Resources.Recall}</button>
                    <button type="button" class="btn btn-secondary" id="cancelRecall" data-bs-dismiss="modal">${Resources.Cancel}</button>
                </div>
            </div>
        </div>
    </div>
    `);
    // The textarea is required (via data-parsley-required and required attributes), ensuring the user cannot submit without entering text.
    // UI Production: The modal is dynamically created using a jQuery template literal and appended to the body.

    $('body').append(modal); // This body is the default screen html body , so we basically append this modal template into the screen content(body)

    modal.modal('show');   //  displays the modal

    modal.find('#submitReason').on('click', function () {
        const textarea = modal.find('#recallReason');
        const reason = textarea.val().trim();                 // Removes any leading or trailing whitespace from the recall reason input value
        const errorMsg = textarea.siblings('.invalid-feedback');

        if (!reason) {
            textarea.addClass('is-invalid');  
            errorMsg.show();  // Shows the error message
            modal.find('form').addClass('was-validated'); 
            return;

        } else {
            textarea.removeClass('is-invalid');
            errorMsg.hide(); // Hides error message
            // modal.modal('hide').remove();           // Once there is added text  removes the error msg and red border automatically. 
            $("#recallModalClose").trigger("click");
            callback(reason);

        }
    });

    // Remove required validation styles once there is an input change or input added
    modal.find('#recallReason').on('input', function () {
        $(this).removeClass('is-invalid');
        $(this).siblings('.invalid-feedback').hide();
    });
    modal.find('#cancelRecall').on('click', function () {
        modal.find('#recallReason').val('');
        modal.modal('hide');

    });
    modal.on('hidden.bs.modal', function () {
        modal.remove();
        //$('.close').trigger('click');
    });
}
function recallUserCorrespondence(userId, id, delegationId) {
    openRecallReasonModal(function (reason) {
            Common.ajaxPost(
                '/Transfer/StructureUserRecall',
                {
                    'userId': userId,
                    'id': id,
                    'delegationId': delegationId,
                    'note': reason,
                    '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
                },
                function (data) {
                    gLocked = false;
                    if (data.success == false) {

                        var messageKey = data.message;
                        var resourceMap = {
                            "FileInUse": Resources?.FileInUse,
                            "CannotBeRecalled": Resources?.CannotBeRecalled
                        };

                        var msg = resourceMap[messageKey] ?? messageKey;

                        setTimeout(function () {
                            Common.alertMsg(msg);
                        }, 300);

                    }
                    else {

                        swal.close();

                        TreeNode.refreshTreeNodeCounts(TreeNode.Draft);
                        TreeNode.refreshTreeNodeCounts(TreeNode.Sent);
                        TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);
                        TreeNode.refreshTreeNodeCounts(TreeNode.StructureInbox);
                        TreeNode.refreshTreeNodeCounts(TreeNode.StructureSent);
                        TreeNode.refreshTreeNodeCounts(TreeNode.Completed);

                        $($("input[data-id='" + id + "']").parents("li")[0]).fadeOut().remove();

                        $(".withBorders-o").addClass("waitingBackground");

                        $("#sentDocumentDetailsContainer").empty();

                        GridCommon.Refresh(gTableName);
                    }
                },
                function () {

                    gLocked = false;
                    Common.showScreenErrorMsg();
                },
                false
            );
            TreeNode.refreshTreeNodeCounts(TreeNode.Draft);
            TreeNode.refreshTreeNodeCounts(TreeNode.Sent);
            TreeNode.refreshTreeNodeCounts(TreeNode.Inbox); TreeNode.refreshTreeNodeCounts(TreeNode.StructureInbox); TreeNode.refreshTreeNodeCounts(TreeNode.StructureSent);
            TreeNode.refreshTreeNodeCounts(TreeNode.Completed);
        }, function () {
            gLocked = false;
        
    });
}
function recall(userId, id, delegationId) {
    if (window.EnableConfirmationMessage === "True") {
        Common.showConfirmMsg(Resources.RecallConfirmation, function () {
            recallUserCorrespondence(userId, id, delegationId)
        })
    }
    else {
        recallUserCorrespondence(userId, id, delegationId)
    }

    
}



function reAssign(transferId, delegationId, toUserId, toStructureId, documentPrivacyId) {

    let model = new ReAssign.ReAssign();
    let structurIds = [];
    structurIds.push(toStructureId);
    model.structureIds = structurIds;
    model.transferId = transferId;
    model.toUserId = toUserId;
    model.documentPrivacyId = documentPrivacyId;
    model.privacies = new CoreComponents.Lookup.Privacies().get(window.language);
    let wrapper = $(".modal-window");
    wrapper.empty();
    let view = new ReAssign.ReAssignView(wrapper, model);
    view.render();

    $(".modalReassign").off("hidden.bs.modal");
    $(".modalReassign").off("shown.bs.modal");
    $(".modalReassign").on('shown.bs.modal', function () { });
    $(".modalReassign").on('hidden.bs.modal', function () { });
    $(".modalReassign").modal("show");
    $("#noteFieldContainer").show();

    $("#submitReassign").on("click", function () {
        const note = $("#reassignNote").val().trim();
        if (!note) {
            $("#noteError").show();
        } else {
            $("#noteError").hide();
            $(".modalReassign").modal("hide")
            model.note = note;
        }
    });
    TreeNode.refreshTreeNodeCounts(TreeNode.Draft);
    TreeNode.refreshTreeNodeCounts(TreeNode.Sent);
    TreeNode.refreshTreeNodeCounts(TreeNode.Inbox); TreeNode.refreshTreeNodeCounts(TreeNode.StructureInbox); TreeNode.refreshTreeNodeCounts(TreeNode.StructureSent);
    TreeNode.refreshTreeNodeCounts(TreeNode.Completed);
}
function createUsersSelect2() {
    let headers = {};
    const url = window.IdentityUrl + `/api/ListUsersByStructureId?id=${$('#hdLoggedInStructureId').val()}&showOnlyActiveUsers=false`;
    headers.Authorization = 'Bearer ' + window.IdentityAccessToken;
    $("#cmbSearchFilterUser").select2({
        minimumInputLength: 0,
        allowClear: true,
        placeholder: "",
        dir: window.language === "ar" ? "rtl" : "ltr",
        language: window.language,
        dropdownParent: $('#userSearchFilterContainer'),
        width: "100%",
        ajax: {
            delay: 250,
            url: url,
            type: "Get",
            dataType: 'json',
            headers: typeof headers !== "undefined" ? headers : "",
            data: function (term) {
                //return { "text": "", "language": window.language }; //if you have more than 15 users in the same structure this will only show the 15 and when you search for a user not in the list of 15 it will show not found
                return { "text": term.term ? term.term : "", "language": window.language };
            },
            processResults: function (data, term) {
                return {
                    results: userDataForSelect2(data, term)
                };
            }
        }
    });
}

function createPrivaciesSelect2() {
    let headers = {};
    let privacies = new CoreComponents.Lookup.Privacies().get(window.language);
    headers.Authorization = 'Bearer ' + window.IdentityAccessToken;
    $("#cmbPrivacyLevel").select2({
        minimumInputLength: 0,
        allowClear: true,
        placeholder: "",
        dir: window.language === "ar" ? "rtl" : "ltr",
        language: window.language,
        dropdownParent: $('#privacyLevelContainer'),
        width: "100%",
        data: privacies
    });
}
function openSearchDocument(id, delegationId, fromLink) {

    if (true) {
        let params = { id: id };
        if (delegationId !== null) {
            params.delegationId = delegationId;
        }
        Common.ajaxGet('/Document/GetSearchDocumentEdit', params, function (response) {

            if (response && response === "NoAccess") {
                Common.alertMsg(Resources.NoPermission);
            } else {
                if (!response.id) {
                    return;
                }
                let wrapper = $(".modal-window");
                let model = new LinkedCorrespondenceIndex.LinkedCorrespondenceIndex();
                model.documentId = response.id;
                model.reference = response.referenceNumber;
                model.subject = response.subject;
                let linkedCorrespondenceDocument = new LinkedCorrespondenceDocument.LinkedCorrespondenceDocumentView(wrapper, model);
                linkedCorrespondenceDocument.render();

                model = new DocumentDetails.DocumentDetails();
                model.documentModel = response;
                model.readonly = true;
                model.delegationId = delegationId;
                model.documentId = response.id;
                model.referenceNumber = response.referenceNumber;
                model.categoryName = response.categoryName;
                model.statusId = response.status;
                model.sendingEntity = response.sendingEntity;
                model.receivingEntities = response.receivingEntities;
                model.createdByUser = response.createdByUser;
                model.statuses = new CoreComponents.Lookup.Statuses().get(window.language);
                model.showMyTransfer = false;
                model.showVisualTrackingOnly = false;
                let securityMatrix = JSON.parse(new CoreComponents.Lookup.SecurityMatrix().get(window.language));// JSON.parse(sessionStorage.getItem("SecurityMatrix"));
                let tabs = [];
                const nodeId = TreeNodes.Search;
                if (nodeId !== undefined && $.isNumeric(nodeId)) {
                    tabs = securityMatrix.SecurityNodes[nodeId].SecurityCategories[response.categoryId].Tabs;
                    model.tabsActions = securityMatrix.SecurityNodes[nodeId].SecurityCategories[response.categoryId].SecurityTabs;
                }
                model.tabs = $.grep(tabs, function (element, index) {
                    return !element.Name.includes("myTransfer") && !element.Name.includes("documentMetadata") &&
                        !element.Name.includes("notes") &&
                        !element.Name.includes("visualTracking") && !element.Name.includes("activityLog") &&
                        !element.Name.includes("linkedDocument") && !element.Name.includes("transferHistory") && !element.Name.includes("attachments");
                });
                model.tabsWithStatic = tabs;
                model.showBackButton = false;
                model.isModal = true;
                model.attachmentId = response.attachmentId;
                model.attachmentVersion = response.attachmentVersion;
                wrapper = $(linkedCorrespondenceDocument.refs['linkDocumentDiv']);
                let view = new DocumentDetails.DocumentDetailsView(wrapper, model);
                view.render();
                $($(view.refs['documentDetailsContainerDiv']).find('.div-flex')[0]).addClass('width100pc-ml0');

                let title = response.categoryName;
                if (response.referenceNumber) {
                    title += ' - ' + response.referenceNumber;
                }
                if (response.createdByUser) {
                    title += ' - ' + response.createdByUser;
                }
                $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocumentTitle']).html(title);
                $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).off("hidden.bs.modal");
                $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).off("shown.bs.modal");
                $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).on('shown.bs.modal', function () {
                    $("ul[ref='tabDocumentDetails']").scrollingTabs('refresh');
                });
                $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).on('hidden.bs.modal', function () {
                    if ($(this).data("remove") != true)
                        return;
                    $(linkedCorrespondenceDocument.refs[linkedCorrespondenceDocument.model.ComponentId]).remove();
                    swal.close();
                    //if ($(".modal-window").children().length > 0 || $(".modal-documents").children().length > 0) {
                    //    $('body').addClass('modal-open');
                    //}
                });
                $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).modal("show");

            }
        }, function () { Common.showScreenErrorMsg(); }, true);
    }
    else {
        let params = { id: id };
        if (delegationId !== null) {
            params.delegationId = delegationId;
        }
        Common.ajaxGet('/Document/GetSearchDocument', params, function (response) {
            if (response && response === "NoAccess") {
                Common.alertMsg(Resources.NoPermission);
            } else {
                Common.setActiveSidebarMenu("liSearch");
                $(".delegation").removeClass("active");
                $("#searchContainerDiv").hide();

                let model = new DocumentDetails.DocumentDetails();
                model.documentModel = response;
                model.delegationId = delegationId;
                model.documentId = response.id;
                model.referenceNumber = response.referenceNumber;
                model.categoryName = response.categoryName;
                model.statusId = response.status;
                model.documentModel = response;
                model.readonly = true;
                model.statuses = new CoreComponents.Lookup.Statuses().get(window.language);
                model.showMyTransfer = false;
                model.fromSearch = true;
                model.attachmentId = response.attachmentId;
                model.attachmentVersion = response.attachmentVersion;
                let securityMatrix = JSON.parse(new CoreComponents.Lookup.SecurityMatrix().get(window.language));// JSON.parse(sessionStorage.getItem("SecurityMatrix"));
                let tabs = securityMatrix.SecurityNodes[Number(TreeNodes.Search)].SecurityCategories[response.categoryId].Tabs;
                model.tabs = $.grep(tabs, function (element, index) {
                    return !element.Name.includes("myTransfer") && !element.Name.includes("documentMetadata") &&
                        !element.Name.includes("transferHistory") && !element.Name.includes("visualTracking") &&
                        !element.Name.includes("activityLog") && !element.Name.includes("attachments") && !element.Name.includes("notes") &&
                        !element.Name.includes("linkedDocument") && !element.Name.includes("nonArchivedAttachments");
                });
                model.tabsWithStatic = tabs;
                model.tabsActions = securityMatrix.SecurityNodes[Number(TreeNodes.Search)].SecurityCategories[response.categoryId].SecurityTabs;
                let wrapper = $(".content-wrapper");
                let view = new DocumentDetails.DocumentDetailsView(wrapper, model);
                view.render();
                $($(view.refs['documentDetailsContainerDiv']).find('.div-flex')[0]).addClass('width100pc-ml0');
                if (!gFromLink) {
                    $(document).off('click', '.btn-back');
                    $(document).on('click', '.btn-back', function () {
                        $("#searchContainerDiv").show();
                        view.remove();
                        $(".toRemove").remove();
                    });

                    $(document).off('click', '.btn-export');
                    $(document).on('click', '.btn-export', function () {
                        let wrapper = $(".modal-window");
                        let model = new ReportCorrespondenceDetailExport.ReportCorrespondenceDetailExport();
                        model.documentId = response.id;
                        model.delegationId = delegationId;
                        let reportCorrespondenceDetailExportView = new ReportCorrespondenceDetailExport.ReportCorrespondenceDetailExportView(wrapper, model);
                        reportCorrespondenceDetailExportView.render();

                        $("#modalReportCorrespondenceDetailExport").off("hidden.bs.modal");
                        $("#modalReportCorrespondenceDetailExport").off("shown.bs.modal");
                        $("#modalReportCorrespondenceDetailExport").on('shown.bs.modal', function () { });
                        $("#modalReportCorrespondenceDetailExport").on('hidden.bs.modal', function () { });
                        $("#modalReportCorrespondenceDetailExport").modal("show");
                    });
                }
            }
        }, function () { Common.showScreenErrorMsg(); }, true);
    }
}

function userDataForSelect2(data, term) {
    let termSearch = term.term ? term.term : "";
    let retVal = [];
    let userId = Number($("#hdUserId").val());
    $.each(data, function (key, val) {

        let fullName = val.fullName;
        if (window.language != 'en') {
            fullName = getFullNameByLangauge(val);
            fullName = fullName.trim() == "" ? val.fullName : fullName;
        }
        let allNames = getFullNameInAllLangauge(val);
        if (allNames.length == 0) allNames.push(fullName);
        if (termSearch != "" &&
            !allNames.some(r => r.toLowerCase().includes(termSearch.toLowerCase()))) {
            return;
        }
        if (val.id === userId) {
            return;
        }
        let isExist = retVal.some(function (usr) { return usr.id === val.id; });
        if (!isExist) {
            let item = {};
            item.id = val.id;
            item.text = fullName;
            item.isStructure = false;
            item.dataId = val.id;
            retVal.push(item);
        }
    });
    return retVal;
}

let table = null;
let firstTime = true;
let gLocked = false;
let gTableName = "grdSearchItems";
let gFromLink = false;
function isFilled() {
    return Boolean($("#cmbSearchFilterUser").val());
}



function search(self) {
    if (firstTime) {
        let documentId = null;
        let delegationId = $('#hdDelegatedId').val() ? $('#hdDelegatedId').val() : null;
        let columns = [];
        Common.gridCommon();
        const securityMatrix = JSON.parse(new CoreComponents.Lookup.SecurityMatrix().get(window.language));
        const buttons = SecurityMatrix.getToolbarActions(securityMatrix, TreeNodes.Search, gTableName);
        let showCheckBoxes = self.model.fromLink || buttons.length > 0;
        columns.push({ title: "Id", data: "id", visible: false, "orderable": false });
        columns.push({ title: Resources.ReferenceNumber, data: "referenceNumber", render: $.fn.dataTable.render.text(), "orderable": true, orderSequence: ["asc", "desc"] });
        columns.push({ title: Resources.FromStructure, data: "fromStructure", render: $.fn.dataTable.render.text(), "orderable": true, orderSequence: ["asc", "desc"] });
        columns.push({ title: Resources.ToStructure, data: "toStructure", render: $.fn.dataTable.render.text(), "orderable": false, orderSequence: ["asc", "desc"] });
        columns.push({ title: Resources.ToUser, data: "toUser", render: $.fn.dataTable.render.text(), "orderable": false, orderSequence: ["asc", "desc"] });
        columns.push({ title: Resources.Subject, data: "subject", render: $.fn.dataTable.render.text(), "orderable": true, orderSequence: ["asc", "desc"] });
        columns.push({
            title: Resources.CreatedDate, data: "createdDate", "orderable": true, orderSequence: ["asc", "desc"],
            render: function (data, type, full, meta) {
                return DateConverter.toHijriFormated(full.createdDate, null, window.CalendarType);
            }
        });
        columns.push({
            "className": "text-right",
            'width': "6px",
            'orderable': false,
            'sortable': false,
            'render': function (data, type, full, meta) {
                documentId = full.documentId;
                let userId = Number($("#cmbSearchFilterUser").val());
                if (full.statusId != 3 && !full.isLocked) {
                    if ($('#cmbMessageType').length > 0 && $('#cmbMessageType').val() === 'sent') {
                        let btnRecall = document.createElement("button");
                        btnRecall.setAttribute("class", "btn btn-xs btn-success  recall ");
                        btnRecall.setAttribute("title", Resources.Recall);
                        btnRecall.setAttribute("type", "button");
                        btnRecall.setAttribute("clickattr", "recall(" + userId + ", " + full.id + ", " + delegationId + ")");
                        btnRecall.innerHTML = "<i class='fa fa-repeat fa-white'></i>";
                        return btnRecall.outerHTML;
                    } else {
                        return '';
                    }

                } else {
                    return '';
                }

            }
        });
        columns.push({
            "className": "text-right",
            'width': "6px",
            'orderable': false,
            'sortable': false,
            'render': function (data, type, full, meta) {
                if (full.toUserId != null && full.statusId != 3) {
                    if (full.workflowStepId == null) {
                        if ($('#cmbMessageType').length > 0 && $('#cmbMessageType').val() === 'inbox') {
                            let btnReAssign = document.createElement("button");
                            btnReAssign.setAttribute("class", "btn btn-success btn-xs  showReassignButton");
                            btnReAssign.setAttribute("title", Resources.ReAssign);
                            btnReAssign.setAttribute("type", "button");
                            btnReAssign.setAttribute("clickattr", "reAssign(" + full.id + "," + delegationId + "," + full.toUserId + "," + full.toStructureId +  "," + full.privacyId + ")");
                            btnReAssign.innerHTML = "<i class='fa fa-user'></i>";
                            return btnReAssign.outerHTML;
                        }
                    }
                    return '';
                }
                else {
                    return '';
                }
            }
        });
        columns.push({
            "className": "text-right",
            'width': "6px",
            'orderable': false,
            'sortable': false,
            'render': function (data, type, full, meta) {
                let btnView = document.createElement("button");
                btnView.setAttribute("class", "btn btn-xs btn-warning view");
                btnView.setAttribute("title", Resources.View);
                btnView.setAttribute("type", "button");
                btnView.setAttribute("clickattr", "openSearchDocument(" + full.documentId + "," + delegationId + "," + self.model.fromLink + ")");
                btnView.innerHTML = "<i class='fa fa-eye fa-white'/>";
                return btnView.outerHTML;
            }
        });
        
        table = $("#grdSearchItems")
            .on('draw.dt', function () {
                $('#grdSearchItems tbody tr td').each(function () {
                    this.setAttribute('title', $(this).text());
                });
                GridCommon.CheckSelectedRows(gTableName);
            })
            .DataTable({
                processing: true,
                ordering: true,
                serverSide: true,
                pageLength: 10,
                "ajax": {
                    "url": "/Search/ManageStructureUsersCorrespondences",
                    "type": "POST",
                    "datatype": "json",
                    "data": function (d) {
                        let model = {};
                        model.UserInbox = $('#cmbMessageType').val() === "inbox" ? $("#cmbSearchFilterUser").val() : null;
                        model.UserSent = $('#cmbMessageType').val() === "sent" ? $("#cmbSearchFilterUser").val() : null;
                        model.UserStructure = $('#hdLoggedInStructureId').val();
                        model.Privacy = $("#cmbPrivacyLevel").val();
                        model.userPrivacyId = $('#hdLoggedStructurePrivacyLevel').val();
                        d.__RequestVerificationToken = $('input[name="__RequestVerificationToken"]').val();
                        d.Model = JSON.stringify(model);
                        return d;
                    },
                    "dataSrc": function (response) {

                        $('#grdSearchItems_processing').css('display', 'none');
                        if (response.recordsTotal > 0) {
                            return response.data;
                        } else {
                            if (response.message != undefined && response.message != "") {
                                Common.showScreenErrorMsg(response.message);
                            }
                            response.data = []
                            return response.data;
                        }
                    }
                },
                "order": [],
                "columns": columns,
                "fnInitComplete": function (settings, json) {
                    gLocked = false;
                    $('[data-toggle="tooltip"]').tooltip();
                },
                dom: '<"html5buttons"B>trpi',
                buttons: !gFromLink ? buttons : []
            });
        if (showCheckBoxes) {
            GridCommon.AddCheckBoxEvents(gTableName);
        }
        $('#grdSearchItems tbody').on('click', ".showReassignButton", function () {
            const onclick = $(this).attr("clickattr");
            eval(onclick);
        });

        // Targets the <tbody> element inside a table with the ID #grdSearchItems, The click event listener is scoped to rows in the body of the table.
        //  Applies the click event to elements with the class .recall inside the <tbody>. Which are the recall buttons that have the .recall class
        $('#grdSearchItems tbody').on('click', ".recall", function () {
            const onclick = $(this).attr("clickattr");
            eval(onclick);

        });
        $('#grdSearchItems tbody').on('click', ".view", function () {
            var onclick = $(this).attr("clickattr");
            eval(onclick);
        });

        table.$('td').tooltip({
            "delay": 0,
            "track": true,
            "fade": 250
        });
        if (!gFromLink) {
            SecurityMatrix.InitToolbarColor();
            SecurityMatrix.InitContextMenu(securityMatrix, TreeNodes.Search);
        }
    } else {
        GridCommon.Refresh(gTableName);
        gLocked = false;
    }
    firstTime = false;
}



class DocumentManageStructureUsersCorrespondencesView extends Intalio.View {
    constructor(element, model) {
        super(element, "manageStructureUsersCorrespondences", model);
    }
    render() {
        const style = document.createElement('style');
        style.textContent = `
            td.empty {
                display: none;
            }`;
        document.head.appendChild(style);
        let self = this;
        gFromLink = self.model.fromLink;
        $.fn.select2.defaults.set("theme", "bootstrap");
        firstTime = true;

        $('#formPost').keydown(function (e) {
            if (!$(e.target).parent().hasClass('bootstrap-tagsinput')) {
                let code = e.keyCode || e.which;
                if (code === 13) {
                    e.preventDefault();
                    $('#btnSearchFilter').trigger("click");
                }
            }
        });
        $("#btnSearchFilter").on('click', function (e) {

            var $form = $(self.refs['formPost']);
            $form.parsley().reset();
            var isValid = $form.parsley().validate();
            if (isValid) {
                if (!gLocked) {
                    gLocked = true;
                    try {
                        $("#grdSearchItems tbody").empty();
                        search(self);
                        $('#gridPanel').show();
                        $('.btn-scroll').fadeIn();
                        $('.gridResult').fadeIn();
                    } catch (e) {
                        gLocked = false;
                    }
                }
            }
        });
        $("#btnSearchFilterClear").on('click', function () {
            $('#cmbSearchFilterUser').val('').trigger('change');
            $("#cmbMessageType").val('').trigger('change');
            $("#searchFilterKeyword").val('');
            $("#grdSearchItems tbody").empty();
        });
        const dropdownData = [
            { id: "inbox", text: "Inbox" },
            { id: "sent", text: "Sent" }
        ];
        $("#cmbSearchFilterUser").val('').trigger('change');
        createUsersSelect2();
        createPrivaciesSelect2();
        $("#cmbPrivacyLevel").val('').trigger('change');
        $("#cmbMessageType").val('').trigger('change');
        $('#cmbMessageType').select2({
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            width: "100%",
            allowClear: true,
            placeholder: "",
            dropdownParent: $('#messageType'),
            data: dropdownData
        });
    }
}



export default { DocumentManageStructureUsersCorrespondences, DocumentManageStructureUsersCorrespondencesView };