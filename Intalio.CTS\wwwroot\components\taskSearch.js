﻿import Intalio from './common.js'
import DocumentDetails from './documentDetails.js'
import LinkedCorrespondenceIndex from './linkedCorrespondenceIndex.js'
import LinkedCorrespondenceDocument from './linkedCorrespondenceDocument.js'
import ReportCorrespondenceDetailExport from './reportCorrespondenceDetailExport.js'

class TaskSearch extends Intalio.Model {
    constructor() {
        super();
        this.categories = null;
        this.statuses = null;
        this.importances = null;
        this.priorities = null;
        this.privacies = null;
        this.delegationUsers = null;
        this.documentId = null;
        this.fromLink = false;
        this.showOCRContentField = window.EnableOCR && (window.CrawlerServerUrl && window.CrawlerServerUrl.split(Splitter).length > 0);
    }
}
var table = null;
var firstTime = true;
var gLocked = false;
var gTableName = "grdSearchItems";
var gFromLink = false;
var gMaxTagsCharCount = 749;
function isFilled() {
    if ($("#cmbSearchFilterStatus").val() === null
        && $("#searchFilterFromDate").val() === "" && $("#cmbFilterUser").val() === null && $("#cmbFilterAssignedTo").val() === null &&
        $("#searchFilterToDate").val() === "" && $("#cmbSearchFilterPriority").val() === null && $("#txtSearchFilterSubject").val().trim() === ""
        && $("#txtSearchFilterKeyword").val().trim() === ""
        && $("#txtSearchFilterInstructions").val().trim() === ""
        && ($("#txtSearchOCRContent").val() ? $("#txtSearchOCRContent").val().trim() === "" : true)) {
        return false;
    }
    return true;
}



function search(self) {
    if (firstTime) {
        var columns = [];
        Common.gridCommon();
        var securityMatrix = JSON.parse(new CoreComponents.Lookup.SecurityMatrix().get(window.language));// JSON.parse(sessionStorage.getItem("SecurityMatrix"));
        var buttons = SecurityMatrix.getToolbarActions(securityMatrix, TreeNodes.Search, gTableName);
        var showCheckBoxes = self.model.fromLink || buttons.length > 0;
        columns.push({
            visible: showCheckBoxes,
            title: '<input id="chkAll" type="checkbox" />',
            width: '16px',
            "orderable": false,
            "render": function (data, type, row) {
                var searchSelectedDocIds = GridCommon.GetSelectedRows(gTableName);
                var index = searchSelectedDocIds.indexOf(row.id);
                if (index > -1) {
                    return "<input type='checkbox' checked=true onclick='event.stopPropagation();' data-id=" + row.id + " />";
                }
                else {
                    return "<input type='checkbox' onclick='event.stopPropagation();' data-id=" + row.id + " />";
                }
            }
        });
        columns.push({
            "className": 'details-control',
            "orderable": false,
            "data": null,
            "defaultContent": '',
            width: '16px'
        });
        columns.push({ title: "Id", data: "id", visible: false, "orderable": false });
        columns.push({
            title: Resources.Category, data: "categoryId", "orderable": true, orderSequence: ["asc", "desc"],
            render: function (data, type, full, meta) {
                var categories = self.model.categories;
                for (var i = 0; i < categories.length; i++) {
                    if (categories[i].id === data) {
                        return categories[i].text;
                    }
                }
                return "";
            }
        });
        columns.push({ title: Resources.ReferenceNumber, data: "referenceNumber", render: $.fn.dataTable.render.text(), "orderable": true, orderSequence: ["asc", "desc"] });
        columns.push({ title: Resources.Subject, data: "subject", render: $.fn.dataTable.render.text(), "orderable": true, orderSequence: ["asc", "desc"] });
        columns.push({
            title: Resources.CreatedDate, data: "createdDate", "orderable": true, orderSequence: ["asc", "desc"],
            render: function (data, type, full, meta) {
                return DateConverter.toHijriFormated(full.createdDate, null, window.CalendarType);
            }
        });
        columns.push({
            title: Resources.Status, data: "statusId", "orderable": false, width: "50px",
            "render": function (data,type,full,meta) {
                var statuses = self.model.statuses;
                for (var i = 0; i < statuses.length; i++) {
                    if (statuses[i].id === data) {

                        var color = "#27c24c";
                        if (statuses[i].color !== undefined && statuses[i].color !== null ) {
                            color = statuses[i].color;
                        }
                        return "<div class='label' style='background-color:" + color+ "'>" + statuses[i].text + "</div>";
                    }
                }
                return "";
            }
        });
        columns.push({
            'visible': true,
            "className": "text-right",
            "autoWidth": false,
            "bAutoWidth": false,
            width: "16px",
            'orderable': false,
            'sortable': false,
            'render': function (data, type, full, meta) {

                var html = "";
                if (full.isOverDue) {
                    html += "<div class='mr-sm' title='" + Resources.OverDue + "'><i class='fa fa-clock-o fa-lg text-danger'></i></div>&nbsp;";
                }
                if (full.isHasNote) {
                    html += "<div class='mr-sm' title='" + Resources.HasNote + "' style='height: 24px'>" +
                        "<i class='fa fa-sticky-note-o text-info'></i>&nbsp;" +
                        "</div>";
                }

                return "<div id='followUpDivIcons_" + full.id + "' style='display: inline-flex;align-items: center'>" + html + "</div>";
            }
        });

        columns.push({
            "className": "text-right",
            "autoWidth": false,
            "bAutoWidth": false,
            width: "16px",
            'orderable': false,
            'sortable': false,
            'render': function (data, type, full, meta) {
                var delegationId = null;
                if (self.model.delegationUsers.length > 0) {
                    delegationId = $("#cmbSearchFilterDelegation").val() !== null && typeof $("#cmbSearchFilterDelegation").val() !== "undefined" ? $("#cmbSearchFilterDelegation").val() : "0";
                }
                var isReadonly = false;
                let btn = document.createElement("button");
                if (full.readonly==true||full.statusId == FollowUpStatuses.Completed || full.statusId == FollowUpStatuses.Canceled) {
                    isReadonly = true;
                    btn.setAttribute("class", "btn btn-xs btn-warning view");
                    btn.setAttribute("title", Resources.View);
                    btn.setAttribute("id", "FollowUpViewBtn_" + full.id);
                    btn.innerHTML = "<i class='fa fa-eye fa-white'/>";
                } else {

                    btn.setAttribute("class", "btn btn-xs btn-primary edit");
                    btn.setAttribute("title", Resources.Edit);
                    btn.setAttribute("id", "FollowUpEditBtn_" + full.id);
                    btn.innerHTML = "<i class='fa fa-edit fa-white'/>";
                }
                full.readonly = isReadonly == true ? isReadonly : full.readonly;
                btn.setAttribute("clickattr", "openSearchDocument(" + full.id + "," + delegationId + "," + self.model.fromLink + "," + full.followUpDocument.categoryId + "," + full.readonly +")");


                return btn.outerHTML;
            }
        });



        table = $("#grdSearchItems")
            .on('draw.dt', function () {
                $('#grdSearchItems tbody tr td').each(function () {
                    this.setAttribute('title', $(this).text());
                });
                GridCommon.CheckSelectedRows(gTableName);
            })
            .DataTable({
                processing: true,
                ordering: true,
                serverSide: true,
                pageLength: 10,
                "ajax": {
                    "url": "/Search/ListFollowUp",
                    "type": "POST",
                    "datatype": "json",
                    "data": function (d) {
                        var model = {};
                        model.documentId = self.model.documentId;
                        model.status = $("#cmbSearchFilterStatus").val();
                        model.fromDate = $("#searchFilterFromDate").val() !== "" && typeof $("#searchFilterFromDate").val() !== "undefined" ? $("#searchFilterFromDate").val() : "";
                        model.toDate = $("#searchFilterToDate").val() !== "" && typeof $("#searchFilterToDate").val() !== "undefined" ? $("#searchFilterToDate").val() : "";
                        model.priority = $("#cmbSearchFilterPriority").val();
                        model.subject = $("#txtSearchFilterSubject").val().trim() !== "" && typeof $("#txtSearchFilterSubject").val() !== "undefined" ? $("#txtSearchFilterSubject").val().trim() : "";
                        model.keyword = $("#txtSearchFilterKeyword").val().trim() !== "" && typeof $("#txtSearchFilterKeyword").val() !== "undefined" ? $("#txtSearchFilterKeyword").val().trim() : "";
                        model.userIds = $("#cmbFilterUser").val();
                        model.assignedToId = $("#cmbFilterAssignedTo").val();
                        model.isFollowUp = true;
                        model.Category = window.FollowUpCategory;
                        model.structureId = window.EnablePerStructure ? $('#hdLoggedInStructureId').val() : $("#hdStructureId").val();
                        if (self.model.showOCRContentField) {
                            model.ocrContent = $("#txtSearchOCRContent").val();
                        }
                        if (self.model.delegationUsers.length > 0) {
                            model.delegationId = $("#cmbSearchFilterDelegation").val() !== null && typeof $("#cmbSearchFilterDelegation").val() !== "undefined" ? $("#cmbSearchFilterDelegation").val() : "0";
                        }
                        d.__RequestVerificationToken = $('input[name="__RequestVerificationToken"]').val();
                        d.Model = JSON.stringify(model);
                        return d;
                    },
                    "dataSrc": function (response) {
                        $('#grdSearchItems_processing').css('display', 'none');
                        if (response.recordsTotal > 0) {
                            return response.data;
                        } else {
                            if (response.message != undefined && response.message != "") {
                                Common.showScreenErrorMsg(response.message);
                            }
                            response.data = [] //since datatables will be checking for the object as array
                            return response.data;
                        }
                    }
                },
                "order": [],
                "columns": columns,
                "fnInitComplete": function (settings, json) {
                    gLocked = false;
                    $('[data-toggle="tooltip"]').tooltip();
                },
                dom: '<"html5buttons"B>ltrpi',
                buttons: !gFromLink ? buttons : []
            });
        if (showCheckBoxes) {
            GridCommon.AddCheckBoxEvents(gTableName);
        }
        $('#grdSearchItems tbody').on('click', ".view,.edit", function () {
            var onclick = $(this).attr("clickattr");
            eval(onclick);
        });
        $('#grdSearchItems tbody').on('dblclick', 'tr', function () {
            var onclick = $(this).find(".view").attr("clickattr");
            eval(onclick);
        });
        $('#grdSearchItems tbody').on('click', 'td.details-control', function () {
            let tr = $(this).closest('tr');
            let row = table.row(tr);
            if (row.child.isShown()) {
                row.child.hide();
                tr.removeClass('shown');
            }
            else {
                row.child(format(row.data(), self.model.importances, self.model.priorities, self.model.privacies)).show();
                tr.addClass('shown');
            }
        });
        table.$('td').tooltip({
            "delay": 0,
            "track": true,
            "fade": 250
        });
        if (!gFromLink) {
            SecurityMatrix.InitToolbarColor();
            SecurityMatrix.InitContextMenu(securityMatrix, TreeNodes.Search);
        }
    } else {
        GridCommon.Refresh(gTableName);
        gLocked = false;
    }
    firstTime = false;
}
function format(row, importances, priorities, privacies) {
    var importance = "", priority = "", privacy = "";
    for (let i = 0; i < priorities.length; i++) {
        if (priorities[i].id === row.priorityId) {
            priority = priorities[i].text;
        }
    }
    for (let i = 0; i < privacies.length; i++) {
        if (privacies[i].id === row.privacyId) {
            privacy = privacies[i].text;
        }
    }
    for (let i = 0; i < importances.length; i++) {
        if (importances[i].id === row.importanceId) {
            importance = importances[i].text;
        }
    }
    return '<table style="width:100%" cellspacing="0" border="0">' +
        '<tr>' +
        '<th style="width: 10%;padding:5px">' + Resources.SendingEntity + ':</th>' +
        '<td style="width: 90%;padding:5px;word-break: break-all;"">' + (row.sendingEntity || '') + '</td>' +
        '</tr>' +
        '<tr>' +
        '<th style="width: 10%;padding:5px">' + Resources.ReceivingEntity + ':</th>' +
        '<td style="width: 90%;padding:5px;word-break: break-all;"">' + (row.receivingEntity || '') + '</td>' +
        '</tr>' +
        '<tr>' +
        '<th style="width: 10%;padding:5px">' + Resources.Priority + ':</th>' +
        '<td style="width: 90%;padding:5px;word-break: break-all;"">' + priority + '</td>' +
        '</tr>' +
        '<tr>' +
        '<th style="width: 10%;padding:5px">' + Resources.Privacy + ':</th>' +
        '<td style="width: 90%;padding:5px;word-break: break-all;"">' + privacy + '</td>' +
        '</tr>' +
        '<tr>' +
        '<th style="width: 10%;padding:5px">' + Resources.Importance + ':</th>' +
        '<td style="width: 90%;padding:5px;word-break: break-all;"">' + importance + '</td>' +
        '</tr>' +
        '<tr>' +
        '<th style="width: 10%;padding:5px">' + Resources.CreatedBy + ':</th>' +
        '<td style="width: 90%;padding:5px;word-break: break-all;"">' + (row.createdByUser || '') + '</td>' +
        '</tr>' +
        '<tr>' +
        '<th style="width: 10%;padding:5px">' + Resources.DueDate + ':</th>' +
        '<td style="width: 90%;padding:5px;word-break: break-all;"">' + (DateConverter.toHijriFormated(row.dueDate, null, window.CalendarType) || '') + '</td>' +
        '</tr>' +
        '</table>';
}
function openSearchDocument(id, delegationId, fromLink, categoryId, readonly) {
    if (categoryId == window.FollowUpCategory) {
        Common.ajaxGet('/FollowUp/Get', { id: id }, function (result) {
            if (result.status) {
                if (result.model) {
                    var data = result.model;
                    Common.setActiveSidebarMenu("liFollowUp");
                    $(".delegation").removeClass("active");

                    var wrapper = $(".modal-documents");
                    var linkedCorrespondenceModel = new LinkedCorrespondenceIndex.LinkedCorrespondenceIndex();
                    var linkedCorrespondenceDocument = new LinkedCorrespondenceDocument.LinkedCorrespondenceDocumentView(wrapper, linkedCorrespondenceModel);
                    linkedCorrespondenceDocument.render();

                    var model = new DocumentDetails.DocumentDetails();
                    model.followupId = id;
                    model.originalDocumentId = data.originalDocument.id;
                    model.documentId = data.followUpDocument.id;
                    model.categoryId = window.FollowUpCategory;
                    model.showMyTransfer = false;
                    model.readonly = readonly == true ? readonly : data.readonly;
                    var securityMatrix = JSON.parse(new CoreComponents.Lookup.SecurityMatrix().get(window.language));
                    var tabs = [];
                    var nodeId = $('[data-inherit="' + TreeNode.FollowUp + '"]').first().data("id");
                    if (nodeId !== undefined && $.isNumeric(nodeId)) {
                        tabs = securityMatrix.SecurityNodes[nodeId].SecurityCategories[window.FollowUpCategory].Tabs;
                        model.tabsActions = securityMatrix.SecurityNodes[nodeId].SecurityCategories[window.FollowUpCategory].SecurityTabs;
                    }
                    model.tabs = $.grep(tabs, function (element, index) {
                        return !element.Name.includes("myTransfer") && !element.Name.includes("documentMetadata") &&
                            !element.Name.includes("notes") && !element.Name.includes("visualTracking") &&
                            !element.Name.includes("activityLog") &&
                            !element.Name.includes("linkedDocument") && !element.Name.includes("transferHistory");
                    });
                    //model.tabsActions = securityMatrix.SecurityNodes[nodeId].SecurityCategories[window.FollowUpCategory].SecurityTabs;
                    model.tabsWithStatic = tabs;
                    model.showBackButton = false;
                    model.fromDraft = false;
                    model.parentComponentId = linkedCorrespondenceDocument.model.ComponentId
                    model.isModal = true;
                    model.teamId = data.teamId
                    model.isPrivateFollowUp = data.isPrivate
                    wrapper = $(linkedCorrespondenceDocument.refs['linkDocumentDiv']);

                    var documentView = new DocumentDetails.DocumentDetailsView(wrapper, model);
                    documentView.render();

                    $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).off("hidden.bs.modal");
                    $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).off("shown.bs.modal");
                    $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).on('shown.bs.modal', function () {
                        $("ul[ref='tabDocumentDetails']").scrollingTabs('refresh');
                    });
                    $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).on('hidden.bs.modal', function () {
                        if ($(this).data("remove") != true)
                            return;
                        $(linkedCorrespondenceDocument.refs[linkedCorrespondenceDocument.model.ComponentId]).remove();
                        swal.close();
                        //if ($(".modal-window").children().length > 0 || $(".modal-documents").children().length > 0) {
                        //    $('body').addClass('modal-open');
                        //}
                    });
                    $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).modal("show");
                }
            } else {
                if (result.message) {
                    Common.showScreenErrorMsg(result.message);
                }
            }
        }, function () { Common.showScreenErrorMsg(); }, true);

    }
    else {
        var params = { id: id };
        if (delegationId !== null) {
            params.delegationId = delegationId;
        }
        Common.ajaxGet('/Document/GetSearchDocument', params, function (response) {
            if (response && response === "NoAccess") {
                Common.alertMsg(Resources.NoPermission);
            } else {
                Common.setActiveSidebarMenu("liSearch");
                $(".delegation").removeClass("active");
                $("#searchTaskContainerDiv").hide();

                var model = new DocumentDetails.DocumentDetails();
                model.documentModel = response;
                if (response.status == 2 || response.status == 4) {
                    model.readonly = false;

                }
                else {
                    model.readonly = true;

                }
                model.delegationId = delegationId;
                model.documentId = response.id;
                model.referenceNumber = response.referenceNumber;
                model.categoryName = response.categoryName;
                model.statusId = response.status;
                model.documentModel = response;
                model.statuses = new CoreComponents.Lookup.Statuses().get(window.language);
                model.showMyTransfer = false;
                model.fromSearch = true;
                model.attachmentId = response.attachmentId;
                model.attachmentVersion = response.attachmentVersion;
                var securityMatrix = JSON.parse(new CoreComponents.Lookup.SecurityMatrix().get(window.language));// JSON.parse(sessionStorage.getItem("SecurityMatrix"));
                var tabs = securityMatrix.SecurityNodes[Number(TreeNodes.Search)].SecurityCategories[response.categoryId].Tabs;
                model.tabs = $.grep(tabs, function (element, index) {
                    return !element.Name.includes("myTransfer") && !element.Name.includes("documentMetadata") &&
                        !element.Name.includes("transferHistory") && !element.Name.includes("visualTracking") &&
                        !element.Name.includes("activityLog") && !element.Name.includes("attachments") && !element.Name.includes("notes") &&
                        !element.Name.includes("linkedDocument") && !element.Name.includes("nonArchivedAttachments");
                });
                model.tabsWithStatic = tabs;
                model.tabsActions = securityMatrix.SecurityNodes[Number(TreeNodes.Search)].SecurityCategories[response.categoryId].SecurityTabs;
                var wrapper = $(".content-wrapper");
                var view = new DocumentDetails.DocumentDetailsView(wrapper, model);
                view.render();
                if (!gFromLink) {
                    $(document).off('click', '.btn-back');
                    $(document).on('click', '.btn-back', function () {
                        $("#searchTaskContainerDiv").show();
                        view.remove();
                        $(".toRemove").remove();
                    });

                    $(document).off('click', '.btn-export');
                    $(document).on('click', '.btn-export', function () {
                        var wrapper = $(".modal-window");
                        var model = new ReportCorrespondenceDetailExport.ReportCorrespondenceDetailExport();
                        model.documentId = response.id;
                        model.delegationId = delegationId;
                        var reportCorrespondenceDetailExportView = new ReportCorrespondenceDetailExport.ReportCorrespondenceDetailExportView(wrapper, model);
                        reportCorrespondenceDetailExportView.render();

                        $("#modalReportCorrespondenceDetailExport").off("hidden.bs.modal");
                        $("#modalReportCorrespondenceDetailExport").off("shown.bs.modal");
                        $("#modalReportCorrespondenceDetailExport").on('shown.bs.modal', function () { });
                        $("#modalReportCorrespondenceDetailExport").on('hidden.bs.modal', function () { });
                        $("#modalReportCorrespondenceDetailExport").modal("show");
                    });
                }
            }
        }, function () { Common.showScreenErrorMsg(); }, true);
    }
}

function createUsersSelect2() {
    var headers = {};
    var url = window.IdentityUrl + '/api/SearchUsers';
    headers.Authorization = 'Bearer ' + window.IdentityAccessToken;
    $("#cmbSearchFilterFromUser").select2({
        minimumInputLength: 0,
        allowClear: true,
        placeholder: "",
        dir: window.language === "ar" ? "rtl" : "ltr",
        language: window.language,
        dropdownParent: $('#fromUserSearchFilterContainer'),
        width: "100%",
        ajax: {
            delay: 250,
            url: url,
            type: "Get",
            dataType: 'json',
            headers: typeof headers !== "undefined" ? headers : "",
            data: function (term) {
                //return { "text":  "", "language": window.language };
                return { "text": term.term ? term.term : "", "language": window.language };
            },
            processResults: function (data, term) {
                return {
                    results: userDataForSelect2(data, term)
                };
            }
        }
    });
    $("#cmbSearchFilterFromUser").val('').trigger('change');
    $("#cmbSearchFilterToUser").select2({
        minimumInputLength: 0,
        allowClear: true,
        placeholder: "",
        dir: window.language === "ar" ? "rtl" : "ltr",
        language: window.language,
        dropdownParent: $('#toUserSearchFilterContainer'),
        width: "100%",
        ajax: {
            delay: 250,
            url: url,
            type: "Get",
            dataType: 'json',
            headers: typeof headers !== "undefined" ? headers : "",
            data: function (term) {
                //return { "text":  "", "language": window.language };
                return { "text": term.term ? term.term : "", "language": window.language };
            },
            processResults: function (data, term) {
                return {
                    results: userDataForSelect2(data, term)
                };
            }
        }
    });
    $("#cmbSearchFilterToUser").val('').trigger('change');
}
function userDataForSelect2(data, term) {
    var termSearch = term.term ? term.term : "";
    var retVal = [];
    $.each(data, function (key, val) {

        var fullName = val.fullName;
        if (window.language != 'en') {
            fullName = getFullNameByLangauge(val);
            fullName = fullName.trim() == "" ? val.fullName : fullName;
        }
        var allNames = getFullNameInAllLangauge(val);
        if (allNames.length == 0) allNames.push(fullName);
        if (termSearch != "" &&
            !allNames.some(r => r.toLowerCase().includes(termSearch.toLowerCase()))) {
            return;
        }
        let isExist = retVal.some(function (usr) { return usr.id === val.id; });
        if (!isExist) {
            var item = {};
            item.id = val.id;
            item.text = fullName;
            item.isStructure = false;
            item.dataId = val.id;
            retVal.push(item);
        }
    });
    return retVal;
}
function createStructuresSelect2() {
    var headers = {};
    var url = window.IdentityUrl + '/api/SearchStructuresWithSearchAttributes';
    headers.Authorization = 'Bearer ' + window.IdentityAccessToken;
    $("#cmbSearchFilterDocumentSender").select2({
        minimumInputLength: 0,
        allowClear: true,
        placeholder: "",
        dir: window.language === "ar" ? "rtl" : "ltr",
        language: window.language,
        dropdownParent: $('#documentSenderSearchFilterContainer'),
        width: "100%",
        ajax: {
            delay: 250,
            url: url,
            type: "POST",
            dataType: 'json',
            headers: typeof headers !== "undefined" ? headers : "",
            data: function (term) {
                return { "text": term.term ? term.term : "", "attributes": [window.StructureNameAr, window.StructureNameFr] };
            },
            processResults: function (data) {
                return {
                    results: structureDataForSelect2(data)
                };
            }
        }
    });
    //$("#cmbSearchFilterDocumentSender").val('').trigger('change');
    //$("#cmbSearchFilterDocumentReceiver").select2({
    //    minimumInputLength: 0,
    //    allowClear: true,
    //    placeholder: "",
    //    dir: window.language === "ar" ? "rtl" : "ltr",
    //    language: window.language,
    //    dropdownParent: $('#documentReceiverSearchFilterContainer'),
    //    width: "100%",
    //    ajax: {
    //        delay: 250,
    //        url: url,
    //        type: "POST",
    //        dataType: 'json',
    //        headers: typeof headers !== "undefined" ? headers : "",
    //        data: function (term) {
    //            return { "text": term.term ? term.term : "", "attributes": [window.StructureNameAr, window.StructureNameFr] };
    //        },
    //        processResults: function (data) {
    //            return {
    //                results: structureDataForSelect2(data)
    //            };
    //        }
    //    }
    //});
    //$("#cmbSearchFilterDocumentReceiver").val('').trigger('change');
    //$("#cmbSearchFilterFromStructure").select2({
    //    minimumInputLength: 0,
    //    allowClear: true,
    //    placeholder: "",
    //    dir: window.language === "ar" ? "rtl" : "ltr",
    //    language: window.language,
    //    dropdownParent: $('#fromStructureSearchFilterContainer'),
    //    width: "100%",
    //    ajax: {
    //        delay: 250,
    //        url: url,
    //        type: "POST",
    //        dataType: 'json',
    //        headers: typeof headers !== "undefined" ? headers : "",
    //        data: function (term) {
    //            return { "text": term.term ? term.term : "", "attributes": [window.StructureNameAr, window.StructureNameFr] };
    //        },
    //        processResults: function (data) {
    //            return {
    //                results: structureDataForSelect2(data)
    //            };
    //        }
    //    }
    //});
    //$("#cmbSearchFilterFromStructure").val('').trigger('change');
    //$("#cmbSearchFilterToStructure").select2({
    //    minimumInputLength: 0,
    //    allowClear: true,
    //    placeholder: "",
    //    dir: window.language === "ar" ? "rtl" : "ltr",
    //    language: window.language,
    //    dropdownParent: $('#toStructureSearchFilterContainer'),
    //    width: "100%",
    //    ajax: {
    //        delay: 250,
    //        url: url,
    //        type: "POST",
    //        dataType: 'json',
    //        headers: typeof headers !== "undefined" ? headers : "",
    //        data: function (term) {
    //            return { "text": term.term ? term.term : "", "attributes": [window.StructureNameAr, window.StructureNameFr] };
    //        },
    //        processResults: function (data) {
    //            return {
    //                results: structureDataForSelect2(data)
    //            };
    //        }
    //    }
    //});
    //$("#cmbSearchFilterToStructure").val('').trigger('change');

}
function getStructureName(data) {
    var structureName = data.name;
    if (data.attributes != null && data.attributes.length > 0) {
        var attributeLang = $.grep(data.attributes, function (e) {
            return e.text === window.StructureNameAr && window.language === "ar" ? e.value : (e.text === window.StructureNameFr && window.language === "fr" ? e.value : "");
        });
        if (attributeLang.length > 0) {
            structureName = attributeLang[0].value === "" ? structureName : attributeLang[0].value;
        }
    }
    return structureName;
}
function structureDataForSelect2(data) {
    var retVal = [];
    if (typeof data !== 'undefined' && data.items) {
        for (var i = 0; i < data.items.length; i++) {
            retVal.push({
                id: data.items[i].id,
                text: getStructureName(data.items[i])
            });
        }
    } else if (data) {
        for (var i = 0; i < data.length; i++) {
            retVal.push({
                id: data[i].id,
                text: getStructureName(data[i])
            });
        }
    }
    return retVal;
}
class TaskSearchView extends Intalio.View {
    constructor(element, model) {
        super(element, "tasksearch", model);
    }

    render() {

        var self = this;
        gFromLink = self.model.fromLink;
        $.fn.select2.defaults.set("theme", "bootstrap");
        firstTime = true;

        $('#formPost').keydown(function (e) {
            if (!$(e.target).parent().hasClass('bootstrap-tagsinput')) {
                var code = e.keyCode || e.which;
                if (code === 13) {
                    e.preventDefault();
                    $('#btnSearchFilter').trigger("click");
                }
            }
        });
        $("#btnSearchFilter").on('click', function (e) {
            if (isFilled()) {
                if (!gLocked) {
                    gLocked = true;
                    try {

                        $("#grdSearchItems tbody").empty();
                        search(self);
                        //$('.search').hide();
                        //$("#expandIcon").show();
                        //$('.btn-scroll').fadeIn();
                        $('.gridResult').fadeIn();
                        //$("#expandIcon").removeClass("fa-compress").addClass("fa-expand");
                        //$("#resultExpandIcon").removeClass("fa-compress").addClass("fa-expand");
                    } catch (e) {
                        gLocked = false;
                    }
                }
            } else {
                Common.alertMsg(Resources.FillSearchCritirea);
            }
        });
        $("#btnSearchFilterClear").on('click', function () {

            $("#cmbSearchFilterStatus").val('').trigger('change');
            $("#cmbSearchFilterPriority").val('').trigger('change');
            $("#txtSearchFilterSubject").val('');
            $("#txtSearchFilterKeyword").val('');
            $("#searchFilterFromDate").val('').trigger('change');
            $("#searchFilterToDate").val('').trigger('change');
            $("#cmbFilterUser").val('').trigger('change');
            $("#cmbFilterAssignedTo").val('').trigger('change');

            $("#txtSearchFilterInstructions").val('');
            $('.with-border').addClass('without-border').removeClass('with-border');
            $("#expandIcon").hide();
            $('.gridResult').hide();

            //$("#grdSearchItems tbody").empty();
        });
        createUsersSelect2();
        createStructuresSelect2();
        if (self.model.delegationUsers.length > 0) {
            $('#cmbSearchFilterDelegation').select2({
                dir: window.language === "ar" ? "rtl" : "ltr",
                language: window.language,
                width: "100%",
                allowClear: false,
                placeholder: "",
                dropdownParent: $('#delegationSearchFilterContainer')
            });
        }
        $('#cmbSearchFilterStatus').select2({
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            width: "100%",
            allowClear: true,
            placeholder: "",
            dropdownParent: $('#statusSearchFilterContainer')
        });
        $("#cmbSearchFilterStatus").val('').trigger('change');


        $('#cmbSearchFilterPriority').select2({
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            width: "100%",
            allowClear: true,
            placeholder: "",
            dropdownParent: $('#prioritySearchFilterContainer')
        });
        $("#cmbSearchFilterPriority").val('').trigger('change');
        var searchFromDate = $('#searchFilterFromDate').flatpickr({
            noCalendar: false,
            enableTime: false,
            dateFormat: 'd/m/Y',
            onOpen: function (ct) {
                this.set({
                    maxDate: jQuery('#searchFilterToDate').val() && jQuery('#searchFilterToDate').val() !== "" ? jQuery('#searchFilterToDate').val() : false
                });
                //if (!(jQuery('#searchFilterFromDate').val() && jQuery('#searchFilterFromDate').val() !== "")) {
                //    this.set({
                //        maxDate: moment().format('DD/MM/YYYY')
                //    });
                //}
            }
        });
        $("#searchFilterFromDate_img").click(function () {
            searchFromDate.toggle();
        });
        var searchToDate = $('#searchFilterToDate').flatpickr({
            noCalendar: false,
            enableTime: false,
            dateFormat: 'd/m/Y',
            onOpen: function (ct) {
                this.set({
                    minDate: jQuery('#searchFilterFromDate').val() && jQuery('#searchFilterFromDate').val() !== "" ? jQuery('#searchFilterFromDate').val() : false
                });
            }
        });
        $("#searchFilterToDate_img").click(function () {
            searchToDate.toggle();
        });
        $('span[aria-labelledby=select2-cmbSearchFilterCategory-container]').keydown(function (e) {
            var code = e.keyCode || e.which;
            if (code === 9) {
                e.preventDefault();
                if (e.shiftKey) {
                    $('#btnSearchFilterClear').focus();
                }
                else {
                    //$('#txtSearchFilterReferenceNumber').focus();
                }
            }
        });
        $('.usersFilter').select2({
            minimumInputLength: 0,
            allowClear: false,
            placeholder: "",
            dir: window.language === "ar" ? "rtl" : "ltr",
            language: window.language,
            dropdownParent: $("#userFilterContainer"),
            multiple: false,
            width: "100%",
            ajax: {
                delay: 400,
                url: window.IdentityUrl + '/api/SearchUsers',
                type: "GET",
                dataType: 'json',
                headers: { Authorization: 'Bearer ' + window.IdentityAccessToken },
                data: function (term) {
                    return { "text": "", "language": window.language };
                },
                processResults: function (data, term) {
                    var termSearch = term.term ? term.term : "";

                    var listitemsMultiList = [];
                    $.each(data, function (key, val) {
                        var fullName = val.fullName;
                        if (window.language != 'en') {
                            fullName = getFullNameByLangauge(val);
                            fullName = fullName.trim() == "" ? val.fullName : fullName;
                        }
                        var allNames = getFullNameInAllLangauge(val);
                        if (allNames.length == 0) allNames.push(fullName);
                        if (termSearch != "" &&
                            !allNames.some(r => r.toLowerCase().includes(termSearch.toLowerCase()))) {
                            return;
                        }
                        let isExist = listitemsMultiList.some(function (usr) { return usr.id === val.id; });
                        if (!isExist) {
                            var item = {};
                            item.id = val.id;
                            item.text = fullName;
                            item.isStructure = false;
                            item.dataId = val.id;
                            listitemsMultiList.push(item);
                        }
                    });
                    return {
                        results: listitemsMultiList
                    };
                }
            },
            sorter: data => data.sort((a, b) => a.text.localeCompare(b.text))
        });
        $("#cmbFilterUser").val('').trigger('change');
        $("#cmbFilterAssignedTo").val('').trigger('change');
        $('#btnSearchFilterClear').keydown(function (e) {
            var code = e.keyCode || e.which;
            if (code === 9) {
                e.preventDefault();
                if (e.shiftKey) {
                    $('#btnSearchFilter').focus();
                }
                else {
                    //$('#txtSearchFilterReferenceNumber').focus();
                }
            }
        });
        $('.btn-scroll').on('click', function () {
            if ($('.gridResult').is(":visible")) {
                $('.search').fadeIn();
                $('.gridResult').hide();
                $("#expandIcon").removeClass("fa-expand").addClass("fa-compress");
                $("#resultExpandIcon").removeClass("fa-expand").addClass("fa-compress");
            } else {
                $('.gridResult').fadeIn();
                $('.search').hide();
                $("#expandIcon").removeClass("fa-compress").addClass("fa-expand");
                $("#resultExpandIcon").removeClass("fa-compress").addClass("fa-expand");
            }
        });
        if (self.model.fromLink) {
            $('.panel.panel-default').addClass('borderTop-1');
        }
    }
}
export default { TaskSearch, TaskSearchView };
