﻿import Intalio from './common.js'
//import LinkedCorrespondenceIndex from './linkedCorrespondenceIndex.js'
import AgendaCorrespondenceDocument from './AgendaCorrespondenceDocument.js'
import AgendaResolutionIndex from './agendaresolution.js'
import DocumentSearch from './search.js'
import DocumentDetails from './documentDetails.js'
import { Categories, DelegationUsers } from './lookup.js'
var gIsLocked = false;
var gPaddingTop = 0;
var gWidth = 100;

function showHideAgendaResolution(id, documentId, delegationId, data) {
    let modalWrapper = $(".modal-window");
    let modelIndex = new AgendaResolutionIndex.AgendaResolutionIndex();
    modelIndex.data = data;
    modelIndex.id = id;
    modelIndex.documentId = documentId;
    modelIndex.delegationId = delegationId;
    let agendaResolutionIndexView = new AgendaResolutionIndex.AgendaResolutionIndexView(modalWrapper, modelIndex);
    agendaResolutionIndexView.render();
    $('#modalAgendaResolution').modal('show');
    $("#modalAgendaResolution").off("hidden.bs.modal");
    $("#modalAgendaResolution").off("shown.bs.modal");
    $('#modalAgendaResolution').on('shown.bs.modal', function () {
    });
    $('#modalAgendaResolution').on('hidden.bs.modal', function () {
        $('#modalAgendaResolution').remove();
    });
}
function deleteDocuments(self) {
    try {
        var checkedRows = $(self.refs['grdAgendaTopicsList']).find('input[type="checkbox"]:checked');
        if (checkedRows.length > 0) {
            Common.showConfirmMsg(Resources.DeleteConfirmation, function () {
                var ids = new Array();
                checkedRows.each(function (index, obj) {
                    ids.push(parseInt(obj.getAttribute('data-id')));
                });
                Common.ajaxDelete('/Document/DeleteDocumentCustomInfo',
                    {
                        'documentId': self.model.documentId,
                        'ids': ids, '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
                    },
                    function (data) {
                        if (data) {
                            Common.showScreenSuccessMsg();
                            $(self.refs['grdAgendaTopicsList']).DataTable().ajax.reload();
                        }
                        else {
                            Common.showScreenErrorMsg();

                        }
                    }, null, false);
            });
        }
        else {
            Common.alertMsg(Resources.NoRowSelected);
        }
    } catch (ex) {
        gIsLocked = false;
    }
}


function createSepResolutionDocument(id, templateId, self) {

    try {
        var success = false;
        var checkedRows = $(self.refs['grdAgendaTopicsList']).find('input[type="checkbox"]:checked').not('#chkAllRes');;
        if (checkedRows.length > 0) {
            Common.mask(document.body, "body-mask");
            var createdByStructureId;
            if (window.EnablePerStructure) {
                if ($("#hdLoggedInStructureId").val() != null && $("#hdLoggedInStructureId").val() != "")
                    createdByStructureId = $("#hdLoggedInStructureId").val();
                else if (sessionStorage.getItem($("#hdUserId").val() + "loggedInStructure") !== null) {
                    createdByStructureId = sessionStorage.getItem($("#hdUserId").val() + "loggedInStructure");
                }
            }
            else {
                if (sessionStorage.getItem($("#hdUserId").val() + "SelectedUserStrcuture") !== null) {
                    createdByStructureId = sessionStorage.getItem($("#hdUserId").val() + "SelectedUserStrcuture");
                }
                else if ($("#hdStructureId").val() != null && $("#hdStructureId").val() != "")
                    createdByStructureId = $("#hdStructureId").val();
                }
            
            checkedRows.each(function (index, obj) {
                var ids = new Array();
                ids.push(parseInt(obj.getAttribute('data-id')));

                var params = {
                    'CategoryId': id,
                    "TemplateId": templateId,
                    "SelectedDocumentIds": ids,
                    "PreviousDocumentId": self.model.documentId,
                    'CreatedByStructureId': createdByStructureId,
                    '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
                };

                $.ajax({
                    async: false,
                    type: 'POST',
                    url: '/Document/CreateMeetingDocument',
                    data: params,
                    success: function (data) {
                        Common.unmask("body-mask");
                        if (data.state == "1") {
                            Common.alertMsg(Resources.NoPermssionCreateMeeting);
                        }
                        else {
                            success = true;
                            TreeNode.addToNodeCount(TreeNode.Draft, 1, 1);
                        }
                    },
                    error: function () {
                        Common.showScreenErrorMsg();
                    }
                });
            });
            if (success) {
                window.location.href = "/#draft/1";
            }
        }
        else {
            Common.alertMsg(Resources.NoRowSelected);
        }
    } catch (ex) {
        gIsLocked = false;
    }
}
function createMeetingMinutesDocument(id, templateId, self) {

    try {

        var checkedRows = $(self.refs['grdAgendaTopicsList']).find('input[type="checkbox"]:checked').not('#chkAllRes');
        if (checkedRows.length > 0) {
            Common.mask(document.body, "body-mask");

            var createdByStructureId;
            if (window.EnablePerStructure) {
                if ($("#hdLoggedInStructureId").val() != null && $("#hdLoggedInStructureId").val() != "")
                    createdByStructureId = $("#hdLoggedInStructureId").val();
                else if (sessionStorage.getItem($("#hdUserId").val() + "loggedInStructure") !== null) {
                    createdByStructureId = sessionStorage.getItem($("#hdUserId").val() + "loggedInStructure");
                }
            }
            else {
                if (sessionStorage.getItem($("#hdUserId").val() + "SelectedUserStrcuture") !== null) {
                    createdByStructureId = sessionStorage.getItem($("#hdUserId").val() + "SelectedUserStrcuture");
                }
                else if ($("#hdStructureId").val() != null && $("#hdStructureId").val() != "")
                    createdByStructureId = $("#hdStructureId").val();
                }
            

            var ids = new Array();
            checkedRows.each(function (index, obj) {
                ids.push(parseInt(obj.getAttribute('data-id')));
            });
            var params = {
                'CategoryId': id,
                "TemplateId": templateId,
                "SelectedDocumentIds": ids,
                "PreviousDocumentId": self.model.documentId,
                'CreatedByStructureId': createdByStructureId,
                '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
            };
            Common.ajaxPost('/Document/CreateMeetingDocument', params, function (data) {
                Common.unmask("body-mask");
                if (data.state == "1") {
                    Common.alertMsg(Resources.NoPermssionCreateMeeting);
                }
                else {
                    TreeNode.addToNodeCount(TreeNode.Draft, 1, 1);
                    window.location.href = "/#document/" + data.id;
                }
            }, function () {
                Common.showScreenErrorMsg();
            });
        }
        else {
            Common.alertMsg(Resources.NoRowSelected);
        }
    } catch (ex) {
        gIsLocked = false;
    }
}
function openLinkWindow(self) {
    if (!self.model.readOnly) {
        var statuses = new CoreComponents.Lookup.Statuses().get(window.language);
        var wrapper = $(".modal-window");
        var modelIndex = new LinkedCorrespondenceIndex.LinkedCorrespondenceIndex();
        modelIndex.transferId = self.model.transferId;
        modelIndex.documentId = self.model.documentId;
        modelIndex.delegationId = self.model.delegationId;
        var linkedCorrespondenceIndex = new LinkedCorrespondenceIndex.LinkedCorrespondenceIndexView(wrapper, modelIndex);
        linkedCorrespondenceIndex.render();
        var wrapper = $("#linkSearchDiv");
        let model = new DocumentSearch.DocumentSearch();
        model.categories = new Categories().get(window.language);
        model.statuses = statuses.filter(function (el) { return el.text !== Resources.Draft; });
        model.priorities = new CoreComponents.Lookup.Priorities().get(window.language);
        model.delegationUsers = new DelegationUsers().get(window.language);
        model.privacies = new CoreComponents.Lookup.Privacies().get(window.language);
        model.importances = new CoreComponents.Lookup.Importances().get(window.language);
        model.documentId = self.model.documentId;
        model.fromLink = true;
        let view = new DocumentSearch.DocumentSearchView(wrapper, model);
        view.render();
        $('#modalLinkedCorrespondenceTitle').html(Resources.New);
        $('#modalLinkedCorrespondence').modal("show");
        $('#modalLinkedCorrespondence').off("hidden.bs.modal");
        $('#modalLinkedCorrespondence').off("shown.bs.modal");
        $('#modalLinkedCorrespondence').on('shown.bs.modal', function () {
            $('#searchContainerDiv').find("h3").hide();
            setTimeout(function () { $('#cmbSearchFilterCategory').focus(); }, 200);
        });
        $('#modalLinkedCorrespondence').on('hidden.bs.modal', function () {
            $('.search').fadeIn();
            $('.gridResult').hide();
            $('.btn-scroll').hide();
            GridCommon.Clear("grdSearchItems");
            $("#btnSearchFilterClear").trigger('click');
            $('#modalLinkedCorrespondence').remove();
            swal.close();
            //if ($(".modal-window").children().length > 0 || $(".modal-documents").children().length > 0) {
            //    $('body').addClass('modal-open');
            //}
        });
    }

}
function openLinkedDocument(documentId, delegationId, mainDocumentId) {
    var params = { id: documentId };
    if (mainDocumentId !== null) {
        //params.parentDocumentId = mainDocumentId;
    }

    if (delegationId !== null) {
        params.delegationId = delegationId;
    }
    Common.ajaxGet('/Document/GetSearchDocument', params, function (response) {
        if (response && response === "NoAccess") {
            Common.alertMsg(Resources.NoPermission);
        } else {
            if (!response.id) {
                return;
            }
            var wrapper = $(".modal-window");
            var model = new AgendaCorrespondenceDocument.AgendaCorrespondenceDocument();
            var linkedCorrespondenceDocument = new AgendaCorrespondenceDocument.AgendaCorrespondenceDocumentView(wrapper, model);
            linkedCorrespondenceDocument.render();

            model = new DocumentDetails.DocumentDetails();
            model.documentModel = response;
            model.readonly = true;
            model.delegationId = delegationId;
            model.documentId = response.id;
            model.referenceNumber = response.referenceNumber;
            model.categoryName = response.categoryName;
            model.statusId = response.status;
            model.attachmentVersion = response.attachmentVersion;
            model.createdByUser = response.createdByUser;
            model.statuses = new CoreComponents.Lookup.Statuses().get(window.language);
            model.showMyTransfer = false;
            var securityMatrix = JSON.parse(new CoreComponents.Lookup.SecurityMatrix().get(window.language));// JSON.parse(sessionStorage.getItem("SecurityMatrix"));
            var tabs = [];
            var nodeId = /*$('[data-inherit="' + TreeNode.Inbox + '"]').first().data("id")*/ TreeNodes.Search;
            if (nodeId !== undefined && $.isNumeric(nodeId)) {
                tabs = securityMatrix.SecurityNodes[nodeId].SecurityCategories[response.categoryId].Tabs;
                model.tabsActions = securityMatrix.SecurityNodes[nodeId].SecurityCategories[response.categoryId].SecurityTabs;
            }
            model.tabs = $.grep(tabs, function (element, index) {
                return !element.Name.includes("myTransfer") && !element.Name.includes("documentMetadata") &&
                    !element.Name.includes("notes") &&
                    !element.Name.includes("visualTracking") && !element.Name.includes("activityLog") &&
                    !element.Name.includes("linkedDocument") && !element.Name.includes("transferHistory")
                    && !element.Name.includes("attachments") && !element.Name.includes("nonArchivedAttachments");
            });
            model.tabsWithStatic = tabs;
            model.showBackButton = false;
            model.isModal = true;
            model.attachmentId = response.attachmentId;
            model.parentLinkedDocumentId = mainDocumentId;
            wrapper = $(linkedCorrespondenceDocument.refs['AgendaDocumentDiv']);
            var view = new DocumentDetails.DocumentDetailsView(wrapper, model);
            view.render();
            $($(view.refs['documentDetailsContainerDiv']).find('.div-flex')[0]).addClass('width100pc-ml0');
            var title = response.categoryName;
            if (response.referenceNumber) {
                title += ' - ' + response.referenceNumber;
            }
            if (response.createdByUser) {
                title += ' - ' + response.createdByUser;
            }
            $(linkedCorrespondenceDocument.refs['modaAgendaCorrespondenceDocumentTitle']).html(title);
            $(linkedCorrespondenceDocument.refs['modalAgendaCorrespondenceDocument']).removeAttr("style");
            $(linkedCorrespondenceDocument.refs['modal-dialog']).attr("style", "width:" + gWidth + "% !important;display: block;padding-left: 9px !important;padding-right: 9px !important;padding-top:" + gPaddingTop + "px;");
            $(linkedCorrespondenceDocument.refs['modalAgendaCorrespondenceDocument']).off("hidden.bs.modal");
            $(linkedCorrespondenceDocument.refs['modalAgendaCorrespondenceDocument']).off("shown.bs.modal");
            $(linkedCorrespondenceDocument.refs['modalAgendaCorrespondenceDocument']).on('shown.bs.modal', function () {
                gPaddingTop += 30;
                gWidth -= 5;
                $("ul[ref='tabDocumentDetails']").scrollingTabs('refresh');
            });
            $(linkedCorrespondenceDocument.refs['modalAgendaCorrespondenceDocument']).on('hidden.bs.modal', function () {
                gPaddingTop -= 30;
                gWidth += 5;
                $(linkedCorrespondenceDocument.refs[linkedCorrespondenceDocument.model.ComponentId]).remove();
                swal.close();
                //if ($(".modal-window").children().length > 0 || $(".modal-documents").children().length > 0) {
                //    $('body').addClass('modal-open');
                //}
            });
            $(linkedCorrespondenceDocument.refs['modalAgendaCorrespondenceDocument']).modal("show");
        }
    }, function () { Common.showScreenErrorMsg(); }, true);
}
class AgendaTopicsList extends Intalio.Model {
    constructor() {
        super();
        this.documentId = null;
        this.statuses = [];
        this.delegationId = null;
        this.readOnly = null;
        this.MeetingResolution = null;
    }
}
class AgendaTopicsListView extends Intalio.View {
    constructor(element, model) {
        super(element, "AgendaTopicsList", model);
    }
    render() {
        var self = this;
        $.fn.select2.defaults.set("theme", "bootstrap");
        var resolution = [];
        var initialLoad = true;

        $(self.refs['btnNewSepResolution']).hide(); 
        $(self.refs['btnNewMinutes']).hide(); 
        $(self.refs['btnDeleteDocument']).hide(); 
        if (this.model.actionName != undefined) {
            var actionArray = this.model.actionName.split("_");

            if ((actionArray.includes("Agenda.NewSepResolution"))) {
                $(self.refs['btnNewSepResolution']).show();
            }
            if ((actionArray.includes("Agenda.NewMinutes"))) {
                $(self.refs['btnNewMinutes']).show();
            }
            if ((actionArray.includes("Agenda.DeleteDocument"))) {
                $(self.refs['btnDeleteDocument']).show();
            }
        }
        

        Common.gridCommon();
        let table = $(self.refs['grdAgendaTopicsList']).on('draw.dt',
            function () {
                $(self.refs['grdAgendaTopicsList']).find('tbody tr td').each(function (index) {
                    if ($(this).hasClass('sortableDateInDatatable')) {
                        if ($(this)[0].lastElementChild) {
                            this.setAttribute('title', $(this)[0].lastElementChild.textContent);
                        }
                    } else {
                        this.setAttribute('title', $(this).text());
                    }
                });
            }).DataTable({
                processing: false,
                ordering: true,
                serverSide: false,
                pageLength: 10,
                "ajax": {
                    "url": "/Document/GetDocumentCustomInfo",
                    "type": "GET",
                    "datatype": "json",
                    data: function (d) {
                        d.documentId = self.model.documentId;
                        d.delegationId = self.model.delegationId;
                        return d;
                    }
                },
                "order": [],
                "columns": [
                    {
                        title: '<input id="chkAllRes" type="checkbox" onClick="this.checked=!this.checked;" />', width: '16px', "orderable": false,
                        "render": function (data, type, row) {
                            return "<input type='checkbox' onclick='event.stopPropagation();' data-id=" + row.id + " />";
                        }
                    },
                    { title: "Id", data: "id", visible: false, "orderable": false },
                    { title: Resources.Subject, data: "subject", "orderable": true, orderSequence: ["asc", "desc"], width: "120px" },
                    { title: Resources.ReferenceNumber, data: "referenceNumber", "orderable": true, orderSequence: ["asc", "desc"], width: "120px" },
                    {
                        title: Resources.Resolution, data: "resolution", "orderable": true, orderSequence: ["asc", "desc"], width: "120px",
                        "render": function (data, type, row) {
                            if (type === 'display' && data != null) {
                                var originalText = data?.replace(/(<([^>]+)>)/ig, "");
                                // Truncate text if it exceeds 100 characters
                                var truncatedText = originalText?.length > 50 ? originalText.substr(0, 50) + '...' : originalText;
                                // Add tooltip with original text
                                return '<span title="' + originalText + '">' + truncatedText + '</span>';
                            }
                            return data;
                        }
                    },
                    {
                        "autoWidth": false,
                        "bAutoWidth": false,
                        'orderable': false,
                        'sortable': false,
                        width: "16px",
                        'render': function (data, type, row) {
                            resolution[row.id] = row?.resolution?.replace(/(\r\n|\n|\r)/gm, "");
                            var html = "";
                            let btn = document.createElement("button");
                            btn.setAttribute("class", "btn btn-xs btn-warning mr-sm view");
                            btn.setAttribute("title", Resources.View);
                            btn.setAttribute("type", "button");
                            btn.setAttribute("clickAttr", "openLinkedDocument(" + row.id + "," + self.model.delegationId + "," + self.model.documentId + ")");
                            btn.innerHTML = "<i class='fa fa-eye fa-white'/>";
                            html += btn.outerHTML;
                            if (self.model.readOnly !== true) {
                                let btnEdit = document.createElement("button");
                                btnEdit.setAttribute("class", "btn btn-xs btn-primary edit");
                                btnEdit.setAttribute("title", Resources.Edit);
                                btnEdit.setAttribute("docId", row.id);
                                btnEdit.setAttribute("clickAttr", "showHideAgendaResolution(" + row.id + "," + self.model.documentId + "," + self.model.delegationId + ")");
                                btnEdit.innerHTML = "<i class='fa fa-edit fa-white'/>";
                                html += btnEdit.outerHTML;

                                //let btn = document.createElement("button");
                                //btn.setAttribute("class", "btn btn-xs btn-danger edit");
                                //btn.setAttribute("title", Resources.Delete);
                                //btn.setAttribute("clickAttr", "deleteLink(" + row.id + "," + self.model.documentId + ", " + row.resolution + ")");
                                //btn.innerHTML = "<i class='fa fa-trash'/>";
                                //html += btn.outerHTML;
                            }


                            return "<div style='display: inline-flex;'>" + html + "</div>";
                        }
                    }
                ],
                "fnInitComplete": function (settings, json) {
                    $('[data-toggle="tooltip"]').tooltip();

                    if (initialLoad) {
                        initialLoad = false;
                        EventReceiver.LinkedCorrespondencesAfterRender(self);
                    }
                },
                dom: 'trpi',
                buttons: [] // button
            });
        $(self.refs['grdAgendaTopicsList']).on('click', 'tr', function () {
            var input = this.getElementsByTagName('input')[0];
            if (typeof input !== "undefined")
                input.checked = input.checked ? false : true;
        });
        $(self.refs['grdAgendaTopicsList']).on('dblclick', 'tr', function (e) {
            var onclick = $(this).find(".view").attr("clickattr");
            eval(onclick);
        });

        table.$('td').tooltip({
            "delay": 0,
            "track": true,
            "fade": 250
        });
        $('#chkAllRes').change(function () {
            $('tbody tr td input[type="checkbox"]').prop('checked', $(this).prop('checked'));
        });
        $(self.refs['grdAgendaTopicsList']).on('click', ".edit", function () {
            var docId = $(this).attr("docId");
            var onclick = $(this).attr("clickattr").slice(0, -1) + ",resolution[docId])";
            eval(onclick);
        });
        $(self.refs['grdAgendaTopicsList']).on('click', ".view", function () {
            var onclick = $(this).attr("clickattr");
            eval(onclick);
        });
        if (!self.model.readOnly) {
            $(self.refs['btnDeleteDocument']).click(function () {
                deleteDocuments(self);
            });
        }

        if (self.model.MeetingResolution) {

            $(self.refs['btnNewMinutes']).click(function () {

                var id = window.MeetingMinutesId;
                var params = { categoryId: id };
                var templateId = null;
                Common.ajaxGet('/Template/ListTemplatesByCategoryId', params, function (data) {

                    if (data.length > 0 && data[0].children.length > 0) {
                        templateId = data[0].children[0].id
                    }
                    createMeetingMinutesDocument(id, templateId, self);
                });
            });


            $(self.refs['btnNewSepResolution']).click(function () {

                var id = window.SepResolutionId;
                var params = { categoryId: id };
                var templateId = null;
                Common.ajaxGet('/Template/ListTemplatesByCategoryId', params, function (data) {

                    if (data.length > 0 && data[0].children.length > 0) {
                        templateId = data[0].children[0].id
                    }
                    createSepResolutionDocument(id, templateId, self);
                });
            });
        }
    }
}
export default { AgendaTopicsList, AgendaTopicsListView };
