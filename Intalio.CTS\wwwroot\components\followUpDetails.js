﻿import Intalio from './common.js'
import Document from './document.js'
import Assignee from './assigneeList.js'
import ActivityLogTimeline from './activityLogTimeline.js'
import LinkedCorrespondence from './linkedCorrespondenceList.js'
import LinkedCorrespondenceDocument from './linkedCorrespondenceDocument.js'
import DocumentDetails from './documentDetails.js'
import TaskEmailReminder from './taskEmailReminder.js'
import FromToDate from './fromToDate.js'
import followUpPostpone from './followUpPostponeIndex.js'
import { IdentityService } from './lookup.js'
import FollowUpOriginalDocument from "./FollowUpOriginalDocument.js"

var gLocked = false;
var originalDocumentModal = null;
function postponeFollowUp(data, self)
{
    var btn = $('#btnSubmitFollowUpPostpone');
    var btnClose = $('#btnCloseFollowUpPostpone');
    var btnCloseX = $('#postponeModalClose');
    

    var followUpIdsData = []
    followUpIdsData.push({ 'followUpId': self.model.followUpId, 'subject': self.model.followUpSubject })

    var prams = {
        postponeType: data.postponeType,
        toDate: data.toDate,
        months: data.months != "" ? parseInt(data.months):0 ,
        weeks: data.weeks != "" ? parseInt(data.weeks) : 0,
        days: data.days != "" ? parseInt(data.days) : 0,
        followUpActionModels: followUpIdsData
    }

    Common.showConfirmMsg(Resources.PostponeConfirmation, function () {

        Common.ajaxPost('/FollowUp/PostponeFollowUp', { followUpPostponeModels: prams },
            function (result) {
                
                if (result != null && result.length > 0) {
                    let msg = "";
                    if (result.find(f => f).updated == false) {
                        for (var i = 0; i < result.length; i++) {
                            if (msg == "") {
                                msg = '○ ' + result[i].message + " : " + result[i].subject;
                            }
                            else {
                                msg = '\n ○ ' + result[i].message + " : " + result[i].subject;
                            }
                        }
                        Common.alertMsg(msg);
                    }
                    else {
                        Common.showScreenSuccessMsg();
                        btn.button('reset');
                        btnClose.removeAttr('disabled');
                        btnCloseX.removeAttr('disabled');
                        $('#modalfollowUpPostponeIndex').modal('hide');
                        tryCloseModal(self.model.parentLinkedDocumentId);
                        TreeNode.refreshTreeNodeCounts(TreeNode.FollowUp);
                        TreeNode.refreshTreeNodeCounts(TreeNode.StructureSent);
                        TreeNode.refreshTreeNodeCounts(TreeNode.Completed);
                        TreeNode.refreshTreeNodeCounts(TreeNode.Closed);
                        TreeNode.refreshTreeNodeCounts(TreeNode.MyRequests);
                        TreeNode.refreshTreeNodeCounts(TreeNode.Sent);
                        if ($("#grdFollowUpItems").val() != undefined) {
                                GridCommon.RefreshCurrentPage("grdFollowUpItems", true);
                        } else {
                            //    $(".withBorders-o").addClass("waitingBackground");
                            //    $("#sentDocumentDetailsContainer").empty();
                            //    $($("input[data-id='" + self.model.id + "']").parents("li")[0]).fadeOut().remove();
                        }
                    }
                } else {
                    setTimeout(function () {
                        btn.button('reset');
                        btnClose.removeAttr('disabled');
                        btnCloseX.removeAttr('disabled');
                        Common.alertMsg(Resources.CantPostponeFollowUp);// can't Postpone follow up
                    }, 300);
                }

                btn.button('reset');
                btnClose.removeAttr('disabled');
                btnCloseX.removeAttr('disabled');
            }, function () {
                Common.showScreenErrorMsg();
        }, false);
    }, function () {
        btn.button('reset');
        btnClose.removeAttr('disabled');
        btnCloseX.removeAttr('disabled');
    });
}
function loadFollowUpMetadata(self) {
    Common.ajaxGet('/Document/Get', { id: self.model.followUpDocumentId }, function (data) {        
        openDocument(data, false, self.model.followUpDocumentId, undefined, self.model.followUpId,self);
    }, null, true);
}
function setFollowDetails(self) {
    var receivingEntity ='';
    if (self.model.followUpReceivingEntity.length>0) {
        let receiversNames = self.model.followUpReceivingEntity.map(item => item.text);
         receivingEntity = receiversNames.length > 1 ? receiversNames.join(" | ") : receiversNames[0] || "";
    }
    var sendingEntity = '';
    if (self.model.followUpSendingEntity != null && self.model.followUpSendingEntity.text) {
        sendingEntity = self.model.followUpSendingEntity.text;
    }

    $(self.refs['followUpSendingEntity'])[0].textContent = sendingEntity; 
    $(self.refs['followUpReceivingEntity'])[0].textContent = receivingEntity; 
    $(self.refs['followUpPriority'])[0].textContent = self.model.followUpPriority; 
    $(self.refs['followUpSubject'])[0].textContent = self.model.followUpSubject; 
    $(self.refs['followUpIsPrivate'])[0].textContent = self.model.IsPrivate == true ? Resources.Yes : Resources.No; 
    
}
function openDocument(data, readOnly, documentId, actionName, followUpId, self) {
    gLocked = false;
    var wrapper = $(self.refs[self.model.ComponentId+'_followUpMetaData'])
    var model = new Document.Document();
    model.id = documentId;
    model.followUpId = followUpId;
    model.dueDate = data.dueDate;
    model.createdByUserId = data.createdByUserId;
    model.createdByUser = data.createdByUser;
    model.categoryId = data.categoryId;
    model.categoryName = data.categoryName;
    model.referenceNumber = data.referenceNumber;
    model.subject = data.subject;
    model.basicAttributes = data.basicAttributes !== null && data.basicAttributes !== "" ? JSON.parse(data.basicAttributes) : [];
    model.customAttributes = data.customAttributes !== null && data.customAttributes !== "" ? JSON.parse(data.customAttributes) : null;
    model.customAttributesTranslation = data.customAttributesTranslation !== null && data.customAttributesTranslation !== "" ? JSON.parse(data.customAttributesTranslation) : null;
    model.formData = data.formData !== null && data.formData !== "" ? JSON.parse(data.formData) : [];
    model.formData.instructions = data.instruction;
    model.receivers = data.receivers;
    model.sendingEntityId = data.sendingEntityId;
    model.dueDate = data.dueDate;
    model.priorityId = data.priorityId;
    model.privacyId = data.privacyId;
    model.carbonCopy = data.carbonCopy;
    model.importanceId = data.importanceId;
    model.classificationId = data.classificationId;
    model.sendingEntity = data.sendingEntity;
    model.receivingEntities = data.receivingEntities;
    model.carbonCopies = data.carbonCopies;
    model.classification = data.classification;
    model.documentType = data.documentType;
    model.readonly = readOnly && data.enableEdit ? !data.enableEdit : readOnly;
    model.delegationId = data.delegationId;
    model.userStructures = new IdentityService().getUserStructures(window.language);
    model.privacies = new CoreComponents.Lookup.Privacies().get(window.language);
    model.priorities = new CoreComponents.Lookup.Priorities().get(window.language);
    model.importances = new CoreComponents.Lookup.Importances().get(window.language);
    model.createdByStructureId = data.createdByStructureId;
    model.body = data.body;
    model.externalReferenceNumber = data.externalReferenceNumber;
    model.keyword = data.keyword;
    model.enableEdit = data.enableEdit;
    model.transferId = null;
    model.senderPerson = data.senderPerson;
    model.receiverPerson = data.receiverPerson;
    model.isExternalReceiver = data.isExternalReceiver;
    model.isExternalSender = data.isExternalSender;
    model.fromVip = false;
    model.isFollowUp = true;
    model.followUpUserRole = self.model.followUpUserRole;
    model.isTaskCreator = (data.createdByUserId == $("#hdUserId").val());
    model.status = data.status;
    model.isCompleted = new CoreComponents.Lookup.Statuses().findById(data.status, 'en').text.toLowerCase() === "completed";
    model.byTemplate = data.byTemplate;

    if (actionName != undefined)
        model.actionName = actionName;

    if ((model.isFollowUp && !model.isCompleted) || (window.location.hash.split("/")[0] !== '#manageFollowUp' && !model.isCompleted)) {
        model.enableEdit = true;
        model.readonly = false;
    }

    var documentView = new Document.DocumentView(wrapper, model);
    setTimeout(function () {
        $('#txtCustomAttributeSubject').focus();
    }, 500);
    let url = readOnly ? '/Document/Edit' : '/Document/Save';
    documentView.render({
        url: url,
        params: {
            'CategoryId': data.categoryId,
            'CategoryName': data.categoryName,
            '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
        },
        callback: function (data) {
            let model = JSON.parse(data);
            var priorities = new CoreComponents.Lookup.Priorities().get(window.language);
            var priority = priorities.find(e => e.id == model.PriorityId).text;

            $(self.refs['followUpPriority'])[0].textContent = priority;
            $(self.refs['followUpSubject'])[0].textContent = model.Subject;
            $(self.refs['followUpIsPrivate'])[0].textContent = model.IsPrivate == true ? "Yes" : "NO";

            if ($("#grdFollowUpItems").val() != undefined) {
                GridCommon.RefreshCurrentPage("grdFollowUpItems", true);
            }
            var tabs = $("div[ref='" + self.model.parentComponentId +"'] ul[ref='tabDocumentDetails'] li a")
            var refreshtab = $.grep(tabs, function (element) {
                return $(element).data("function").indexOf('CTSCoreComponents.CustomTabs.openFollowUpUsersTab') >= 0;
            })[0];

            $(refreshtab).data("loaded", false)
            $(refreshtab).data("customloaded", false)

            $(self.refs['btnEditFollowUp']).trigger("click");
        }
    }, {
        url: '/Document/Send',
        params: {
            '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
        },
        callback: function (response) {
        }
    });

    $(document).off('click', '.btn-back');
    $(document).on('click', '.btn-back', function () {
        $(self.refs[self.model.ComponentId +'_followUpMetaData']).empty();
        $(self.refs['followUpMetaDataContainer']).addClass('hidden').attr('hidden', "hidden");
        $(self.refs['followUpDetailsContainer']).removeClass('hidden').removeAttr("hidden");
        //documentView.remove();
    });
}
function openTransferViewMode(id, privacyId) {
    gLocked = false;

    var userPrivacyLevel = Number.parseInt($("#hdStructurePrivacyLevel").val());

    if (privacyId > userPrivacyLevel) {
        return Common.alertMsg(Resources.NoPermission);
    }
        var params = { id: id };
    //Common.ajaxGet('/Document/GetDocumentBasicInfoByTransferId', params, function (response) {
    Common.ajaxGet('/Transfer/GetTransferDetailsById', params, function (response) {
            var wrapper = $(".modal-documents");
            var linkedCorrespondenceModel = new LinkedCorrespondenceDocument.LinkedCorrespondenceDocument();
            linkedCorrespondenceModel.reference = response.referenceNumber;
            linkedCorrespondenceModel.subject = response.subject;
            linkedCorrespondenceModel.documentId = response.documentId;
            var linkedCorrespondenceDocument = new LinkedCorrespondenceDocument.LinkedCorrespondenceDocumentView(wrapper, linkedCorrespondenceModel);
            linkedCorrespondenceDocument.render();

            var model = new DocumentDetails.DocumentDetails();
            model.categoryId = response.categoryId;
            model.delegationId = null;
            model.id = id;
            model.documentId = response.documentId;
            model.referenceNumber = response.referenceNumber;
            model.categoryName = response.categoryName;
            model.statusId = response.status;
            model.createdByUser = response.createdByUser;
            model.readonly = true;
            model.attachmentId = response.attachmentId;
            model.statuses = new CoreComponents.Lookup.Statuses().get(window.language);
            model.showMyTransfer = true;
            model.fromInbox = true;
            model.parentComponentId = linkedCorrespondenceDocument.model.ComponentId
            model.isModal = true;
            model.showBackButton = false;
            var securityMatrix = JSON.parse(new CoreComponents.Lookup.SecurityMatrix().get(window.language));// JSON.parse(sessionStorage.getItem("SecurityMatrix"));

            var tabs = securityMatrix.SecurityNodes[TreeNodes.Inbox].SecurityCategories[response.categoryId].Tabs;
            model.tabs = $.grep(tabs, function (element, index) {
                return !element.Name.includes("myTransfer") && !element.Name.includes("documentMetadata") &&
                    !element.Name.includes("transferHistory") && !element.Name.includes("visualTracking") &&
                    !element.Name.includes("activityLog") && !element.Name.includes("attachments") && !element.Name.includes("notes") &&
                    !element.Name.includes("linkedDocument") && !element.Name.includes("nonArchivedAttachments");
            });
            model.tabsWithStatic = tabs;
            model.tabsActions = securityMatrix.SecurityNodes[TreeNodes.Inbox].SecurityCategories[response.categoryId].SecurityTabs;
            model.nodeId = TreeNodes.Inbox;
            model.fromFollowUp = true;
            wrapper = $(linkedCorrespondenceDocument.refs['linkDocumentDiv']);

            var view = new DocumentDetails.DocumentDetailsView(wrapper, model);
            view.render();

            $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).off("hidden.bs.modal");
            $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).off("shown.bs.modal");
            $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).on('shown.bs.modal', function () {
                $("ul[ref='tabDocumentDetails']").scrollingTabs('refresh');
            });
            $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).on('hidden.bs.modal', function () {
                if ($(this).data("remove") != true)
                    return;
                $(linkedCorrespondenceDocument.refs[linkedCorrespondenceDocument.model.ComponentId]).remove();
                swal.close();
                if ($(".modal-documents").children().length > 0 || $(".modal-window").children().length > 0) {
                    $('body').addClass('modal-open');
                }
            });
            $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).modal("show");
        }, function () { Common.showScreenErrorMsg(); }, true);
    

}
function loadAssigneesData(_model) {
    let param = {
        'documentId': _model.documentId
    };
    Common.ajaxGet("Assignee/GetAssigneePermission", param, function (data) {
        var wrapper = $("#followUpAssigneesdata");
        var model = new Assignee.Assignee();
        model.documentId = _model.documentId;
        model.assigneeOperationPermission = data.assigneeOperationPermission;
        var assigneeView = new Assignee.AssigneeView(wrapper, model);
        assigneeView.render();

        return data;
    }, function () { Common.showScreenErrorMsg(); }, true);
}
function loadLinkedCorrespondencesData(_model) {
    _model.isFollowUp = true;
    _model.readOnly = false;

    var wrapper = $("#followUpLinkedCorrespondencesData");
    var documentView = new LinkedCorrespondence.LinkedCorrespondenceView(wrapper, _model);
    documentView.render();
}
function PostponementTransfer(model, refs, followUpDocumentId) {
    var wrapper = $(".modal-window");
    var modelIndex = new FromToDate.FromToDate();
    modelIndex.documentId = model.followUpDocumentId;
    modelIndex.transferId = model.transferId;
    modelIndex.fromFollowUpDate = model.dueDate;
    modelIndex.documentDueDate = model.documentDueDate;
    modelIndex.postponeType = PostponeType.Transfer;
    var callback = function (data) {
        GridCommon.Refresh(model.componentId + '_grdFollowUpDocumentTransfersItems');
         //loadFollowUpActvityLogs(refs, followUpDocumentId);
    };
    modelIndex.callback = callback;

    var fromToDateIndex = new FromToDate.FromToDateView(wrapper, modelIndex);
    fromToDateIndex.render();

    $(fromToDateIndex.refs['modalFromToDateTitle']).html(Resources.NewPostponedTransferDueDate);

    $(fromToDateIndex.refs['modalFromToDate']).modal("show");
    $(fromToDateIndex.refs['modalFromToDate']).off("hidden.bs.modal");
    $(fromToDateIndex.refs['modalFromToDate']).off("shown.bs.modal");
    $(fromToDateIndex.refs['modalFromToDate']).on('hidden.bs.modal', function () {
        $(fromToDateIndex.refs['modalFromToDate']).parent().remove();
        swal.close();
        $('body').addClass('modal-open');

    });
}
function SendReminderToTransferUser(transferId, documentId, user) {
    
    let modalWrapper = $(".modal-window");
    let modelIndex = new TaskEmailReminder.TaskEmailReminder();
    modelIndex.documentId = documentId;
    modelIndex.transferId = transferId;
    modelIndex.toUser = user;
    modelIndex.subject = '';
    modelIndex.withOutTemplate = true;

    Common.ajaxGet("/Attachment/ListAttachments?documentId=" + documentId + "&transferId=" + transferId + "&delegationId=" + null, null, function (data) {
        $("#modalTaskEmailReminder").remove();

        modelIndex.attachments = data.data;
        let taskEmailReminderView = new TaskEmailReminder.TaskEmailReminderView(modalWrapper, modelIndex);
        taskEmailReminderView.render();
        $('#modalTaskEmailReminder').modal("show");
        $("#modalTaskEmailReminder").off("hidden.bs.modal");
        $("#modalTaskEmailReminder").off("shown.bs.modal");
        $("#modalTaskEmailReminder").on('hidden.bs.modal', function () {
            $("#modalTaskEmailReminder").remove();
            swal.close();
            $('body').addClass('modal-open');
        });
        //$('#modalTaskEmailReminder').on('shown.bs.modal', function () {
        //    CKEDITOR.instances.txtAreaEmailBody.focus();
        //});
    }, function () { Common.showScreenErrorMsg(); }, null, null, false);
}
function loadFollowUpDocumentTransfers(self) {
    Common.gridCommon();

    let table = $(self.refs['grdFollowUpDocumentTransfersItems']).on('draw.dt',
        function () {
              $(self.refs['grdFollowUpDocumentTransfersItems']).find('tbody tr td').each(function () {
                this.setAttribute('title', $(this).text());
            });
        }).DataTable({
            processing: true,
            ordering: true,
            serverSide: true,
            pageLength: 10,
            "ajax": {
                "url": "/FollowUp/ListFollowUpDocumentTransfers",
                "type": "POST",
                "datatype": "json",
                data: function (d) {

                    d.DocumentId = self.model.originalDocumentId;
                    return d;

                }
            },
            "order": [],
            "columns": [
                { title: "Id", data: "id", visible: false, "orderable": false },
                { title: Resources.ReferenceNumber, data: "referenceNumber", render: $.fn.dataTable.render.text(), "orderable": false, width: "100px" },
                { title: Resources.Category, data: "categoryName", render: $.fn.dataTable.render.text(), "orderable": false, width: "100px" },
                { title: Resources.ToUser, data: "toUser", render: $.fn.dataTable.render.text(), "orderable": false, width: "100px" },
                { title: Resources.ToStructure, data: "toStructure", render: $.fn.dataTable.render.text(), "orderable": false, width: "100px" },
                {
                    title: Resources.DueDate, data: "dueDate", render: $.fn.dataTable.render.text(), "orderable": false, width: "100px",
                    render: function (data, type, full, meta) {
                        return DateConverter.toHijriFormated(full.dueDate, null, window.CalendarType);
                    }
                },
                {
                    title: Resources.Status, data: "statusId", "orderable": false, width: "100px", render: $.fn.dataTable.render.text(),
                    render: function (data) {
                        var statuses = self.model.statuses;
                        for (var i = 0; i < statuses.length; i++) {
                            if (statuses[i].id === data) {
                                return "<div class='label' style='background-color:" + (statuses[i].color !== null ? statuses[i].color : "#27c24c") + "'>" + statuses[i].text + "</div>";
                            }
                        }
                        return "";
                    } },
                {
                    title: Resources.OpenTransfer,
                    "className": "text-right",
                    "autoWidth": false,
                    "bAutoWidth": false,
                    width: "16px",
                    'orderable': false,
                    'sortable': false,
                    'render': function (data, type, full, meta) {
                        var html = "";
                        let btnView = document.createElement("button");
                            btnView.setAttribute("class", "btn btn-xs btn-warning mr-sm view");
                        btnView.setAttribute("title", Resources.View);
                        btnView.setAttribute("clickattr", "openTransferViewMode(" + full.id + "," + full.document.privacyId + ")");
                        btnView.innerHTML = "<i class='fa fa-eye fa-white'/>";
                        html += btnView.outerHTML;
                        return "<div style='align-items: center; display: flex; justify-content: center;'>" + html + "</div>";
                    }
                },
                {
                    "className": "text-right",
                    "autoWidth": false,
                    "bAutoWidth": false,
                    width: "16px",
                    'orderable': false,
                    'sortable': false,
                    'render': function (data, type, full, meta) {
                        var html = "";
                        var postponeDiv = "<div class='mr-sm ' title='" + Resources.Postponement + "'>";
                        if (!self.model.readonly) {
                            var postponeModel = JSON.stringify({ transferId: full.id, dueDate: full.dueDate, documentDueDate: full.documentDueDate, componentId: self.model.ComponentId  })
                            var timeLineDiv = self.refs['followUpTimeLine']
                            var followUpDocumentId = self.model.followUpDocumentId;
                            var postpone = "<i style='cursor: pointer;' clickattr='PostponementTransfer(" + postponeModel + ")' class='text-info Postponement'>" + Resources.Postponement + "</i>";
                            postponeDiv += postpone;
                        }
                        postponeDiv += "</div>";

                        html += postponeDiv;
                        return "<div id='divPostponement_" + full.id + "' style='display: inline-flex;'>" + html + "</div>";
                    }
                },
                {
                    "className": "text-right",
                    "autoWidth": false,
                    "bAutoWidth": false,
                    width: "16px",
                    'orderable': false,
                    'sortable': false,
                    'render': function (data, type, full, meta) {
                        var html = "";
                        var reminderDiv = "<div class='mr-sm ' title='" + Resources.SendReminder + "'>";
                        if (full.toUserId != null && !self.model.readonly) {
                            var user = JSON.stringify({ id: full.toUserId, name: full.toUser, email: full.toUserEmail })
                            var reminder = "<i style='cursor: pointer;' clickattr='SendReminderToTransferUser(" + full.id + "," + full.followUpDocumentId + "," + user +")' class='text-info SendReminder'>" + Resources.SendReminder + "</i>";
                            reminderDiv += reminder;
                        }
                        reminderDiv += "</div>";
                        html += reminderDiv;
                        return "<div id='divReminder_" + full.id + "' style='display: inline-flex;'>" + html + "</div>";
                    }
                }
            ],
            "fnInitComplete": function (settings, json) {
                $('[data-toggle="tooltip"]').tooltip();
            },
            dom: '<"html5buttons "B>ltr<"pull-left"p><"pull-right"i>',
            buttons: []
            
        });

    table.$('td').tooltip({
        "delay": 0,
        "track": true,
        "fade": 250
    });
}
function loadFollowUpActvityLogs(refs, followUpDocumentId) {


    var wrapper = $(refs['followUpTimeLine']);
    wrapper.empty();
    if ($.fn.DataTable.isDataTable($(refs['grdFollowUpActivtyLogsItems']))) {
        $(refs['grdFollowUpActivtyLogsItems']).DataTable().destroy();
    }
    var activityModel = new ActivityLogTimeline.ActivityLogTimeline();
    activityModel.statuses = new CoreComponents.Lookup.Statuses().get(window.language);
    activityModel.documentId = followUpDocumentId;
    activityModel.timelineOnly = true;
    activityModel.delegationId = null;
    var documentView = new ActivityLogTimeline.ActivityLogTimelineView(wrapper, activityModel);
    documentView.render();

    Common.gridCommon();

    let table = $(refs['grdFollowUpActivtyLogsItems']).on('draw.dt',
        function () {
            $(refs['grdFollowUpActivtyLogsItems']).find('tbody tr td').each(function () {
                this.setAttribute('title', $(this).text());
            });
        }).DataTable({
            processing: true,
            ordering: true,
            serverSide: true,
            pageLength: 10,
            "ajax": {
                "url": "/ActivityLog/LisActivityLogGridtByDocumentId",
                "type": "POST",
                "datatype": "json",
                data: function (d) {

                    d.DelegationId = null;
                    d.DocumentId = followUpDocumentId;
                    d.ActivityLogActionId = "0";
                    return d;

                }
            },
            "order": [],
            "columns": [
                { title: "Id", data: "id", visible: false, "orderable": false },

                { title: Resources.Action, data: "action", "orderable": true, orderSequence: ["asc", "desc"], width: "150px" },
                { title: Resources.User, data: "user", render: $.fn.dataTable.render.text(), "orderable": false },

                {
                    title: Resources.CreatedDate, data: "createdDate", "orderable": true, orderSequence: ["asc", "desc"], width: "100px",
                    render: function (data, type, full, meta) {
                        return DateConverter.toHijriFormated(full.createdDate, ConversionDateFormat.DateTimeFormat24_Slash, window.CalendarType);
                    }
                },
                { title: Resources.Note, data: "note", render: $.fn.dataTable.render.text(), "orderable": false },

            ],
            "fnInitComplete": function (settings, json) {
                $('[data-toggle="tooltip"]').tooltip();
            },
            dom: '<"html5buttons "B>ltr<"pull-left"p><"pull-right"i>',
            buttons: []
            //buttons: [{
            //    extend: 'excelHtml5',
            //    className: 'btn-excel',
            //    action: function (e, dt, node, config) {
            //        var tableData = dt.rows({ search: 'applied' }).data().toArray();
            //        var referenceNumber = tableData.length > 0 ? tableData[0].referenceNumber : 'default_filename';
            //        var subject = tableData.length > 0 ? tableData[0].subject : 'default_filename';
            //        fetch('/Export/ExportActivityLogGrid', {
            //            method: 'POST',
            //            headers: {
            //                'Content-Type': 'application/json'
            //            },
            //            body: JSON.stringify({ format: 'excel', activityLogActionId: $("#cmbActivityLogGridAction").val() !== null && typeof $("#cmbActivityLogGridAction").val() !== "undefined" ? $("#cmbActivityLogGridAction").val() : "0", documentId: gSelfIndex.model.documentId, delegationId: gSelfIndex.model.delegationId })
            //        })
            //            .then(response => response.blob())
            //            .then(blob => {
            //                const url = window.URL.createObjectURL(blob);
            //                const a = document.createElement('a');
            //                a.href = url;
            //                a.download = referenceNumber ? referenceNumber + '_' + new Date().toISOString() + '.xlsx' : subject + '_' + new Date().toISOString() + '.xlsx';
            //                a.click();
            //                window.URL.revokeObjectURL(url);
            //            })
            //            .catch(error => console.error('Error exporting to Excel:', error));
            //    }
            //}, {
            //    extend: 'pdfHtml5',
            //    className: 'btn-pdf',
            //    action: function (e, dt, node, config) {
            //        var tableData = dt.rows({ search: 'applied' }).data().toArray();
            //        var referenceNumber = tableData.length > 0 ? tableData[0].referenceNumber : 'default_filename';
            //        var subject = tableData.length > 0 ? tableData[0].subject : 'default_filename';
            //        fetch('/Export/ExportActivityLogGrid', {
            //            method: 'POST',
            //            headers: {
            //                'Content-Type': 'application/json'
            //            },
            //            body: JSON.stringify({ format: 'pdf', activityLogActionId: $("#cmbActivityLogGridAction").val() !== null && typeof $("#cmbActivityLogGridAction").val() !== "undefined" ? $("#cmbActivityLogGridAction").val() : "0", documentId: gSelfIndex.model.documentId, delegationId: gSelfIndex.model.delegationId })
            //        })
            //            .then(response => response.blob())
            //            .then(blob => {
            //                const url = window.URL.createObjectURL(blob);
            //                const a = document.createElement('a');
            //                a.href = url;
            //                a.download = referenceNumber ? referenceNumber + '_' + new Date().toISOString() + '.pdf' : subject + '_' + new Date().toISOString() + '.pdf';
            //                document.body.appendChild(a);
            //                a.click();
            //                window.URL.revokeObjectURL(url);
            //                document.body.removeChild(a);
            //            })
            //            .catch(error => console.error('Error exporting to pdf:', error));
            //    }
            //}]
        });

    table.$('td').tooltip({
        "delay": 0,
        "track": true,
        "fade": 250
    });
}

let gWidth = 100, gPaddingTop = 0;

class FollowUpDetailsModel extends Intalio.Model {
    constructor() {
        super();
        this.followUpId = null;
        this.followupStatusId = null;
        this.followUpStatus = null;
        this.followUpDocumentId = null;
        this.followUpDocument = null;
        this.originalDocumentId = null;
        this.originalDocument = null;
        this.createdByUserId = null;
        this.createdByUser = null;
        this.readonly = null;
        this.actions = [];
        this.showBackButton = true;
        this.parentComponentId = null;
        this.IsPrivate = null;
        this.followUpUserRole = null;
        this.followUpSendingEntity = null;
        this.followUpReceivingEntity = null;
        this.followUpPriority = null;
        this.followUpSubject = null;
        this.Language = null;
        this.actionName = null;
        this.tabId = '';


        var statuses = new CoreComponents.Lookup.Statuses().get(window.language);
        if (statuses.length > 0) {
            statuses = statuses.filter(function (obj) {
                return obj.id !== SystemStatus.Draft;
            });
        }
        this.statuses = statuses;
    }
}
class FollowUpDetailsView extends Intalio.View {
    constructor(element, model) {
        super(element, "followUpDetails", model);
    }
    render() {
        var self = this;
        var model = this.model;
        // make followUp details tab not reload
        var tabs = $("div[ref='" + self.model.parentComponentId + "'] ul[ref='tabDocumentDetails'] li a")
        var refreshtab = $.grep(tabs, function (element) {
            return $(element).data("id") == self.model.tabId;
        })[0];

        $(refreshtab).data("loaded", true)
        $(refreshtab).data("customloaded", true)

        var actionsPanel = "#" + self.model.parentLinkedDocumentId + "_ActionsPanel";
        var actionArray;
        if (this.model.actionName  ) {
            actionArray = this.model.actionName.split("_");

            if (self.model.followupStatusId != FollowUpStatuses.Completed && self.model.followupStatusId != FollowUpStatuses.Canceled && !self.model.readonly && self.model.followUpUserRole == FollowUpRoles.Owner) {


                if (actionArray.includes("FollowUp.CompleteFollowUp") && $('#' + self.model.parentComponentId +'_btnCompleteFollowUp').length<=0) {
                    $(actionsPanel).append('<button ref="btnCompleteFollowUp" id="' + self.model.parentComponentId +'_btnCompleteFollowUp" type="button" class="btn btn-vip-toolbar btnCompleteFollowUp" data-loading-text="<i class="fa fa-spinner fa-spin"><i class="fa fa - send mr - sm"></i>' + Resources.Complete + '</button>');
                }
                //if (actionArray.includes("FollowUp.RequestToComplete")) {
                //    $(actionsPanel).append('<button ref="btnRequestToComplete" id="' + self.model.parentComponentId + '_btnRequestToComplete" type="button" class="btn btn-vip-toolbar " data-loading-text="<i class="fa fa-spinner fa-spin"><i class="fa fa-send mr-sm"></i>' + Resources.RequestToComplete + '</button>');
                //}
                if (actionArray.includes("FollowUp.Postpone") && $('#' + self.model.parentComponentId + '_btnPostpone').length <= 0) {
                    $(actionsPanel).append('<button ref="btnPostpone" id="' + self.model.parentComponentId +'_btnPostpone" type="button" class="btn btn-vip-toolbar " data-loading-text="<i class="fa fa-spinner fa-spin"><i class="fa fa-send mr-sm"></i>' + Resources.PostponementFollowUp + '</button>');
                }
                //if (actionArray.includes("FollowUp.SendEmailReminder")) {
                //    $(actionsPanel).append('<button ref="btnSendEmailReminder" id="' + self.model.parentComponentId + '_btnSendEmailReminder" type="button" class="btn btn-vip-toolbar " data-loading-text="<i class="fa fa-spinner fa-spin"><i class="fa fa-send mr-sm"></i>' + Resources.SendEmailReminder + '</button>');
                //}
                //if (actionArray.includes("FollowUp.CancelTask")) {
                //    $(actionsPanel).append('<button ref="btnAttributeCancelTask" id="' + self.model.parentComponentId + '_btnAttributeCancelTask" type="button" class="btn btn-vip-toolbar " data-loading-text="<i class="fa fa-spinner fa-spin"><i class="fa fa-send mr-sm"></i>' + Resources.CancelFollowUp + '</button>');
                //}
                if (actionArray.includes("FollowUp.CancelFollowUp") && $('#' + self.model.parentComponentId + '_btnCancelFollowUp').length <= 0) {
                    $(actionsPanel).append('<button ref="btnCancelFollowUp" id="' + self.model.parentComponentId +'_btnCancelFollowUp" type="button" class="btn btn-vip-toolbar " data-loading-text="<i class="fa fa-spinner fa-spin"><i class="fa fa-send mr-sm"></i>' + Resources.CancelFollowUp + '</button>');
                }
            }

            if (actionArray.includes("FollowUp.OriginalDocument") && $('#' + self.model.parentComponentId + '_btnOpenOriginalDocument').length <= 0) {
                $(actionsPanel).append('<button ref="btnOpenOriginalDocument" id="' + self.model.parentComponentId +'_btnOpenOriginalDocument" type="button" class="btn btn-vip-toolbar " data-loading-text="<i class="fa fa-spinner fa-spin"><i class="fa fa-send mr-sm"></i>' + Resources.OriginalDocument + '</button>');
            }


        }
        setFollowDetails(self);
        loadFollowUpActvityLogs(self.refs, self.model.followUpDocumentId);
        loadFollowUpDocumentTransfers(self);
        $(self.refs['btnEditFollowUp']).on('click', function () {
            $(self.refs['followUpDetailsContainer']).addClass('hidden').attr('hidden', "hidden");
            $(self.refs['followUpMetaDataContainer']).removeClass('hidden').removeAttr("hidden");

            loadFollowUpMetadata(self);


        });

          $(self.refs['grdFollowUpDocumentTransfersItems']).find('tbody').on('click', ".view,.Postponement, .SendReminder ", function () {
            var onclick = $(this).attr("clickattr");
            eval(onclick);
        });

        $('#' + self.model.parentComponentId +'_btnCompleteFollowUp').click(function () {
            var paramData = []
            paramData.push({ 'followUpId': self.model.followUpId, 'subject': self.model.followUpSubject })
            Common.showConfirmMsg(Resources.CompleteConfirmation, function () {
                
                Common.ajaxPost('/FollowUp/CompleteFollowUp', { followUpCompleteModels: paramData },
                    function (result) {
                        
                        if (result != null && result.length > 0) {
                            let msg = "";
                            if (result.find(f => f).updated == false) {
                                for (var i = 0; i < result.length; i++) {
                                    if (msg == "") {
                                        msg = '○ ' + result[i].message + " : " + result[i].subject;
                                    }
                                    else {
                                        msg = '\n ○ ' + result[i].message + " : " + result[i].subject;
                                    }
                                }
                                Common.alertMsg(msg);
                            }
                            else {
                                Common.showScreenSuccessMsg();
                                tryCloseModal(self.model.parentLinkedDocumentId);
                                TreeNode.refreshTreeNodeCounts(TreeNode.FollowUp);
                                TreeNode.refreshTreeNodeCounts(TreeNode.StructureSent);
                                TreeNode.refreshTreeNodeCounts(TreeNode.Completed);
                                TreeNode.refreshTreeNodeCounts(TreeNode.Closed);
                                TreeNode.refreshTreeNodeCounts(TreeNode.MyRequests);
                                TreeNode.refreshTreeNodeCounts(TreeNode.Sent);
                                if ($("#grdFollowUpItems").val() != undefined) {
                                    GridCommon.RefreshCurrentPage("grdFollowUpItems", true);
                                } else {
                                    //    $(".withBorders-o").addClass("waitingBackground");
                                    //    $("#sentDocumentDetailsContainer").empty();
                                    //    $($("input[data-id='" + self.model.id + "']").parents("li")[0]).fadeOut().remove();
                                }
                            }
                        } else {
                            setTimeout(function () {
                                Common.alertMsg(Resources.CantCompleteFollowUp);// can't complete follow up
                            }, 300);
                        }
                    }, function () {
                        Common.showScreenErrorMsg();
                    }, false);
            });
        });
        $('#' + self.model.parentComponentId +'_btnRequestToComplete').click(function () {
        //    Common.showConfirmMsg(Resources.RequestToCompleteOneTaskConfirmation, function () {
        //        Common.ajaxPost('/Transfer/RequestToComplete',
        //            {
        //                'ids': self.model.transferId, 'delegationId': self.model.delegationId, '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
        //            },
        //            function (result) {
        //                if (result != null && result.length > 0) {
        //                    let msg = "";
        //                    if (result.find(f => f).updated == false) {
        //                        for (var i = 0; i < result.length; i++) {
        //                            if (msg == "") {
        //                                msg = '○  ' + result[i].message;
        //                            }
        //                            else {
        //                                msg = '\n ○ ' + result[i].message;
        //                            }
        //                        }
        //                        Common.alertMsg(msg);
        //                    }
        //                    else {
        //                        $(".close").trigger("click");
        //                        $("[ref=btnCloseTransfer]").trigger("click");
        //                        if ($('li .active').attr("data-id") == undefined) {
        //                            window.location.href = "/";
        //                        }
        //                        else {
        //                            window.location.reload();
        //                        }

        //                        Common.showScreenSuccessMsg();
        //                        $("#gridContainerDiv").show();
        //                        $("div [ref=documentDetailsContainerDiv]").remove();
        //                        TreeNode.refreshTreeNodeCounts(TreeNode.Inbox);
        //                        TreeNode.refreshTreeNodeCounts(TreeNode.StructureInbox); TreeNode.refreshTreeNodeCounts(TreeNode.StructureSent);
        //                        TreeNode.refreshTreeNodeCounts(TreeNode.Completed);
        //                        TreeNode.refreshTreeNodeCounts(TreeNode.Closed);
        //                        TreeNode.refreshTreeNodeCounts(TreeNode.MyRequests);
        //                        TreeNode.refreshTreeNodeCounts(TreeNode.Sent);
        //                        //window.location.reload();
        //                        if ($("#grdMyRequestsItems").val() != undefined || $("#grdSentItems").val() != undefined) {
        //                            GridCommon.RefreshCurrentPage(gTableName, true);
        //                        } else {
        //                            $(".withBorders-o").addClass("waitingBackground");
        //                            $("#sentDocumentDetailsContainer").empty();
        //                            $($("input[data-id='" + self.model.transferId + "']").parents("li")[0]).fadeOut().remove();
        //                        }
        //                    }
        //                } else {
        //                    Common.showScreenErrorMsg();
        //                }
        //            }, function () {
        //                Common.showScreenErrorMsg();
        //                Common.unmask(gridContainerName);
        //            }, false);
        //    });
        });
        $('#' + self.model.parentComponentId +'_btnPostpone').click(function () {
            
            let modalWrapper = $(".modal-window");
            modalWrapper.find("#modalfollowUpPostponeIndex").remove();
            var followUpPostponeModel = new followUpPostpone.FollowUpPostponeIndex();
            followUpPostponeModel.dueDate = self.model.dueDate;
            followUpPostponeModel.callback = function (data) {
               
                postponeFollowUp(data, self);
            };
            var followUpPostponeView = new followUpPostpone.FollowUpPostponeIndexView(modalWrapper, followUpPostponeModel);
            followUpPostponeView.render();

            $('#modalfollowUpPostponeIndex').modal('show');
            $("#modalfollowUpPostponeIndex").off("hidden.bs.modal");
            $("#modalfollowUpPostponeIndex").off("shown.bs.modal");
            $('#modalfollowUpPostponeIndex').on('shown.bs.modal', function () {
            });
            $('#modalfollowUpPostponeIndex').on('hidden.bs.modal', function () {
                $('#formFollowUpPostponeIndexPost').parsley().reset();
                $('#cmbFollowUpUserRole').val('');
                $('#modalfollowUpPostponeIndex').remove();
                if ($(".modal-documents").children().length > 0 || $(".modal-window").children().length > 0) {
                    $('body').addClass('modal-open');
                }
            });

        });
        $('#' + self.model.parentComponentId +'_btnAttributeCancelTask').click(function () {
            Common.showConfirmMsg(Resources.CancelTaskConfirmation, function () {
                let cancelTaskUrl = "/Document/CancelTask";
                if (!gLocked) {
                    gLocked = true;
                    let param = {
                        'documentId': self.model.id,
                        'overrideAccess': window.location.hash.split("/")[0] == '#manageFollowUp' ? true : false,
                    };
                    try {
                        Common.ajaxPost(cancelTaskUrl, param, function (response) {
                            gLocked = false;
                            if (response == true) {
                                $(".documentHeader span").before(" - " + response + " ");
                                $(self.refs['btnAttributeSave']).hide();
                                model.referenceNumber = response;
                                Common.showScreenSuccessMsg();
                                window.location.hash.split("/")[0] == '#document' ? window.location = '/' : window.location.reload();
                            } else {
                                Common.showScreenErrorMsg(Resources.CancelTaskNoPermission);
                            }
                        }, function () {
                            $(self.refs['btnAttributeSave']).attr("disabled", false);
                            $(self.refs['btnAttributeSave']).button('reset');
                            Common.showScreenErrorMsg();
                            gLocked = false;
                        });
                    } catch (e) {
                        $(self.refs['btnAttributeSave']).attr("disabled", false);
                        $(self.refs['btnAttributeSave']).button('reset');
                        gLocked = false;
                    }
                }
            });
        });
        $('#' + self.model.parentComponentId +'_btnOpenOriginalDocument').click(function () {
            if (originalDocumentModal == null) {
                const userPrivacyLevel = Number.parseInt($("#hdStructurePrivacyLevel").val());

                const originalDocument = self.model.originalDocument;
                if (originalDocument.privacyId > userPrivacyLevel) {
                    return Common.alertMsg(Resources.NoPermission);
                }
                var wrapper = $(".modal-documents");
                var followUpOriginalDocumentModel = new FollowUpOriginalDocument.FollowUpOriginalDocumentModel();
                followUpOriginalDocumentModel.subject = self.model.subject;
                followUpOriginalDocumentModel.reference = self.model.referenceNumber;
                followUpOriginalDocumentModel.documentId = originalDocument.id;
                var followUpOriginalDocumentView = new FollowUpOriginalDocument.FollowUpOriginalDocumentView(wrapper, followUpOriginalDocumentModel);
                followUpOriginalDocumentView.render();

                var documentDetailsModel = new DocumentDetails.DocumentDetails();
                documentDetailsModel.documentModel = originalDocument;
                documentDetailsModel.readonly = true;
                documentDetailsModel.delegationId = self.model.delegationId;
                documentDetailsModel.documentId = originalDocument.id;
                documentDetailsModel.referenceNumber = originalDocument.referenceNumber;
                documentDetailsModel.categoryName = originalDocument.category.name;
                documentDetailsModel.statusId = originalDocument.status;
                documentDetailsModel.createdByUser = originalDocument.createdByUser;
                documentDetailsModel.statuses = new CoreComponents.Lookup.Statuses().get(window.language);
                documentDetailsModel.showMyTransfer = false;
                var securityMatrix = JSON.parse(new CoreComponents.Lookup.SecurityMatrix().get(window.language));// JSON.parse(sessionStorage.getItem("SecurityMatrix"));
                var tabs = [];
            var nodeId =  TreeNodes.Search;
                if (nodeId !== undefined && $.isNumeric(nodeId)) {
                    tabs = securityMatrix.SecurityNodes[nodeId].SecurityCategories[originalDocument.categoryId].Tabs;
                    documentDetailsModel.tabsActions = securityMatrix.SecurityNodes[nodeId].SecurityCategories[originalDocument.categoryId].SecurityTabs;
                }
                documentDetailsModel.tabs = $.grep(tabs, function (element, index) {
                    return !element.Name.includes("myTransfer") && !element.Name.includes("documentMetadata") &&
                        !element.Name.includes("notes") &&
                        !element.Name.includes("visualTracking") && !element.Name.includes("activityLog") &&
                        !element.Name.includes("linkedDocument") && !element.Name.includes("transferHistory")
                        && !element.Name.includes("attachments") && !element.Name.includes("nonArchivedAttachments");
                });
                documentDetailsModel.tabsWithStatic = tabs;
                documentDetailsModel.showBackButton = false;
                documentDetailsModel.isModal = true;
                documentDetailsModel.attachmentId = originalDocument.attachmentId;
                documentDetailsModel.attachmentVersion = self.model.odAttachmentVersion;
                wrapper = $(followUpOriginalDocumentView.refs['followUpOriginalDocumentDiv']);
                var view = new DocumentDetails.DocumentDetailsView(wrapper, documentDetailsModel);
                view.render();
                $($(view.refs['documentDetailsContainerDiv']).find('.div-flex')[0]).addClass('width100pc-ml0');

                var title = originalDocument.category.name;
                if (window.language === "ar" && originalDocument.category.nameAr !== "") {
                    title = originalDocument.category.nameAr;
                } else if (window.language === "fr" && originalDocument.category.nameFr !== "") {
                    title = originalDocument.category.nameFr;
                }
                if (originalDocument.referenceNumber) {
                    title += ' - ' + originalDocument.referenceNumber;
                }
                $(followUpOriginalDocumentView.refs['modalFollowUpOriginalDocumentTitle']).html(title);
                $(followUpOriginalDocumentView.refs['modalFollowUpOriginalDocument']).removeAttr("style");
                $(followUpOriginalDocumentView.refs['modal-dialog']).attr("style", "width:" + gWidth + "% !important;display: block;padding-left: 9px !important;padding-right: 9px !important;padding-top:" + gPaddingTop + "px;");
                $(followUpOriginalDocumentView.refs['modalFollowUpOriginalDocument']).off("hidden.bs.modal");
                $(followUpOriginalDocumentView.refs['modalFollowUpOriginalDocument']).off("shown.bs.modal");
                $(followUpOriginalDocumentView.refs['modalFollowUpOriginalDocument']).on('shown.bs.modal', function () {
                    gPaddingTop += 30;
                    gWidth -= 5;
                    $("ul[ref='tabDocumentDetails']").scrollingTabs('refresh');
                    originalDocumentModal = $(followUpOriginalDocumentView.refs['modalFollowUpOriginalDocument']);
                });
                $(followUpOriginalDocumentView.refs['modalFollowUpOriginalDocument']).on('hidden.bs.modal', function () {
                    if ($(this).data("remove") != true)
                        return;
                    gPaddingTop -= 30;
                    gWidth += 5;
                    $(followUpOriginalDocumentView.refs[followUpOriginalDocumentView.model.ComponentId]).remove();
                    originalDocumentModal = null;
                    swal.close();
                    //if ($(".modal-window").children().length > 0 || $(".modal-documents").children().length > 0) {
                    //    $('body').addClass('modal-open');
                    //}
                });
                $(followUpOriginalDocumentView.refs['modalFollowUpOriginalDocument']).modal("show");
            } else {
                originalDocumentModal.modal("show")
            }
        });
        $('#' + self.model.parentComponentId +'_btnCancelFollowUp').click(function () {


            var paramData = []
            paramData.push({ 'followUpId': self.model.followUpId, 'subject': self.model.followUpSubject })
            Common.showConfirmMsg(Resources.CancelTaskConfirmation, function () {

                Common.ajaxPost('/FollowUp/CancelFollowUp', { followUpCancelModels: paramData },
                    function (result) {
                        
                        if (result != null && result.length > 0) {
                            let msg = "";
                            if (result.find(f => f).updated == false) {
                                for (var i = 0; i < result.length; i++) {
                                    if (msg == "") {
                                        msg = '○ ' + result[i].message + " : " + result[i].subject;
                                    }
                                    else {
                                        msg = '\n ○ ' + result[i].message + " : " + result[i].subject;
                                    }
                                }
                                Common.alertMsg(msg);
                            }
                            else {
                                Common.showScreenSuccessMsg();
                                tryCloseModal(self.model.parentLinkedDocumentId);
                                TreeNode.refreshTreeNodeCounts(TreeNode.FollowUp);
                                TreeNode.refreshTreeNodeCounts(TreeNode.StructureSent);
                                TreeNode.refreshTreeNodeCounts(TreeNode.Completed);
                                TreeNode.refreshTreeNodeCounts(TreeNode.Closed);
                                TreeNode.refreshTreeNodeCounts(TreeNode.MyRequests);
                                TreeNode.refreshTreeNodeCounts(TreeNode.Sent);
                                swal.close();
                                if ($("#grdFollowUpItems").val() != undefined) {
                                    GridCommon.RefreshCurrentPage("grdFollowUpItems", true);
                                } else {
                                    //    $(".withBorders-o").addClass("waitingBackground");
                                    //    $("#sentDocumentDetailsContainer").empty();
                                    //    $($("input[data-id='" + self.model.id + "']").parents("li")[0]).fadeOut().remove();
                                }
                            }
                        } else {
                            setTimeout(function () {
                                Common.alertMsg(Resources.CantCancelFollowUp);// can't cancel follow up
                            }, 300);
                        }
                    }, function () {
                        Common.showScreenErrorMsg();
                    }, false);
            });
        });
        $('#' + self.model.parentComponentId + '_btnSendEmailReminder').click(function () {
            loadSendEmailReminderModal(self.model.delegationId, self);
        });

    }
}
export default { FollowUpDetailsModel, FollowUpDetailsView };