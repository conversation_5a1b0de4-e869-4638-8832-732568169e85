﻿
update tab set JsFunction='CTSCoreComponents.CustomTabs.openAttachments($transferId, $documentId, $delegationId, $readOnly, $categoryId, $fromInbox, $fromDraft, $isCced, $ComponentId, $tabId, $parentLinkedDocumentId,$actionName,$fromRejectedDocument,$attachmentVersion)' where name ='Attachments'  

IF NOT EXISTS (SELECT 1 FROM TranslatorDictionary WHERE Keyword = N'GenerateBarcode')
BEGIN
    INSERT [dbo].[TranslatorDictionary] ([Keyword], [EN], [FR], [AR], [IsSystem]) 
    VALUES (N'GenerateBarcode', 
            N'Generate barcode',       
            N'Générer le code-barres',       
            N'إنشاء رمز شريطي',  
            1)
END



IF NOT EXISTS (SELECT 1 FROM TranslatorDictionary WHERE Keyword = N'DocumentNotRegistered')
BEGIN
    INSERT [dbo].[TranslatorDictionary] ([Keyword], [EN], [FR], [AR], [IsSystem])
    VALUES (N'DocumentNotRegistered', N'Document Not Registered', N'Document non enregistré', N'الوثيقة غير مسجلة', 1)
END