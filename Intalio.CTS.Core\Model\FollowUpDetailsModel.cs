﻿
using System;
using System.Collections.Generic;

namespace Intalio.CTS.Core.Model
{
    public class FollowUpDetailsModel : DocumentDetailsModel
    {
        public string FollowUpStatusName { get; set; }
        public long FollowUpId { get; set; }
        public long FollowUpStatusId { get; set; }
        public string Priority { get; set; }
        public object OriginalDocument { get; set; }
        public string? ODAttachmentVersion { get; set; }
        public object FollowUpDocument { get; set; }
        public bool Readonly { get; set; }
        public string FollowUpFromDate { get; set; }
        public string FollowUpToDate { get; set; }
        public string SendingEntityName { get; set; }
        public List<string> ReceiversName { get; set; }
        public bool IsPrivate { get; set; }
        public long? TeamId { get; set; }
        public short FollowUpUserRole { get; set; }


    }
}