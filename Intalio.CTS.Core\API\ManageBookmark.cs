﻿using Aspose.Words;
using Intalio.Core;
using Intalio.Core.API;
using Intalio.Core.Model;
using Intalio.CTS.Core.Model;
using Intalio.CTS.Core.Utility;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Aspose.Words.Tables;
using Intalio.Core.Utility;

namespace Intalio.CTS.Core.API
{
    public static class ManageBookmark
    {
        #region Internal Methods

        internal static async Task<bool> FindReplaceBookmarkAndAttachment(long attachmentId, long storageAttachmentId, long documentId, long userId, long? transferId = null, bool convertToPdf = false, bool replaceVersion = false)
        {
            var storageAttachmentModel = await ManageAttachment.GetStorageAttachmentModel(storageAttachmentId);
            if (storageAttachmentModel.Data?.Length > 0)
            {
                FileViewModel fileAttachment = new FileViewModel();
                fileAttachment.Name = Intalio.Core.Helper.GetFileName(storageAttachmentModel.Name);
                fileAttachment.FileSize = Convert.ToInt64(storageAttachmentModel.FileSize);
                fileAttachment.ContentType = convertToPdf == true ? "application/pdf" : storageAttachmentModel.ContentType;
                fileAttachment.Extension = convertToPdf == true ? "pdf" : storageAttachmentModel.Extension;
                var bytes = ManageBookmark.ReplaceBookmark(storageAttachmentModel.Data, documentId, convertToPdf);
                bytes = new EventReceivers().OnDocumentReplaceBoomark(bytes, documentId);
                fileAttachment.Data = bytes;
                await ManageAttachment.Replace(attachmentId, fileAttachment, documentId, transferId, userId, true);
                return true;
            }
            return false;
        }

        /// <summary>
        /// Generate Letter
        /// </summary>
        /// <param name="word"></param>
        /// <param name="documentId"></param>
        /// <param name="convertToPdf"></param>
        /// <param name="generateReceivingEntity"></param>
        /// <param name="generateCarbonCopy"></param>
        /// <returns></returns>
        internal static byte[] GenerateLetter(byte[] word, long documentId, bool generateReceivingEntity, bool generateCarbonCopy, bool convertToPdf = false)
        {
            byte[] retValue = word;
            List<byte[]> words = new List<byte[]>();
            BookmarkUtility bookmarkUtility = new BookmarkUtility();
            List<BookmarkModel> receivingEntityBookmarks = new List<BookmarkModel>();
            receivingEntityBookmarks.Add(new BookmarkModel { Key = "ReceivingEntity", Type = BookmarkType.Text });
            receivingEntityBookmarks.Add(new BookmarkModel { Key = "ReceivingEntityAr", Type = BookmarkType.Text });
            receivingEntityBookmarks.Add(new BookmarkModel { Key = "ReceivingEntityFr", Type = BookmarkType.Text });
            List<TextValue> result = bookmarkUtility.Exists(word, receivingEntityBookmarks);
            if (result.Any(t => t.Value.ToLower() == "true"))
            {
                if (ManageDocument.CheckDocumentDraft(documentId))
                {
                    DAL.Document document = new DAL.Document().FindIncludeDocumentFormClassification(documentId);
                    if (document != null)
                    {
                        string dateFormat = new CoreParameters()["BookmarkDateFormat"] ?? Constants.DATETIME_FORMAT;
                        string createdDateValue = document.CreatedDate.ToString(dateFormat);
                        if (Configuration.CalendarType != Intalio.Core.CalendarType.None)
                        {
                            createdDateValue = Intalio.Core.Helper.ConvertGregorianToHijri(document.CreatedDate, dateFormat);
                        }
                        List<BookmarkModel> bookmarks = new List<BookmarkModel>();
                        bookmarks.Add(new BookmarkModel { Key = "ReferenceNumber", Type = BookmarkType.Text, Value = document.ReferenceNumber });
                        bookmarks.Add(new BookmarkModel { Key = "Subject", Type = BookmarkType.Text, Value = document.Subject });
                        bookmarks.Add(new BookmarkModel { Key = "CreatedDate", Type = BookmarkType.Text, Value = createdDateValue });
                        if (document.SendingEntity != null)
                        {
                            bookmarks.Add(new BookmarkModel { Key = "SendingEntity", Type = BookmarkType.Text, Value = document.SendingEntity.Name });
                            bookmarks.Add(new BookmarkModel { Key = "SendingEntityAr", Type = BookmarkType.Text, Value = document.SendingEntity.NameAr ?? document.SendingEntity.Name });
                            bookmarks.Add(new BookmarkModel { Key = "SendingEntityFr", Type = BookmarkType.Text, Value = document.SendingEntity.NameFr ?? document.SendingEntity.Name });
                        }

                        CategoryNameAttributeModel category = ManageCategory.FindWithCaching(document.CategoryId);
                        bookmarks.Add(new BookmarkModel { Key = "Category", Type = BookmarkType.Text, Value = category?.Name });
                        bookmarks.Add(new BookmarkModel { Key = "CategoryAr", Type = BookmarkType.Text, Value = category?.NameAr ?? category?.Name });
                        bookmarks.Add(new BookmarkModel { Key = "CategoryFr", Type = BookmarkType.Text, Value = category?.NameFr ?? category?.Name });

                        if (document.PriorityId != null)
                        {
                            PriorityListViewModel priority = ManagePriority.FindWithCaching(document.PriorityId.Value);
                            bookmarks.Add(new BookmarkModel { Key = "Priority", Type = BookmarkType.Text, Value = priority?.Name });
                            bookmarks.Add(new BookmarkModel { Key = "PriorityAr", Type = BookmarkType.Text, Value = priority?.NameAr ?? priority?.Name });
                            bookmarks.Add(new BookmarkModel { Key = "PriorityFr", Type = BookmarkType.Text, Value = priority?.NameFr ?? priority?.Name });
                        }
                        if (document.PrivacyId != null)
                        {
                            PrivacyListViewModel privacy = ManagePrivacy.FindWithCaching(document.PrivacyId.Value);
                            bookmarks.Add(new BookmarkModel { Key = "Privacy", Type = BookmarkType.Text, Value = privacy?.Name });
                            bookmarks.Add(new BookmarkModel { Key = "PrivacyAr", Type = BookmarkType.Text, Value = privacy?.NameAr ?? privacy?.Name });
                            bookmarks.Add(new BookmarkModel { Key = "PrivacyFr", Type = BookmarkType.Text, Value = privacy?.NameFr ?? privacy?.Name });
                        }
                        if (document.ImportanceId != null)
                        {
                            ImportanceListViewModel importance = ManageImportance.FindWithCaching(document.ImportanceId.Value);
                            bookmarks.Add(new BookmarkModel { Key = "Importance", Type = BookmarkType.Text, Value = importance?.Name });
                            bookmarks.Add(new BookmarkModel { Key = "ImportanceAr", Type = BookmarkType.Text, Value = importance?.NameAr ?? importance?.Name });
                            bookmarks.Add(new BookmarkModel { Key = "ImportanceFr", Type = BookmarkType.Text, Value = importance?.NameFr ?? importance?.Name });
                        }
                        if (document.DocumentTypeId != null)
                        {
                            DocumentTypeListViewModel documentType = ManageDocumentType.FindWithCaching(document.DocumentTypeId.Value);
                            bookmarks.Add(new BookmarkModel { Key = "DocumentType", Type = BookmarkType.Text, Value = documentType?.Name });
                            bookmarks.Add(new BookmarkModel { Key = "DocumentTypeAr", Type = BookmarkType.Text, Value = documentType?.NameAr ?? documentType?.Name });
                            bookmarks.Add(new BookmarkModel { Key = "DocumentTypeFr", Type = BookmarkType.Text, Value = documentType?.NameFr ?? documentType?.Name });
                        }
                        if (document.Classification != null)
                        {
                            bookmarks.Add(new BookmarkModel { Key = "Classification", Type = BookmarkType.Text, Value = document.Classification.Name });
                            bookmarks.Add(new BookmarkModel { Key = "ClassificationAr", Type = BookmarkType.Text, Value = document.Classification.NameAr ?? document.Classification.Name });
                            bookmarks.Add(new BookmarkModel { Key = "ClassificationFr", Type = BookmarkType.Text, Value = document.Classification.NameFr ?? document.Classification.Name });
                        }
                        if (!string.IsNullOrEmpty(document.DocumentForm?.Body))
                        {
                            bookmarks.Add(new BookmarkModel { Key = "Body", Type = BookmarkType.Text, Value = document.DocumentForm.Body });
                        }
                        string dueDateValue = document.DueDate != null ? ((DateTime)document.DueDate).ToString(Constants.DATE_FORMAT) : null;
                        if (Configuration.CalendarType != Intalio.Core.CalendarType.None && document.DueDate != null)
                        {
                            dueDateValue = Intalio.Core.Helper.ConvertGregorianToHijri(document.DueDate.Value, Constants.DATE_FORMAT);
                        }
                        bookmarks.Add(new BookmarkModel { Key = "DueDate", Type = BookmarkType.Text, Value = dueDateValue });
                        if (document.CreatedByUser != null)
                        {
                            var language = Helper.GetLanguage();
                            bookmarks.Add(new BookmarkModel { Key = "CreatedBy", Type = BookmarkType.Text, Value = language==Intalio.Core.Language.EN? $"{document.CreatedByUser.Firstname} {document.CreatedByUser.Lastname}": $"{IdentityHelperExtension.GetFullName(document.CreatedByUser.Id, language)}" });
                        }
                        bookmarks.Add(new BookmarkModel { Key = "LoggedInUser", Type = BookmarkType.Text, Value = UserContextAccessor.UserContext?.FullName });

                        string todayDateValue = DateTime.Now.ToString(dateFormat);
                        if (Configuration.CalendarType != Intalio.Core.CalendarType.None)
                        {
                            todayDateValue = Intalio.Core.Helper.ConvertGregorianToHijri(DateTime.Now, dateFormat);
                        }
                        bookmarks.Add(new BookmarkModel { Key = "TodayDate", Type = BookmarkType.Text, Value = todayDateValue });

                        if (!string.IsNullOrEmpty(category?.Attribute) && !string.IsNullOrEmpty(document.DocumentForm?.Form))
                        {
                            bookmarks.AddRange(new FormBuilderUtility().GetBookmarks(category.Attribute, category.AttributeTranslation, document.DocumentForm.Form));
                        }
                        word = bookmarkUtility.Replace(word, bookmarks);
                        List<long> structureIdReceivers = new List<long>();
                        if (generateReceivingEntity)
                        {
                            if (!document.DocumentReceiverEntity.IsNullOrEmpty())
                            {
                                structureIdReceivers = document.DocumentReceiverEntity.Where(d => d.StructureId.HasValue).Select(d => (long)d.StructureId).ToList();
                                bookmarks = new List<BookmarkModel>();
                                foreach (var documentReceiver in document.DocumentReceiverEntity)
                                {
                                    string receivingEntity = documentReceiver.Structure.IsNull() ? documentReceiver.EntityGroup.Name : documentReceiver.Structure.Name;
                                    string receivingEntityAr = documentReceiver.Structure.IsNull() ? (documentReceiver.EntityGroup.NameAr ?? documentReceiver.EntityGroup.Name) : (documentReceiver.Structure.NameAr ?? documentReceiver.Structure.Name);
                                    string receivingEntityFr = documentReceiver.Structure.IsNull() ? (documentReceiver.EntityGroup.NameFr ?? documentReceiver.EntityGroup.Name) : (documentReceiver.Structure.NameFr ?? documentReceiver.Structure.Name);
                                    if (bookmarks.Any(x => x.Key == "ReceivingEntity"))
                                    {
                                        bookmarks.First(x => x.Key == "ReceivingEntity").Value = receivingEntity;
                                    }
                                    else
                                    {
                                        bookmarks.Add(new BookmarkModel { Key = "ReceivingEntity", Type = BookmarkType.Text, Value = receivingEntity });
                                    }
                                    if (bookmarks.Any(x => x.Key == "ReceivingEntityAr"))
                                    {
                                        bookmarks.First(x => x.Key == "ReceivingEntityAr").Value = receivingEntityAr;
                                    }
                                    else
                                    {
                                        bookmarks.Add(new BookmarkModel { Key = "ReceivingEntityAr", Type = BookmarkType.Text, Value = receivingEntityAr });
                                    }
                                    if (bookmarks.Any(x => x.Key == "ReceivingEntityFr"))
                                    {
                                        bookmarks.First(x => x.Key == "ReceivingEntityFr").Value = receivingEntityFr;
                                    }
                                    else
                                    {
                                        bookmarks.Add(new BookmarkModel { Key = "ReceivingEntityFr", Type = BookmarkType.Text, Value = receivingEntityFr });
                                    }
                                    words.Add(bookmarkUtility.Replace(word, bookmarks));
                                }
                            }
                        }
                        if (generateCarbonCopy)
                        {
                            if (!document.DocumentCarbonCopy.IsNullOrEmpty())
                            {
                                bookmarks = new List<BookmarkModel>();
                                var carbonCopies = document.DocumentCarbonCopy.Where(c => !structureIdReceivers.Contains(c.StructureId));
                                foreach (var carbonCopy in carbonCopies)
                                {
                                    if (bookmarks.Any(x => x.Key == "ReceivingEntity"))
                                    {
                                        bookmarks.First(x => x.Key == "ReceivingEntity").Value = carbonCopy.Structure.Name;
                                    }
                                    else
                                    {
                                        bookmarks.Add(new BookmarkModel { Key = "ReceivingEntity", Type = BookmarkType.Text, Value = carbonCopy.Structure.Name });
                                    }
                                    if (bookmarks.Any(x => x.Key == "ReceivingEntityAr"))
                                    {
                                        bookmarks.First(x => x.Key == "ReceivingEntityAr").Value = carbonCopy.Structure.NameAr ?? carbonCopy.Structure.Name;
                                    }
                                    else
                                    {
                                        bookmarks.Add(new BookmarkModel { Key = "ReceivingEntityAr", Type = BookmarkType.Text, Value = carbonCopy.Structure.NameAr ?? carbonCopy.Structure.Name });
                                    }
                                    if (bookmarks.Any(x => x.Key == "ReceivingEntityFr"))
                                    {
                                        bookmarks.First(x => x.Key == "ReceivingEntityFr").Value = carbonCopy.Structure.NameFr ?? carbonCopy.Structure.Name;
                                    }
                                    else
                                    {
                                        bookmarks.Add(new BookmarkModel { Key = "ReceivingEntityFr", Type = BookmarkType.Text, Value = carbonCopy.Structure.NameFr ?? carbonCopy.Structure.Name });
                                    }
                                    words.Add(bookmarkUtility.Replace(word, bookmarks));
                                }
                            }
                        }
                    }
                }
                else
                {
                    DAL.Document document = new DAL.Document().FindIncludeEntities(documentId);
                    if (document != null)
                    {
                        List<long> structureIdReceivers = new List<long>();
                        if (generateReceivingEntity)
                        {
                            if (!document.DocumentReceiverEntity.IsNullOrEmpty())
                            {
                                structureIdReceivers = document.DocumentReceiverEntity.Where(d => d.StructureId.HasValue).Select(d => (long)d.StructureId).ToList();
                                List<BookmarkModel> bookmarks = new List<BookmarkModel>();
                                foreach (var documentReceiver in document.DocumentReceiverEntity)
                                {
                                    string receivingEntity = documentReceiver.Structure.IsNull() ? documentReceiver.EntityGroup.Name : documentReceiver.Structure.Name;
                                    string receivingEntityAr = documentReceiver.Structure.IsNull() ? (documentReceiver.EntityGroup.NameAr ?? documentReceiver.EntityGroup.Name) : (documentReceiver.Structure.NameAr ?? documentReceiver.Structure.Name);
                                    string receivingEntityFr = documentReceiver.Structure.IsNull() ? (documentReceiver.EntityGroup.NameFr ?? documentReceiver.EntityGroup.Name) : (documentReceiver.Structure.NameFr ?? documentReceiver.Structure.Name);
                                    if (bookmarks.Any(x => x.Key == "ReceivingEntity"))
                                    {
                                        bookmarks.First(x => x.Key == "ReceivingEntity").Value = receivingEntity;
                                    }
                                    else
                                    {
                                        bookmarks.Add(new BookmarkModel { Key = "ReceivingEntity", Type = BookmarkType.Text, Value = receivingEntity });
                                    }
                                    if (bookmarks.Any(x => x.Key == "ReceivingEntityAr"))
                                    {
                                        bookmarks.First(x => x.Key == "ReceivingEntityAr").Value = receivingEntityAr;
                                    }
                                    else
                                    {
                                        bookmarks.Add(new BookmarkModel { Key = "ReceivingEntityAr", Type = BookmarkType.Text, Value = receivingEntityAr });
                                    }
                                    if (bookmarks.Any(x => x.Key == "ReceivingEntityFr"))
                                    {
                                        bookmarks.First(x => x.Key == "ReceivingEntityFr").Value = receivingEntityFr;
                                    }
                                    else
                                    {
                                        bookmarks.Add(new BookmarkModel { Key = "ReceivingEntityFr", Type = BookmarkType.Text, Value = receivingEntityFr });
                                    }
                                    words.Add(bookmarkUtility.Replace(word, bookmarks));
                                }
                            }
                        }
                        if (generateCarbonCopy)
                        {
                            if (!document.DocumentCarbonCopy.IsNullOrEmpty())
                            {
                                List<BookmarkModel> bookmarks = new List<BookmarkModel>();
                                var carbonCopies = document.DocumentCarbonCopy.Where(c => !structureIdReceivers.Contains(c.StructureId));
                                foreach (var carbonCopy in carbonCopies)
                                {
                                    if (bookmarks.Any(x => x.Key == "ReceivingEntity"))
                                    {
                                        bookmarks.First(x => x.Key == "ReceivingEntity").Value = carbonCopy.Structure.Name;
                                    }
                                    else
                                    {
                                        bookmarks.Add(new BookmarkModel { Key = "ReceivingEntity", Type = BookmarkType.Text, Value = carbonCopy.Structure.Name });
                                    }
                                    if (bookmarks.Any(x => x.Key == "ReceivingEntityAr"))
                                    {
                                        bookmarks.First(x => x.Key == "ReceivingEntityAr").Value = carbonCopy.Structure.NameAr ?? carbonCopy.Structure.Name;
                                    }
                                    else
                                    {
                                        bookmarks.Add(new BookmarkModel { Key = "ReceivingEntityAr", Type = BookmarkType.Text, Value = carbonCopy.Structure.NameAr ?? carbonCopy.Structure.Name });
                                    }
                                    if (bookmarks.Any(x => x.Key == "ReceivingEntityFr"))
                                    {
                                        bookmarks.First(x => x.Key == "ReceivingEntityFr").Value = carbonCopy.Structure.NameFr ?? carbonCopy.Structure.Name;
                                    }
                                    else
                                    {
                                        bookmarks.Add(new BookmarkModel { Key = "ReceivingEntityFr", Type = BookmarkType.Text, Value = carbonCopy.Structure.NameFr ?? carbonCopy.Structure.Name });
                                    }
                                    words.Add(bookmarkUtility.Replace(word, bookmarks));
                                }
                            }
                        }
                    }
                }
                retValue = new WordUtility().Merge(words);
            }
            if (convertToPdf)
            {
                retValue = new WordUtility().ConvertToPDF(retValue);
            }
            return retValue;
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Replace bookmark
        /// </summary>
        /// <param name="word"></param>
        /// <param name="documentId"></param>
        /// <returns></returns>
        /// 
        public static byte[] ReplaceBookmark(byte[] word, long documentId, bool convertToPdf = false, string referenceNumber = null)
        {
            byte[] retValue = word;
            DAL.Document document = new DAL.Document().FindIncludeDocumentFormClassification(documentId);
            document.ReferenceNumber = referenceNumber != null ? referenceNumber : document.ReferenceNumber;
            if (document != null)
            {
                string dateFormat = new CoreParameters()["BookmarkDateFormat"] ?? Constants.DATETIME_FORMAT;
                string multipleValueSeperator = new CoreParameters()["MultipleValueSeperator"] ?? Constants.SEPARATOR;
                string createdDateValue = document.CreatedDate.ToString(dateFormat);
                if (Configuration.CalendarType != Intalio.Core.CalendarType.None)
                {
                    createdDateValue = Intalio.Core.Helper.ConvertGregorianToHijri(document.CreatedDate, dateFormat);
                }
                List<BookmarkModel> bookmarks = new List<BookmarkModel>();
                bookmarks.Add(new BookmarkModel { Key = "ReferenceNumber", Type = BookmarkType.Text, Value = document.ReferenceNumber });
                bookmarks.Add(new BookmarkModel { Key = "Subject", Type = BookmarkType.Text, Value = document.Subject });
                bookmarks.Add(new BookmarkModel { Key = "CreatedDate", Type = BookmarkType.Text, Value = createdDateValue });
                bookmarks.Add(new BookmarkModel { Key = "Signature", Type = BookmarkType.Text, Value = "Signature" });
                if (document.SendingEntity != null)
                {
                    bookmarks.Add(new BookmarkModel { Key = "SendingEntity", Type = BookmarkType.Text, Value = document.SendingEntity.Name });
                    bookmarks.Add(new BookmarkModel { Key = "SendingEntityAr", Type = BookmarkType.Text, Value = document.SendingEntity.NameAr ?? document.SendingEntity.Name });
                    bookmarks.Add(new BookmarkModel { Key = "SendingEntityFr", Type = BookmarkType.Text, Value = document.SendingEntity.NameFr ?? document.SendingEntity.Name });
                }
                if (!document.DocumentReceiverEntity.IsNullOrEmpty())
                    if (!document.DocumentReceiverEntity.IsNullOrEmpty())
                    {
                        bookmarks.Add(new BookmarkModel { Key = "ReceivingEntity", Type = BookmarkType.Text, Value = string.Join(multipleValueSeperator, document.DocumentReceiverEntity.Select(t => t.Structure.IsNull() ? t.EntityGroup.Name : t.Structure.Name).ToArray()) });
                        bookmarks.Add(new BookmarkModel { Key = "ReceivingEntityAr", Type = BookmarkType.Text, Value = string.Join(multipleValueSeperator, document.DocumentReceiverEntity.Select(t => t.Structure.IsNull()? (string.IsNullOrEmpty(t.EntityGroup.NameAr) ? t.EntityGroup.Name : t.EntityGroup.NameAr): (string.IsNullOrEmpty(t.Structure.NameAr) ? t.Structure.Name : t.Structure.NameAr)))});
                        bookmarks.Add(new BookmarkModel { Key = "ReceivingEntityFr", Type = BookmarkType.Text, Value = string.Join(multipleValueSeperator, document.DocumentReceiverEntity.Select(t => t.Structure.IsNull()? (string.IsNullOrEmpty(t.EntityGroup.NameFr) ? t.EntityGroup.Name : t.EntityGroup.NameFr): (string.IsNullOrEmpty(t.Structure.NameFr) ? t.Structure.Name : t.Structure.NameFr)))});
                    }

                CategoryNameAttributeModel category = ManageCategory.FindWithCaching(document.CategoryId);
                bookmarks.Add(new BookmarkModel { Key = "Category", Type = BookmarkType.Text, Value = category?.Name });
                bookmarks.Add(new BookmarkModel { Key = "CategoryAr", Type = BookmarkType.Text, Value = category?.NameAr ?? category?.Name });
                bookmarks.Add(new BookmarkModel { Key = "CategoryFr", Type = BookmarkType.Text, Value = category?.NameFr ?? category?.Name });

                if (document.PriorityId != null)
                {
                    PriorityListViewModel priority = ManagePriority.FindWithCaching(document.PriorityId.Value);
                    bookmarks.Add(new BookmarkModel { Key = "Priority", Type = BookmarkType.Text, Value = priority?.Name });
                    bookmarks.Add(new BookmarkModel { Key = "PriorityAr", Type = BookmarkType.Text, Value = priority?.NameAr ?? priority?.Name });
                    bookmarks.Add(new BookmarkModel { Key = "PriorityFr", Type = BookmarkType.Text, Value = priority?.NameFr ?? priority?.Name });
                }
                if (document.PrivacyId != null)
                {
                    PrivacyListViewModel privacy = ManagePrivacy.FindWithCaching(document.PrivacyId.Value);
                    string? textColor = null;
                    if(Configuration.UsePrivacyColorInBookmark)
                    {
                        textColor = privacy.Color;
                    }
                    bookmarks.Add(new BookmarkModel { Key = "Privacy", Type = BookmarkType.Text, Value = privacy?.Name, Color = textColor });
                    bookmarks.Add(new BookmarkModel { Key = "PrivacyAr", Type = BookmarkType.Text, Value = privacy?.NameAr ?? privacy?.Name, Color = textColor });
                    bookmarks.Add(new BookmarkModel { Key = "PrivacyFr", Type = BookmarkType.Text, Value = privacy?.NameFr ?? privacy?.Name, Color = textColor });
                }
                if (document.ImportanceId != null)
                {
                    ImportanceListViewModel importance = ManageImportance.FindWithCaching(document.ImportanceId.Value);
                    bookmarks.Add(new BookmarkModel { Key = "Importance", Type = BookmarkType.Text, Value = importance?.Name });
                    bookmarks.Add(new BookmarkModel { Key = "ImportanceAr", Type = BookmarkType.Text, Value = importance?.NameAr ?? importance?.Name });
                    bookmarks.Add(new BookmarkModel { Key = "ImportanceFr", Type = BookmarkType.Text, Value = importance?.NameFr ?? importance?.Name });
                }
                if (document.DocumentTypeId != null)
                {
                    DocumentTypeListViewModel documentType = ManageDocumentType.FindWithCaching(document.DocumentTypeId.Value);
                    bookmarks.Add(new BookmarkModel { Key = "DocumentType", Type = BookmarkType.Text, Value = documentType?.Name });
                    bookmarks.Add(new BookmarkModel { Key = "DocumentTypeAr", Type = BookmarkType.Text, Value = documentType?.NameAr ?? documentType?.Name });
                    bookmarks.Add(new BookmarkModel { Key = "DocumentTypeFr", Type = BookmarkType.Text, Value = documentType?.NameFr ?? documentType?.Name });
                }
                if (document.Classification != null)
                {
                    bookmarks.Add(new BookmarkModel { Key = "Classification", Type = BookmarkType.Text, Value = document.Classification.Name });
                    bookmarks.Add(new BookmarkModel { Key = "ClassificationAr", Type = BookmarkType.Text, Value = document.Classification.NameAr ?? document.Classification.Name });
                    bookmarks.Add(new BookmarkModel { Key = "ClassificationFr", Type = BookmarkType.Text, Value = document.Classification.NameFr ?? document.Classification.Name });
                }
                if (!document.DocumentCarbonCopy.IsNullOrEmpty())
                {
                    bookmarks.Add(new BookmarkModel { Key = "CarbonCopy", Type = BookmarkType.Text, Value = string.Join(multipleValueSeperator, document.DocumentCarbonCopy.Select(t => t.Structure.Name).ToArray()) });
                    bookmarks.Add(new BookmarkModel { Key = "CarbonCopyAr", Type = BookmarkType.Text, Value = string.Join(multipleValueSeperator, document.DocumentCarbonCopy.Select(t => t.Structure.NameAr ?? t.Structure.Name).ToArray()) });
                    bookmarks.Add(new BookmarkModel { Key = "CarbonCopyFr", Type = BookmarkType.Text, Value = string.Join(multipleValueSeperator, document.DocumentCarbonCopy.Select(t => t.Structure.NameFr ?? t.Structure.Name).ToArray()) });
                }
                if (!string.IsNullOrEmpty(document.DocumentForm?.Body))
                {
                    bookmarks.Add(new BookmarkModel { Key = "Body", Type = BookmarkType.Text, Value = document.DocumentForm.Body });
                }
                string dueDateValue = document.DueDate != null ? ((DateTime)document.DueDate).ToString(Constants.DATE_FORMAT) : null;
                if (Configuration.CalendarType != Intalio.Core.CalendarType.None && document.DueDate != null)
                {
                    dueDateValue = Intalio.Core.Helper.ConvertGregorianToHijri(document.DueDate.Value, Constants.DATE_FORMAT);
                }
                bookmarks.Add(new BookmarkModel { Key = "DueDate", Type = BookmarkType.Text, Value = dueDateValue });
                if (document.CreatedByUser != null)
                {
                    var language = Helper.GetLanguage();
                    bookmarks.Add(new BookmarkModel { Key = "CreatedBy", Type = BookmarkType.Text, Value =language==Language.EN? $"{document.CreatedByUser.Firstname} {document.CreatedByUser.Lastname}": $"{IdentityHelperExtension.GetFullName(document.CreatedByUser.Id, language)}" });
                }
                bookmarks.Add(new BookmarkModel { Key = "LoggedInUser", Type = BookmarkType.Text, Value = UserContextAccessor.UserContext?.FullName });

                string todayDateValue = DateTime.Now.ToString(dateFormat);
                if (Configuration.CalendarType != Intalio.Core.CalendarType.None)
                {
                    todayDateValue = Intalio.Core.Helper.ConvertGregorianToHijri(DateTime.Now, dateFormat);
                }
                bookmarks.Add(new BookmarkModel { Key = "TodayDate", Type = BookmarkType.Text, Value = todayDateValue });

                
                var hijriDate = Convert.ToDateTime(Calendar.ConvertToIslamic(DateTime.Now).ToString());
                hijriDate = hijriDate.AddHours(DateTime.Now.Hour).AddMinutes(DateTime.Now.Minute).AddSeconds(DateTime.Now.Second);
                hijriDate = Configuration.CalendarType == Intalio.Core.CalendarType.Civil ? hijriDate.AddDays(-1) : hijriDate;
                bookmarks.Add(new BookmarkModel { Key = "TodayHijriDate", Type = BookmarkType.Text, Value = hijriDate.ToString(dateFormat) });

                if (!string.IsNullOrEmpty(category?.Attribute) && !string.IsNullOrEmpty(document.DocumentForm?.Form) && document.DocumentForm?.Form != "[]")
                {
                    bookmarks.AddRange(new FormBuilderUtility().GetBookmarks(category.Attribute, category.AttributeTranslation, document.DocumentForm.Form));
                }
                retValue = new Intalio.Core.BookmarkUtility().Replace(word, bookmarks);

                if(!string.IsNullOrEmpty(document.ReferenceNumber))
                {
                    AsposeLicense asposeLicense = new AsposeLicense();
                    Stream licenseStream = asposeLicense.Get();
                    if (licenseStream != null)
                    {
                        Aspose.Words.License license = new Aspose.Words.License();
                        license.SetLicense(licenseStream);
                    }

                    Document doc;
                    using (MemoryStream ms = new MemoryStream(retValue))
                    {
                        doc = new Document(ms);
                    }

                    NodeCollection shapes = doc.GetChildNodes(Aspose.Words.NodeType.Shape, true);

                    //This should be saved in some constant place for bookmarks
                    const string BARCODE = nameof(BARCODE);
                    foreach (Aspose.Words.Drawing.Shape shape in shapes)
                    {
                        if (shape.HasImage && shape.AlternativeText.Equals(BARCODE, StringComparison.OrdinalIgnoreCase))
                        {
                            byte[] referenceNumberImage;
                            if (string.IsNullOrEmpty(referenceNumber))
                            {
                                referenceNumberImage = ManageDocument.GenerateBarcode(documentId);
                            }
                            else
                            {
                                referenceNumberImage = ManageDocument.GenerateBarcode(documentId, referenceNumber);
                            }
                            using (MemoryStream imgStream = new MemoryStream(referenceNumberImage))
                            {
                                shape.ImageData.SetImage(imgStream);
                            }
                        }
                    }

                    using (MemoryStream outStream = new MemoryStream())
                    {
                        doc.Save(outStream, SaveFormat.Docx);
                        retValue = outStream.ToArray();
                    }
                }

                if (convertToPdf)
                {
                    retValue = new WordUtility().ConvertToPDF(retValue);
                }
            }
            return retValue;
        }

        public static void AddAgendaTables(FileViewModel file, List<long> documentIds) {

            Stream stream = new Intalio.Core.Utility.AsposeLicense().Get();
            if (stream != null)
            {
                new License().SetLicense(stream);
            }

            if (documentIds.Count>0)
            {
                MemoryStream inStream = new MemoryStream(file.Data);
                Aspose.Words.Document asposeDocument = new Aspose.Words.Document(inStream);
                DocumentBuilder documentBuilder = new DocumentBuilder(asposeDocument);

               // string? json = document.DocumentForm.Form != null && document.DocumentForm.Form.Split("\"dataGrid\":")[1] != null ? document.DocumentForm.Form.Split("\"dataGrid\":")[1].Remove(document.DocumentForm.Form.Split("\"dataGrid\":")[1].Length - 1, 1) : null;
                //var definition = new[] { new { textField = "", textField2 = "" } };
                Bookmark bookmark = asposeDocument.Range.Bookmarks["EntityTable"];
                if (bookmark != null)
                {
                    Bookmark bm = asposeDocument.Range.Bookmarks["table1"];
                    if (bm != null)
                    {
                        Table tabled = (Table)bm.BookmarkStart.GetAncestor(Aspose.Words.NodeType.Table);
                        if (tabled != null)
                        {
                            tabled.Remove();
                        }
                    }

                    // var result = JsonConvert.DeserializeAnonymousType(json, definition);
                    List<BookmarkModel> list = new List<BookmarkModel>();

                    documentBuilder.MoveToBookmark("EntityTable", isStart: true, isAfter: true);
                    bookmark.Text = "";
                    //bookmark.Remove();
                    documentBuilder.StartBookmark("EntityTable");
                    Table table = documentBuilder.StartTable();
                    documentBuilder.RowFormat.Height = 30.0;
                    documentBuilder.RowFormat.HeightRule = HeightRule.AtLeast;
                    documentBuilder.CellFormat.VerticalAlignment = CellVerticalAlignment.Center;
                    documentBuilder.InsertCell();
                    documentBuilder.Write("التفاصيل");
                    documentBuilder.StartBookmark("table1");
                    documentBuilder.EndBookmark("table1");
                    documentBuilder.InsertCell();
                    documentBuilder.Write("رقم");
                    documentBuilder.EndRow();
                    documentBuilder.ParagraphFormat.Alignment = ParagraphAlignment.Left;
                    documentBuilder.Font.Size = 12.0;
                    documentBuilder.Font.Bold = false;
                    documentBuilder.Font.Name = "HASSOB";

                    for (int i = 0; i < documentIds.Count; i++)
                    {
                        DAL.Document document = new DAL.Document().Find(documentIds[i]);

                        documentBuilder.InsertCell();
                        documentBuilder.Write(document.Subject);
                        documentBuilder.InsertCell();
                        documentBuilder.Write((i+1).ToString());
                        documentBuilder.EndRow();
                    }
                    documentBuilder.EndTable();
                    documentBuilder.EndBookmark("EntityTable");
            //        table.ClearBorders();
             //       table.ClearShading();

                    MemoryStream outStream = new MemoryStream();
                    asposeDocument.Save(outStream, SaveFormat.Docx);
                    file.Data = outStream.ToArray();
                    file.FileSize = Convert.ToInt64(outStream.Length);
                }
            }
        }



        public static void AddMeetingResolutionTables(FileViewModel file, List<long> documentIds)
        {
            Stream stream = new Intalio.Core.Utility.AsposeLicense().Get();
            if (stream != null)
            {
                new License().SetLicense(stream);
            }
            if (documentIds.Count > 0)
            {
                MemoryStream inStream = new MemoryStream(file.Data);
                Aspose.Words.Document asposeDocument = new Aspose.Words.Document(inStream);
                DocumentBuilder documentBuilder = new DocumentBuilder(asposeDocument);

                // string? json = document.DocumentForm.Form != null && document.DocumentForm.Form.Split("\"dataGrid\":")[1] != null ? document.DocumentForm.Form.Split("\"dataGrid\":")[1].Remove(document.DocumentForm.Form.Split("\"dataGrid\":")[1].Length - 1, 1) : null;
                //var definition = new[] { new { textField = "", textField2 = "" } };
                Bookmark bookmark = asposeDocument.Range.Bookmarks["EntityTable"];
                if (bookmark != null)
                {
                    Bookmark bm = asposeDocument.Range.Bookmarks["table1"];
                    if (bm != null)
                    {
                        Table tabled = (Table)bm.BookmarkStart.GetAncestor(Aspose.Words.NodeType.Table);
                        if (tabled != null)
                        {
                            tabled.Remove();
                        }
                    }

                    // var result = JsonConvert.DeserializeAnonymousType(json, definition);
                    List<BookmarkModel> list = new List<BookmarkModel>();

                    documentBuilder.MoveToBookmark("EntityTable", isStart: true, isAfter: true);
                    bookmark.Text = "";
                    //bookmark.Remove();
                    documentBuilder.StartBookmark("EntityTable");
                    Table table = documentBuilder.StartTable();
                    documentBuilder.RowFormat.Height = 30.0;
                    documentBuilder.RowFormat.HeightRule = HeightRule.AtLeast;
                    documentBuilder.CellFormat.VerticalAlignment = CellVerticalAlignment.Center;
                    documentBuilder.ParagraphFormat.Alignment = ParagraphAlignment.Center;
                    documentBuilder.InsertCell();
                    documentBuilder.CellFormat.HorizontalMerge = CellMerge.First;
                    documentBuilder.Write("قرارات مجلس إدارة بنك قطر للتنمية خلال إجتماعه ..... للعلم......");
                    documentBuilder.StartBookmark("table1");
                    documentBuilder.EndBookmark("table1");
                    documentBuilder.InsertCell();
                    documentBuilder.CellFormat.HorizontalMerge = CellMerge.Previous;
                    //     documentBuilder.Write("رقم");
                    documentBuilder.InsertCell();
                    documentBuilder.CellFormat.HorizontalMerge = CellMerge.Previous;
                    documentBuilder.EndRow();
                    documentBuilder.ParagraphFormat.Alignment = ParagraphAlignment.Left;
                    documentBuilder.Font.Size = 12.0;
                    documentBuilder.Font.Bold = false;
                    documentBuilder.Font.Name = "HASSOB";

                    for (int i = 0; i < documentIds.Count; i++)
                    {
                        DAL.Document document = new DAL.Document().Find(documentIds[i]);

                        documentBuilder.InsertCell();
                        documentBuilder.CellFormat.HorizontalMerge = CellMerge.None;
                        documentBuilder.CellFormat.Width = 30;
                        documentBuilder.InsertCell();
                        documentBuilder.CellFormat.Width = 20;
                        documentBuilder.Write(document.Subject);
                        documentBuilder.InsertCell();
                        documentBuilder.CellFormat.Width = 10;
                        documentBuilder.Write((i + 1).ToString());
                        documentBuilder.EndRow();
                    }
                    documentBuilder.EndTable();
                    documentBuilder.EndBookmark("EntityTable");
                    //        table.ClearBorders();
                    //       table.ClearShading();

                    MemoryStream outStream = new MemoryStream();
                    asposeDocument.Save(outStream, SaveFormat.Docx);
                    file.Data = outStream.ToArray();
                    file.FileSize = Convert.ToInt64(outStream.Length);
                }
            }
        }

        /// <summary>
        /// Get bookmarks by categoryId
        /// </summary>
        /// <param name="categoryId"></param>
        /// <returns></returns>
        public static List<string> GetBookmarksByCategoryId(int categoryId)
        {
            List<string> retValue = null;
            var customAttributes = ManageCategory.GetCustomAttribute(categoryId);
            if (!string.IsNullOrEmpty(customAttributes?.Attribute))
            {
                retValue = new FormBuilderUtility().GetBookmarkList(customAttributes.Attribute);
            }
            return retValue;
        }

        /// <summary>
        /// Get bookmark form by id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public static (string FormDesigner, string FormDesignerTranslation) GetBookmarkFormById(int id)
        {
            var retValue = (string.Empty, string.Empty);
            var customAttributes = ManageCategory.GetCustomAttribute(id);
            if (!string.IsNullOrEmpty(customAttributes?.Attribute))
            {
                retValue.Item1 = new FormBuilderUtility().ReplaceFormBookmarks(customAttributes.Attribute);
                retValue.Item2 = customAttributes?.AttributeTranslation;
            }
            return retValue;
        }

        #endregion

    }
}
