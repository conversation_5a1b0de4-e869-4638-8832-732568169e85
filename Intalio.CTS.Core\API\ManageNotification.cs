﻿using Aspose.Words.Lists;
using Intalio.Core;
using Intalio.Core.API;
using Intalio.CTS.Core.DAL;
using MathNet.Numerics.Distributions;
using Microsoft.AspNetCore.SignalR;
using NPOI.HSSF.Record.PivotTable;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Intalio.CTS.Core.API
{
    public static class ManageNotification
    {
        private const string MSG_KEYWORD = "NotificationMsg";

        #region Public Methods

        public static void Send(this IHubContext<CommunicationHub> hub, long documentId, Language language)
        {
            System.Threading.Tasks.Task.Run(() =>
            {
                try
                {
                    var document = new Document().FindIncludeTransfers(documentId);
                    if (document != null)
                    {
                        foreach (var transfer in document.Transfer)
                        {
                            var nodeId = RetrieveNodeIdToNotification(transfer);
                            var referenceNumber = transfer.Document.ReferenceNumber;
                            var subject = transfer.Document.Subject;
                            var maxSubjectLength = 30;
                            var trimmedSubject = subject?.Length > maxSubjectLength
                                ? subject.Substring(0, maxSubjectLength) + "..."
                                : subject;

                            string separator = "\n";
                            List<string> lines = new List<string>();

                            if (!string.IsNullOrEmpty(referenceNumber))
                            {
                                lines.Add($"{TranslationUtility.Translate("ReferenceNumber", language)}: {referenceNumber}.");
                            }

                            if (!string.IsNullOrEmpty(trimmedSubject))
                            {
                                lines.Add($"{TranslationUtility.Translate("Subject", language)}: {trimmedSubject}.");
                            }

                            string refAndSubjectPart = string.Join(separator, lines);

                            var message = string.Format(TranslationUtility.Translate("NotificationMsg", language),refAndSubjectPart).Replace("\\n", separator);

                            if (transfer.ToUserId != null)
                            {


                                hub.Clients.User(transfer.ToUserId.ToString()).SendAsync("ReceiveMessage", new
                                {
                                    message = message,
                                    data = new
                                    {
                                        transferId = transfer.Id,
                                        nodeId= nodeId.ToString()
                                    },
                                });
                            }
                            else

                            {
                                if (transfer.ToStructureId != null)
                                {
                                    hub.Clients.Group($"S-{transfer.ToStructureId}-{document.Privacy?.Level}").SendAsync("ReceiveMessage", new
                                    {
                                        message = message,
                                        data = new
                                        {
                                            transferId = transfer.Id,
                                            nodeId = nodeId.ToString()
                                        },
                                    });
                                }
                            }
                        }
                        if (Configuration.EnableEmailNotification)
                        {
                            foreach (var transfer in document.Transfer)
                            {
                                
                                if (transfer.ToUserId == null)
                                {

                                    var structureUsers = IdentityHelper.GetStructureAllUsers(transfer.ToStructureId.Value, Configuration.IdentityAccessToken);
                                    if (!structureUsers.IsNullOrEmpty())
                                    {
                                        foreach (var user in structureUsers)
                                        {
                                            if (user.Attributes.Where(t => t.Text == Configuration.UserStructureReceiver).Select(t => Convert.ToBoolean(t.Value)).FirstOrDefault() == true)
                                            {
                                                var userPrivacy = user.Attributes.Where(t => t.Text == Configuration.UserPrivacy).Select(t => Convert.ToInt16(t.Value)).FirstOrDefault();
                                                var userPrivacyLevel = !userPrivacy.IsNull() ? ManagePrivacy.CheckPrivacyLevel(userPrivacy) : ManagePrivacy.GetLowestLevel();
                                  
                                                if (userPrivacyLevel >= document.Privacy?.Level)
                                                {
                                                    string senderUser = IdentityHelper.GetUser(transfer.FromUserId.Value, Configuration.IdentityAccessToken).FirstName + " " + IdentityHelper.GetUser(transfer.FromUserId.Value, Configuration.IdentityAccessToken).LastName;

                                                    var purpose = ManagePurpose.FindWithCaching((short)transfer.PurposeId).Name;
                                                    var priority = ManagePriority.FindWithCaching((short)document.PriorityId).Name;
                                                    SendOnTransferEmail(user.Email, document.ReferenceNumber, transfer.Id, user.FirstName + " " + user.LastName, senderUser, document.SendingEntity.Name, purpose, document.Subject, priority, document.Privacy.Name, document.CreatedDate.ToString());
                                                }
                                            }
                                        }
                                    }
                                }
                                else
                                {
                                    var user = IdentityHelper.GetUser(transfer.ToUserId.Value, Configuration.IdentityAccessToken);
                                    string senderUser = IdentityHelper.GetUser(transfer.FromUserId.Value, Configuration.IdentityAccessToken).FirstName + " " + IdentityHelper.GetUser(transfer.FromUserId.Value, Configuration.IdentityAccessToken).LastName;
                                    var purpose = ManagePurpose.FindWithCaching((short)transfer.PurposeId).Name;
                                    var priority = ManagePriority.FindWithCaching((short)document.PriorityId).Name;

                                    SendOnTransferEmail(user.Email, document.ReferenceNumber, transfer.Id, user.FirstName + " " + user.LastName, senderUser, document.SendingEntity.Name, purpose, document.Subject, priority, document.Privacy.Name, document.CreatedDate.ToString());
                                }
                            }
                        }
                    }
                }
                catch (System.Exception ex)
                {
                    ExceptionLogger.WriteEntry(ex.ToString());
                }
            });
        }

        public static void Reply(this IHubContext<CommunicationHub> hub, long documentId, Language language)
        {
            System.Threading.Tasks.Task.Run(() =>
            {
                try
                {
                    var document = new Document().FindIncludeTransfers(documentId);
                    var transfer = document.Transfer.LastOrDefault();
                    if (transfer != null)
                    {
                        var nodeId = RetrieveNodeIdToNotification(transfer);
                        var referenceNumber = transfer.Document.ReferenceNumber;
                        var subject = transfer.Document.Subject;
                        var maxSubjectLength = 30;
                        var trimmedSubject = subject?.Length > maxSubjectLength
                            ? subject.Substring(0, maxSubjectLength) + "..."
                            : subject;

                        string separator = "\n";
                        List<string> lines = new List<string>();

                        if (!string.IsNullOrEmpty(referenceNumber))
                        {
                            lines.Add($"{TranslationUtility.Translate("ReferenceNumber", language)}: {referenceNumber}.");
                        }

                        if (!string.IsNullOrEmpty(trimmedSubject))
                        {
                            lines.Add($"{TranslationUtility.Translate("Subject", language)}: {trimmedSubject}.");
                        }

                        string refAndSubjectPart = string.Join(separator, lines);

                        var message = string.Format(TranslationUtility.Translate("NotificationMsg", language), refAndSubjectPart).Replace("\\n", separator);

                        if (transfer.ToUserId != null)
                        {
                            hub.Clients.User(transfer.ToUserId.ToString()).SendAsync("ReceiveMessage", new
                            {
                                message = message,
                                data = new
                                {
                                    transferId = transfer.Id,
                                    nodeId = nodeId.ToString()
                                },
                            });

                        }
                        else
                        {
                            if (transfer.ToStructureId != null)
                            {
                                hub.Clients.Group($"S-{transfer.ToStructureId}-{document.Privacy?.Level}").SendAsync("ReceiveMessage", new
                                {
                                    message = message,
                                    data = new
                                    {
                                        transferId = transfer.Id,
                                        nodeId = nodeId.ToString()
                                    },
                                });

                            }
                        }
                        if (Configuration.EnableEmailNotification)
                        {
                            if (transfer.ToUserId == null)
                            {
                                var structureUsers = IdentityHelper.GetStructureAllUsers(transfer.ToStructureId.Value, Configuration.IdentityAccessToken);
                                if (!structureUsers.IsNullOrEmpty())
                                {
                                    foreach (var user in structureUsers)
                                    {
                                        if (user.Attributes.Where(t => t.Text == Configuration.UserStructureReceiver).Select(t => Convert.ToBoolean(t.Value)).FirstOrDefault() == true)
                                        {
                                            var userPrivacy = user.Attributes.Where(t => t.Text == Configuration.UserPrivacy).Select(t => Convert.ToInt16(t.Value)).FirstOrDefault();
                                            var userPrivacyLevel = !userPrivacy.IsNull() ? ManagePrivacy.CheckPrivacyLevel(userPrivacy) : ManagePrivacy.GetLowestLevel();
                                            
                                            if (userPrivacyLevel >= document.Privacy?.Level)
                                            {
                                                var purpose = ManagePurpose.FindWithCaching((short)transfer.PurposeId).Name;
                                                var priority = ManagePriority.FindWithCaching((short)document.PriorityId).Name;
                                                string senderUser = IdentityHelper.GetUser(transfer.FromUserId.Value, Configuration.IdentityAccessToken).FirstName + " " + IdentityHelper.GetUser(transfer.FromUserId.Value, Configuration.IdentityAccessToken).LastName;
                                                SendOnTransferEmail(user.Email, document.ReferenceNumber, transfer.Id, user.FirstName + " " + user.LastName, senderUser , document.SendingEntity.Name, purpose, document.Subject, priority, document.Privacy.Name, document.CreatedDate.ToString());
                                            }
                                        }
                                    }
                                }
                            }
                            else
                            {
                                
                                var user = IdentityHelper.GetUser(transfer.ToUserId.Value, Configuration.IdentityAccessToken);
                                var purpose = ManagePurpose.FindWithCaching((short)transfer.PurposeId).Name;
                                var priority = ManagePriority.FindWithCaching((short)document.PriorityId).Name;
                                string senderUser = IdentityHelper.GetUser(transfer.FromUserId.Value, Configuration.IdentityAccessToken).FirstName + " " + IdentityHelper.GetUser(transfer.FromUserId.Value, Configuration.IdentityAccessToken).LastName;
                                SendOnTransferEmail(user.Email, document.ReferenceNumber, transfer.Id, user.FirstName + " " + user.LastName, senderUser, document.SendingEntity.Name, purpose, document.Subject, priority, document.Privacy.Name, document.CreatedDate.ToString());
                            }
                        }
                    }
                }
                catch (System.Exception ex)
                {
                    ExceptionLogger.WriteEntry(ex.ToString());
                }
            });
        }

        public static void Transfer(this IHubContext<CommunicationHub> hub, long transferId, Language language)
        {
            System.Threading.Tasks.Task.Run(() =>
            {
                try
                {
                    var transfers = new DAL.Transfer().FindChildWithDocument(transferId);
                    if (!transfers.IsNullOrEmpty())
                    {
                        foreach (var transfer in transfers)
                        {
                            var nodeId = RetrieveNodeIdToNotification(transfer);
                            var referenceNumber = transfer.Document.ReferenceNumber;
                            var subject = transfer.Document.Subject;
                            var maxSubjectLength = 30;
                            var trimmedSubject = subject?.Length > maxSubjectLength
                                ? subject.Substring(0, maxSubjectLength) + "..."
                                : subject;

                            string separator = "\n";
                            List<string> lines = new List<string>();

                            if (!string.IsNullOrEmpty(referenceNumber))
                            {
                                lines.Add($"{TranslationUtility.Translate("ReferenceNumber", language)}: {referenceNumber}.");
                            }

                            if (!string.IsNullOrEmpty(trimmedSubject))
                            {
                                lines.Add($"{TranslationUtility.Translate("Subject", language)}: {trimmedSubject}.");
                            }

                            string refAndSubjectPart = string.Join(separator, lines);

                            var message = string.Format(TranslationUtility.Translate("NotificationMsg", language), refAndSubjectPart).Replace("\\n", separator);


                            if (transfer.ToUserId != null)
                            {
                                hub.Clients.User(transfer.ToUserId.ToString()).SendAsync("ReceiveMessage", new
                                {
                                    message = message,
                                    data = new
                                    {
                                        transferId = transfer.Id,
                                        nodeId = nodeId.ToString()
                                    },
                                });

                            }
                            else
                            {
                                if (transfer.ToStructureId != null)
                                {
                                    hub.Clients.Group($"S-{transfer.ToStructureId}-{transfer.Document.Privacy?.Level}").SendAsync("ReceiveMessage", new
                                    {
                                        message = message,
                                        data = new
                                        {
                                            transferId = transfer.Id,
                                            nodeId =nodeId.ToString()
                                        },
                                    });

                                }
                            }
                        }
                        if (Configuration.EnableEmailNotification)
                        {
                            foreach (var transfer in transfers)
                            {
                                if (transfer.ToUserId == null)
                                {
                                    var structureUsers = IdentityHelper.GetStructureAllUsers(transfer.ToStructureId.Value, Configuration.IdentityAccessToken);
                                    if (!structureUsers.IsNullOrEmpty())
                                    {
                                        foreach (var user in structureUsers)
                                        {
                                            string senderUser = IdentityHelper.GetUser(transfer.FromUserId.Value, Configuration.IdentityAccessToken).FirstName + " " + IdentityHelper.GetUser(transfer.FromUserId.Value, Configuration.IdentityAccessToken).LastName;

                                            var purpose = ManagePurpose.FindWithCaching((short)transfer.PurposeId).Name;
                                            var priority = ManagePriority.FindWithCaching((short)transfer.Document.PriorityId).Name;
                                            SendOnTransferEmail(user.Email, transfer.Document.ReferenceNumber, transfer.Id, user.FirstName + " " + user.LastName, senderUser, transfer.Document.SendingEntity.Name, purpose, transfer.Document.Subject, priority, transfer.Document.Privacy.Name, transfer.Document.CreatedDate.ToString());
                                        }
                                    }
                                }
                                else
                                {
                                    var user = IdentityHelper.GetUser(transfer.ToUserId.Value, Configuration.IdentityAccessToken);

                                    var purpose = ManagePurpose.FindWithCaching((short)transfer.PurposeId).Name;
                                    var priority = ManagePriority.FindWithCaching((short)transfer.Document.PriorityId).Name;
                                    string senderUser = IdentityHelper.GetUser(transfer.FromUserId.Value, Configuration.IdentityAccessToken).FirstName + " " + IdentityHelper.GetUser(transfer.FromUserId.Value, Configuration.IdentityAccessToken).LastName;
                                    SendOnTransferEmail(user.Email, transfer.Document.ReferenceNumber, transfer.Id, user.FirstName + " " + user.LastName, senderUser, transfer.Document?.SendingEntity?.Name, purpose, transfer.Document?.Subject, priority, transfer.Document?.Privacy?.Name, transfer.Document?.CreatedDate.ToString());
                                }
                            }
                        }
                    }
                }
                catch (System.Exception ex)
                {
                    ExceptionLogger.WriteEntry(ex.ToString());
                }
            });
        }

        public static void Complete(long transferId)
        {
            System.Threading.Tasks.Task.Run(() =>
            {
                try
                {
                    var transfer = new Transfer().FindIncludeDocument(transferId);
                    if (transfer != null)
                    {
                        SendOnCompleteEmail(transfer);
                    }
                }
                catch (System.Exception ex)
                {
                    ExceptionLogger.WriteEntry(ex.ToString());
                }
            });
        }

        public static void CompleteFollowUp(long transferId)
        {
            System.Threading.Tasks.Task.Run(() =>
            {
                try
                {
                    var transfer = new Transfer().FindIncludeDocument(transferId);
                    var assignees = ManageAssignee.FindByDocumentId(transfer.DocumentId.Value);
                    
                    foreach (var assignee in assignees)
                    {
                        assignee.Document = transfer.Document;
                        SendOnFollowupCompleteEmail(assignee);
                    }
                    if (transfer != null)
                    {
                        
                    }
                }
                catch (System.Exception ex)
                {
                    ExceptionLogger.WriteEntry(ex.ToString());
                }
            });
        }

        public static void CompleteDocument(long documentId)
        {
            System.Threading.Tasks.Task.Run(() =>
            {
                try
                {
                    var transfers = new Transfer().ListByDocumentIdIncludeDocument(documentId);
                    foreach (var transfer in transfers)
                    {
                        if (transfer != null)
                        {
                            SendOnCompleteEmail(transfer);
                        }
                    }
                }
                catch (System.Exception ex)
                {
                    ExceptionLogger.WriteEntry(ex.ToString());
                }
            });
        }

        public static void Recall(this IHubContext<CommunicationHub> hub, long transferId, Language language)
        {
            System.Threading.Tasks.Task.Run(() =>
            {
                try
                {
                    var transfer = new DAL.Transfer().FindIncludeDocumentAndPrivacyandSendingEntity(transferId);
                    if (transfer!=null)
                    {
                        //foreach (var transfer in transfers)
                        //{
                        var nodeId = RetrieveNodeIdToNotification(transfer);

                        var referenceNumber = transfer.Document.ReferenceNumber;
                        var subject = transfer.Document.Subject;
                        var maxSubjectLength = 30;
                        var trimmedSubject = subject?.Length > maxSubjectLength
                            ? subject.Substring(0, maxSubjectLength) + "..."
                            : subject;


                        string separator = "\n";
                        List<string> lines = new List<string>();

                        if (!string.IsNullOrEmpty(referenceNumber))
                        {
                            lines.Add($"{TranslationUtility.Translate("ReferenceNumber", language)}: {referenceNumber}.");
                        }

                        if (!string.IsNullOrEmpty(trimmedSubject))
                        {
                            lines.Add($"{TranslationUtility.Translate("Subject", language)}: {trimmedSubject}.");
                        }

                        string refAndSubjectPart = string.Join(separator, lines);

                        var message = string.Format(TranslationUtility.Translate("NotificationMsg", language), refAndSubjectPart).Replace("\\n", separator);

                        if (transfer.ToUserId != null)
                        {
                            hub.Clients.User(transfer.ToUserId.ToString()).SendAsync("ReceiveMessage", new
                            {
                                message = message,
                                data = new
                                {
                                    transferId = transfer.Id,
                                    nodeId = nodeId.ToString()
                                },
                            });

                        }
                        else
                        {
                            if (transfer.ToStructureId != null)
                            {
                                hub.Clients.Group($"S-{transfer.ToStructureId}-{transfer.Document.Privacy?.Level}").SendAsync("ReceiveMessage", new
                                {
                                    message = message,
                                    data = new
                                    {
                                        transferId = transfer.Id,
                                        nodeId = nodeId.ToString()
                                    },
                                });

                            }
                        }
                        //}
                        if (Configuration.EnableEmailNotification)
                        {
                            if (transfer.ToUserId == null)
                            {
                                var structureUsers = IdentityHelper.GetStructureAllUsers(transfer.ToStructureId.Value, Configuration.IdentityAccessToken);
                                if (!structureUsers.IsNullOrEmpty())
                                {
                                    foreach (var user in structureUsers)
                                    {
                                        string senderUser = IdentityHelper.GetUser(transfer.FromUserId.Value, Configuration.IdentityAccessToken).FirstName + " " + IdentityHelper.GetUser(transfer.FromUserId.Value, Configuration.IdentityAccessToken).LastName;

                                        var purpose = ManagePurpose.FindWithCaching((short)transfer.PurposeId).Name;
                                        var priority = ManagePriority.FindWithCaching((short)transfer.Document.PriorityId).Name;
                                        SendOnRecallTransferEmail(user.Email, transfer.Document.ReferenceNumber, transfer.Id, user.FirstName + " " + user.LastName, senderUser, transfer.Document.SendingEntity.Name, purpose, transfer.Document.Subject, priority, transfer.Document.Privacy.Name, transfer.Document.CreatedDate.ToString());
                                    }
                                }
                            }
                            else
                            {
                                var user = IdentityHelper.GetUser(transfer.ToUserId.Value, Configuration.IdentityAccessToken);

                                var purpose = ManagePurpose.FindWithCaching((short)transfer.PurposeId).Name;
                                var priority = ManagePriority.FindWithCaching((short)transfer.Document.PriorityId).Name;
                                string senderUser = IdentityHelper.GetUser(transfer.FromUserId.Value, Configuration.IdentityAccessToken).FirstName + " " + IdentityHelper.GetUser(transfer.FromUserId.Value, Configuration.IdentityAccessToken).LastName;
                                SendOnRecallTransferEmail(user.Email, transfer.Document.ReferenceNumber, transfer.Id, user.FirstName + " " + user.LastName, senderUser, transfer.Document?.SendingEntity?.Name, purpose, transfer.Document?.Subject, priority, transfer.Document?.Privacy?.Name, transfer.Document?.CreatedDate.ToString());
                            }
                        }
                    }
                }
                catch (System.Exception ex)
                {
                    ExceptionLogger.WriteEntry(ex.ToString());
                }
            });
        }


        #endregion

        #region Private Methods

        private static void SendOnTransferEmail(string email, string referenceNumber, long transferId , string receiverName, string senderName,string sendingEntity, string purpose, string subject, string priority, string privacy, string dateRegistered )
        {
            Dictionary<string, string> bookmark = new Dictionary<string, string>();
            var url = string.Format("{0}/#inboxDocumentDetails/{1}", Configuration.WebsiteUrl, transferId);
            bookmark.Add("[ReferenceNumber]", referenceNumber);
            bookmark.Add("[URL]", url);
            bookmark.Add("[ReceiverName]", receiverName);
            bookmark.Add("[SendingEntity]", sendingEntity);
            bookmark.Add("[Purpose]", purpose);
            bookmark.Add("[Subject]",subject);
            bookmark.Add("[Privacy]", privacy);
            bookmark.Add("[Priority]", priority);
            bookmark.Add("[CreatedDate]", dateRegistered);
            bookmark.Add("[SenderName]", senderName);
            Intalio.Core.API.ManageNotificationTemplate.Send(email, NotificationTemplateName.OnTransfer.ToString(), bookmark);
        }

        private static void SendOnCompleteEmail(Transfer transfer)
        {
            if (transfer.ToUserId == null)
            {
                var structureUsers = IdentityHelper.GetStructureAllUsers(transfer.ToStructureId.Value, Configuration.IdentityAccessToken);
                if (!structureUsers.IsNullOrEmpty())
                {
                    foreach (var user in structureUsers)
                    {
                        Dictionary<string, string> bookmark = new Dictionary<string, string>();
                        bookmark.Add("[ReferenceNumber]", transfer.Document.ReferenceNumber);
                        bookmark.Add("[ReceiverName]", user.FirstName + " " + user.LastName);
                        ManageNotificationTemplate.Send(user.Email, NotificationTemplateName.OnComplete.ToString(), bookmark);
                    }
                }
            }
            else
            {
                var user = IdentityHelper.GetUser(transfer.ToUserId.Value, Configuration.IdentityAccessToken);
                Dictionary<string, string> bookmark = new Dictionary<string, string>();
                bookmark.Add("[ReferenceNumber]", transfer.Document.ReferenceNumber);
                bookmark.Add("[ReceiverName]", user.FirstName + " " + user.LastName);
                ManageNotificationTemplate.Send(user.Email, NotificationTemplateName.OnComplete.ToString(), bookmark);
            }
        }

        private static void SendOnFollowupCompleteEmail(Assignee assignee)
        {
            var assignedToUser = Intalio.Core.API.IdentityHelper.GetUser(assignee.UserId, Configuration.IdentityAccessToken);
            var assignedTo = assignedToUser.FirstName + " " + assignedToUser.LastName;
            Dictionary<string, string> bookmark = new Dictionary<string, string>();

            bookmark.Add("[FollowUpSubject]", assignee.Document.Subject);
            bookmark.Add("[AssignedTo]", assignedTo);
            bookmark.Add("[DueDate]", assignee.Document.DueDate.ToString());

            Intalio.Core.API.ManageNotificationTemplate.Send(assignedToUser.Email, NotificationTemplateName.OnFollowUpCompleted.ToString(), bookmark);
        }

        private static int RetrieveNodeIdToNotification(Transfer transfer)
        {
            var inboxNodes = new Intalio.Core.DAL.Node().ListChildrenByInheritName(nameof(CustomNodes.Inbox));
            var sortedInboxNodes = inboxNodes.OrderBy(n => n.Id);
            foreach (var node in sortedInboxNodes)
            {
                var result = ManageNode.GetNodeConditionsInExpressionBuilder(node.Id);
                if (result.Filters.Any(x => x.PropertyName == "ToUserId") && transfer.ToUserId.HasValue)
                {
                    result.Filters.FirstOrDefault(x => x.PropertyName == "ToUserId").Value = transfer.ToUserId;
                }
                var filterExp = ExpressionBuilder.GetExpression<Transfer>(result.Filters, ExpressionBuilderOperator.And);
                if (filterExp == null)
                {
                    continue;
                }
                var compiledExpression = filterExp.Compile();
                if (compiledExpression(transfer))
                {
                    return node.Id; 
                }
            }
            return 0;
        }

        private static void SendOnRecallTransferEmail(string email, string referenceNumber, long transferId, string receiverName, string senderName, string sendingEntity, string purpose, string subject, string priority, string privacy, string dateRegistered)
        {
            Dictionary<string, string> bookmark = new Dictionary<string, string>();
            var url = string.Format("{0}/#inboxDocumentDetails/{1}", Configuration.WebsiteUrl, transferId);
            bookmark.Add("[ReferenceNumber]", referenceNumber);
            bookmark.Add("[URL]", url);
            bookmark.Add("[ReceiverName]", receiverName);
            bookmark.Add("[SendingEntity]", sendingEntity);
            bookmark.Add("[Purpose]", purpose);
            bookmark.Add("[Subject]", subject);
            bookmark.Add("[Privacy]", privacy);
            bookmark.Add("[Priority]", priority);
            bookmark.Add("[CreatedDate]", dateRegistered);
            bookmark.Add("[SenderName]", senderName);
            Intalio.Core.API.ManageNotificationTemplate.Send(email, NotificationTemplateName.OnRecall.ToString(), bookmark);
        }

        #endregion
    }
}
