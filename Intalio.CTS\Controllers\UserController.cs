﻿using Intalio.IAM.Core.API;
using Intalio.IAM.Core.Model;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Linq;
using System.Net;
using Intalio.IAM.Core.DAL;
using System.Collections.Generic;
using Intalio.IAM.Core;
using LinqKit;
using Intalio.CTS.Filters;
using Microsoft.EntityFrameworkCore.Metadata;
using System.Threading.Tasks;

namespace Intalio.CTS.Controllers
{
    /// <summary>
    /// To make Crud Operation on User
    /// </summary>
    [Route("[controller]/[action]")]
    [ApiExplorerSettings(IgnoreApi = true)]
    public class UserController : BaseController
    {
        /// <summary>
        /// Check User Name Repeated
        /// </summary>
        /// <param name="username"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult CheckUserName(string username, long id)
        {
            try
            {
                return Ok(ManageUser.CheckUserName(username, id));
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }
        /// <summary>
        /// Check User Name Repeated
        /// </summary>
        /// <param name="username"></param>
        /// <param name="id"></param>
        /// <param name="providerId"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult GetADUser(string username, long id, long providerId)
        {
            UserViewModel userViewModel = new UserViewModel();
            try
            {
                if (!ManageUser.CheckUserName(username, id))
                {
                    userViewModel = ManageUser.Fetch(username, providerId);
                }
                else
                {
                    userViewModel.Username = username;
                }

                return Ok(userViewModel);
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }
        /// <summary>
        /// Get List Structures
        /// </summary>
        /// <param name="term"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult GetListStructures(string term)
        {
            try
            {
                return StatusCode(200, ManageStructure.SearchStructure(term));
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }
        /// <summary>
        /// GetUserListManagers
        /// </summary>
        /// <param name="term"></param>
        /// <param name="userId"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult GetUserListManagers(long userId, string term)
        {
            try
            {
                return StatusCode(200, ManageUser.SearchWithoutCurrentUser(userId, term));
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }
        /// <summary>
        /// GetUserListManagers
        /// </summary>
        /// <param name="term"></param>
        /// <returns></returns>
        [HttpPost]
        public IActionResult GetListGroups(string term)
        {
            try
            {
                return StatusCode(200, ManageGroup.Search(term));
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }
        /// <summary>
        /// ListLoginProviders
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        public IActionResult ListLoginProviders()
        {
            try
            {
                return StatusCode(200, ManageLoginProvider.List());
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }
        /// <summary>
        /// GetProfilePicture
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetProfilePicture(long attachmentId)
        {
            try
            {
                return Ok(ManageAttachment.GetAttachmentData(attachmentId)?.Data);
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        /// <summary>
        /// GetProfilePicture
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IActionResult GetCurrentUserProfilePicture()
        {
            try
            {
                var profile = ManageUser.FindProfileWithInclude(UserId).PhotoAttachmentId;
                if(profile is null)
                {
                    return Redirect("/images/default-user-image.png");
                }
                return File(ManageAttachment.GetAttachmentData(profile.Value)?.Data, "image/png");
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        [HttpGet]
        [CustomMenusAuthorizationFilter(new string[] {nameof(Core.CustomMenus.SecureUserId) })] 
        public JsonResult GetUsersWithSecureUserId(int draw, int start, int length, string name, string userId)
        {
            (long count, List<UserProfileModel> items) = ManageUser.ListWithSearch(
                startIndex: start,
                pageSize: length,
                clientId: Core.Configuration.IdentityClientId,
                search: name,
                showOnlyActiveUsers: true, language: (Intalio.IAM.Core.Language)Language);

            if (!string.IsNullOrEmpty(userId))
            {
                items = items
                    .Where(u => u.Attributes
                    .FirstOrDefault(a => a.Text == "SecureUserId")?.Value?
                    .Contains(userId, StringComparison.OrdinalIgnoreCase) == true)
                    .ToList();
                count = items.Count;
            }

            var shaped = items?.Select(e => new {
                e.Id,
                e.FirstName,
                e.MiddleName,
                e.LastName,
                SecureUserId = e.Attributes.FirstOrDefault(e => e.Text == "SecureUserId")?.Value ?? null
            });
            return Json(new {
                draw,
                recordsTotal = count,
                recordsFiltered = count,
                data = shaped
            });
        }

        [HttpPost]
        [CustomMenusAuthorizationFilter(new string[] {nameof(Core.CustomMenus.SecureUserId) })] 
        public IActionResult UpdateUserSecureId(int userId, string newSecureUserId)
        {
            UserViewModel model = new UserViewModel();
            try
            {
                if (userId != 0L)
                {
                    model = ManageUser.FindWithInclude(userId);
                }
                if (model == null)
                {
                    return StatusCode((int)HttpStatusCode.Found, null);
                }
                var Roles = ManageRole.ListAllRoles();
                string Role = Roles.FirstOrDefault(x => x.Id == RoleId).Name;
        
                Result result = new Result();
                int statusCode = 400;
                long id = model.Id.Value;
                AttributeErrorSummary error;
                if (!ManageUser.CheckUserApplicationLicense(id))
                {
                    result.Message = "LimitsExceeded";
                    return StatusCode(400, result);
                }
                var user = ManageUser.FindWithInclude(model.Id.Value);
                model.ApplicationsRoles = (!user.ApplicationsRolesList.IsNullOrEmpty()) ? user.ApplicationsRolesList.Select(x => new UserApplicationRoleViewModel()
                {
                    Id = x.Id,
                    ApplicationId = x.Application.Id,
                    RoleId = (short)x.Role.Id,
                    UserTypeId = x.UserTypeId
                }).ToList() : null;
                model.Structures = user.StructuresList.Select(x => x.Id).ToList();
        
                var attributeModels = ManageUserAttribute.ListUserAttribute(Role);
                var secureUserIdAttr = attributeModels.FirstOrDefault(e => e.Name == "SecureUserId");
                var attr = user.AttributesValues.FirstOrDefault(e => e.AttributeProperties.Id == secureUserIdAttr.Id);
        
                if(attr is null)
                {
                    return StatusCode((int)HttpStatusCode.Found, null);
                }
                attr.AttributeProperties.Value = newSecureUserId;
                model.GroupsList = user.GroupsList;
                model.UserStructureAttributeListValues = user.UserStructureAttributesValues;
                model.ObjectAttributeListValues = user.AttributesValues;
                model.StructureId = user.DefaultStructure?.Id;
                model.Username = user.Username;
                model.Password = null;
                error = ManageUser.Edit(UserId, Role, model, (Language)Language);
                statusCode = 200;
        
                if (error.Message == "LimitsExceeded")
                {
                    result.Message = "LimitsExceeded";
                    return StatusCode(statusCode, result);
                }
                if ((error?.AttributeValidationErrors?.Count ?? 0) > 0)
                {
                    return StatusCode(statusCode, error);
                }
        
                return StatusCode(statusCode);
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        /// <summary>
        /// GetUserData
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [CustomMenusAuthorizationFilter(new string[] {nameof(Core.CustomMenus.OrganizationManagement), nameof(Core.CustomMenus.ManageDepartmentUsers) })] 
        public IActionResult GetUserData(long id,long structureId)
        {
            UserViewModel vm = new UserViewModel();
            CTS.Core.Model.CTSUserViewModel model;
            try
            {
                if (id != 0L)
                {
                    vm = ManageUser.FindWithInclude(id);
                }
                if (vm == null)
                    return StatusCode((int)HttpStatusCode.Found, null);

                model = new CTS.Core.Model.CTSUserViewModel(vm);
                var Roles = ManageRole.ListAllRoles();
                string Role = Roles.FirstOrDefault(x => x.Id == RoleId).Name;
                model.AttributeModels = ManageUserAttribute.ListUserAttribute(Role);
                List<string> Attributes = new List<string>() { "Roles", "StructureSender", "StructureReceiver", "Privacy", "AllowEditSigned" , "EditDesignatedPerson" };
                model.StructureAttributes = ManageUserAttribute.ListStrcutureAttribute().Where(x=>Attributes.Contains(x.Name)).Select(t =>
                {
                    t.Group = null;
                    return t;
                }).ToList();
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
            return Ok(model);
        }
        /// <summary>
        /// PostUserData
        /// </summary>
        /// <returns></returns>
        /// 
        [HttpPost]
        public IActionResult PostUserData(UserViewModel model,long editedStructureId)
        {
            try
            {
                var Roles = ManageRole.ListAllRoles();
                string Role = Roles.FirstOrDefault(x => x.Id == RoleId).Name;

                Result result = new Result();
                int statusCode = 400;
                AttributeErrorSummary attributeErrorSummary1 = new AttributeErrorSummary();
                if (ModelState.IsValid)
                {
                    //if (!AllStructureIds.Contains(editedStructureId))
                    //{
                    //    return StatusCode((int)HttpStatusCode.Unauthorized);
                    //}
                    List<AttachmentViewModel> attachmentViewModelList = new List<AttachmentViewModel>();
                    if (Request.Form.Files != null && Request.Form.Files.Count > 0)
                    {
                        List<IFormFile> list = Request.Form.Files.ToList<IFormFile>();
                        for (int index = 0; index < list.Count; ++index)
                        {
                            IFormFile formFile = list[index];
                            if (formFile.Name == "Logo[0]")
                                model.Logo = new AttachmentViewModel()
                                {
                                    Name = Helper.GetFileName(formFile.FileName),
                                    FileSize = formFile.Length,
                                    ContentType = formFile.ContentType,
                                    Data = formFile.OpenReadStream().StreamToByte(),
                                    Extension = Helper.GetFileExtension(formFile.FileName).ToLower()
                                };

                        }
                    }
                    if (!model.ApplicationsRoles.IsNullOrEmpty<UserApplicationRoleViewModel>())
                    {
                        foreach (UserApplicationRoleViewModel applicationsRole in model.ApplicationsRoles)
                        {
                            if (!ManageApplication.CheckAppPermission(model.Id, applicationsRole.ApplicationId, applicationsRole.UserTypeId))
                            {
                                result.Message = "LimitsExceeded";
                                return StatusCode(200, result);
                            }
                        }
                    }
                    if (!model.ObjectAttributeListValues.IsNullOrEmpty<ObjectAttributeListValue>())
                    {
                        System.Collections.Generic.List<int> list = ManageUserAttribute.ListUserAttribute(Role).Select<AttributeModel, int>((Func<AttributeModel, int>)(t => t.Id)).ToList<int>();
                        if (model.ObjectAttributeListValues.Count > list.Count)
                        {
                            result.Message = "CorruptedAttributes";
                            return StatusCode(200, result);
                        }
                        foreach (ObjectAttributeListValue attributeListValue in model.ObjectAttributeListValues)
                        {
                            if (!list.Contains(attributeListValue.AttributeProperties.Id))
                            {
                                result.Message = "CorruptedAttributes";
                                return StatusCode(200, result);
                            }
                        }
                    }
                    long? id = model.Id;
                    AttributeErrorSummary attributeErrorSummary2;
                    if (id.HasValue)
                    {
                        int num;
                        if (model.Active)
                        {
                            id = model.Id;
                            num = !ManageUser.CheckUserApplicationLicense(id.Value) ? 1 : 0;
                        }
                        else
                            num = 0;
                        if (num != 0)
                        {
                            result.Message = "LimitsExceeded";
                            return StatusCode(200, result);
                        }
                        var user = ManageUser.FindWithInclude(model.Id.Value);
                        model.ApplicationsRoles = (!user.ApplicationsRolesList.IsNullOrEmpty()) ? user.ApplicationsRolesList.Select(x => new UserApplicationRoleViewModel()
                        {
                            Id = x.Id,
                            ApplicationId = x.Application.Id,
                            RoleId = (short)x.Role.Id,
                            UserTypeId = x.UserTypeId
                        }).ToList() : null;
                        model.Structures = user.StructuresList.Select(x => x.Id).ToList();
                        if (user.DefaultStructure != null && user.DefaultStructure.Id == editedStructureId)
                        {
                            foreach (var attribute in model.UserStructureAttributeListValues)
                            {
                                var editedAttribute = user.AttributesValues.Where(x => x.ObjectAttributeValueId == attribute.ObjectAttributeValueId).FirstOrDefault();
                                if (editedAttribute != null)
                                {
                                    editedAttribute.AttributeProperties.Value = attribute.AttributeProperties.Value;
                                }
                            }
                        }
                        else
                        {
                            foreach (var attribute in model.UserStructureAttributeListValues)
                            {
                                var editedAttribute = user.UserStructureAttributesValues.Where(x => x.ObjectAttributeValueId == attribute.ObjectAttributeValueId).FirstOrDefault();
                                if (editedAttribute != null)
                                {
                                    editedAttribute.AttributeProperties.Value = attribute.AttributeProperties.Value;
                                }
                            }
                        }
                        model.UserStructureAttributeListValues = user.UserStructureAttributesValues;
                        model.ObjectAttributeListValues = user.AttributesValues;
                        model.StructureId = user.DefaultStructure?.Id;
                        model.Username = user.Username;
                        attributeErrorSummary2 = ManageUser.Edit(UserId, Role, model, (Language)Language);
                        statusCode = 200;
                    }
                    else
                    {
                        model.ApplicationsRoles = new List<UserApplicationRoleViewModel>();
                        short.TryParse(Intalio.CTS.Core.API.ManageParameter.FindByKeyWord("CTSApplicationId")?.Content, out short CtsAppId);
                        short.TryParse(Intalio.CTS.Core.API.ManageParameter.FindByKeyWord("CTSDefaultRoleId")?.Content, out short CtsRoleId);
                        short.TryParse(Intalio.CTS.Core.API.ManageParameter.FindByKeyWord("ViewerApplicationId")?.Content, out short ViewerApp);
                        short.TryParse(Intalio.CTS.Core.API.ManageParameter.FindByKeyWord("ViewerDefaultRoleId")?.Content, out short ViewerRole);
                        if (CtsAppId == 0 || CtsRoleId == 0 || ViewerRole == 0 || ViewerApp == 0)
                        {
                            result.Message = "ctsAndViewerAppAndRoleIdInParameters";
                            return StatusCode(200, result);
                        }
                        model.ApplicationsRoles.Add(new UserApplicationRoleViewModel()
                        {
                            ApplicationId = CtsAppId,
                            RoleId = CtsRoleId,
                            UserTypeId = (byte)UserTypes.Editor,

                        });
                        model.ApplicationsRoles.Add(new UserApplicationRoleViewModel()
                        {
                            ApplicationId = ViewerApp,
                            RoleId = ViewerRole,
                            UserTypeId = (byte)UserTypes.Editor,
                        });
                        model.SystemRoleId = (byte)SystemRole.User;
                        model.ObjectAttributeListValues = model.UserStructureAttributeListValues.Select(x=>new ObjectAttributeListValue()
                        {
                            AttributeProperties = x.AttributeProperties,
                            ObjectAttributeValueId = x.ObjectAttributeValueId
                        }).ToList();
                        model.UserStructureAttributeListValues = null;
                        attributeErrorSummary2 = ManageUser.Create(UserId, model, (Language)Language);
                        statusCode = 200;
                    }
                    if (attributeErrorSummary2.Message == "LimitsExceeded")
                    {
                        result.Message = "LimitsExceeded";
                        return StatusCode(statusCode, result);
                    }
                    if (attributeErrorSummary2.EmailAlreadyExists)
                    {
                        result.Message = "EmailAlreadyExists";
                        return StatusCode(statusCode, result);
                    }
                    if (attributeErrorSummary2.UserNameAlreadyExists)
                    {
                        result.Message = "UserNameAlreadyExists";
                        return StatusCode(statusCode, result);
                    }
                    System.Collections.Generic.List<AttributeUniqueError> attributeUniqueErrors = attributeErrorSummary2.AttributeUniqueErrors;
                    int num1;
                    // ISSUE: explicit non-virtual call
                    if ((attributeUniqueErrors != null ? ((attributeUniqueErrors.Count) > 0 ? 1 : 0) : 0) == 0)
                    {
                        System.Collections.Generic.List<AttributeValidationError> validationErrors = attributeErrorSummary2.AttributeValidationErrors;
                        // ISSUE: explicit non-virtual call
                        num1 = validationErrors != null ? ((validationErrors.Count) > 0 ? 1 : 0) : 0;
                    }
                    else
                        num1 = 1;
                    if (num1 != 0)
                        return StatusCode(statusCode, attributeErrorSummary2);
                }
                Intalio.Core.API.ManageUser.Provision(new Intalio.Core.Model.UserViewModel
                {
                    Id = Convert.ToInt32(model.Id),
                    Firstname = model.FirstName,
                    Lastname = model.LastName,
                    RoleId = model.SystemRoleId
                });

                if (Core.API.ManageUserStructure.IsExistUser(model.Id.Value, model.StructureId.Value))
                {
                    Core.API.ManageUser.UpdateLoggedInStructure(model.Id.Value, model.StructureId.Value, true);
                }
                else
                {
                    Core.API.ManageUser.InsertLoggedInStructure(model.Id.Value, model.StructureId.Value, true);
                }
                return StatusCode(statusCode, model);
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }
        /// <summary>
        /// GetStructureUser
        /// </summary>
        /// <returns></returns>
        /// 
        [HttpGet]
        public IActionResult GetStructureUser(long StructureId)
        {
            var allUsers = ManageUser.Search("");
            allUsers.ForEach(x =>
            {
                var user = ManageUser.FindWithInclude(x.Id);
                if (user != null)
                {
                    if (user.StructureId == StructureId || user.Structures.Contains(StructureId))
                    {
                        allUsers.Remove(x);
                    }
                }
            });
            return Ok(new { users = allUsers });
        }
        /// <summary>
        /// AddExistUsers
        /// </summary>
        /// <returns></returns>
        /// 
        [HttpPost]
        public IActionResult AddExistUsers(long StructureId, List<long> UserIds)
        {
            AttributeErrorSummary attributeErrorSummary = new AttributeErrorSummary();
            var Roles = ManageRole.ListAllRoles();
            string Role = Roles.FirstOrDefault(x => x.Id == RoleId).Name;
            //if (!AllStructureIds.Contains(StructureId))
            //{
            //    return StatusCode((int)HttpStatusCode.Unauthorized);
            //}
            foreach (long UserId in UserIds)
            {
                var user = ManageUser.FindWithInclude(UserId);
                user.Groups = user.GroupsList.Select(x => (short)x.Id).ToList();
                user.ApplicationsRoles = (!user.ApplicationsRolesList.IsNullOrEmpty()) ? user.ApplicationsRolesList.Select(x => new UserApplicationRoleViewModel()
                {
                    Id = x.Id,
                    ApplicationId = x.Application.Id,
                    RoleId = (short)x.Role.Id,
                    UserTypeId = x.UserTypeId
                }).ToList() : null;
                user.Structures = user.StructuresList.Select(x => x.Id).ToList();
                if (!user.Structures.Contains(StructureId))
                {
                    user.Structures.Add(StructureId);
                }
                List<string> Attributes = new List<string>() { "Roles", "StructureSender", "StructureReceiver", "Privacy", "AllowEditSigned", "EditDesignatedPerson" };
                user.AttributeModels = ManageUserAttribute.ListStrcutureAttribute().Where(x => Attributes.Contains(x.Name)).Select(t =>
                {
                    t.Group = null;
                    return t;
                }).ToList();
                user.UserStructureAttributeListValues = user.UserStructureAttributesValues;

                user.UserStructureAttributeListValues.AddRange(
                    user.AttributeModels.Select(x => new UserStructureAttributeListValue()
                    {
                        StructureId = StructureId,
                        AttributeProperties = new AttributeProperties()
                        {
                            Id = x.Id,
                            Name = x.Name,
                            Value = x.Name == "Roles" || x.Name == "Privacy" ? "0" : "false"
                        }

                    }).ToList()
                );
                user.ObjectAttributeListValues = user.AttributesValues;
                user.Password = null;
                attributeErrorSummary = ManageUser.Edit(this.UserId, Role, user, (Language)Language);
                Core.API.ManageUser.InsertLoggedInStructure(UserId, StructureId, true);

            }
            return Ok(attributeErrorSummary);
        }
        /// <summary>
        /// DeleteUserFromStructure
        /// </summary>
        /// <returns></returns>
        /// 

        [HttpDelete]
        public IActionResult DeleteUserFromStructure(long UserId, long StructureId)
        {
            bool deleteUserStructure = Core.API.ManageUserStructure.DeleteUserStructure(StructureId, UserId);
            AttributeErrorSummary attributeErrorSummary = new AttributeErrorSummary();

            if (deleteUserStructure)
            {
                var Roles = ManageRole.ListAllRoles();
                string Role = Roles.FirstOrDefault(x => x.Id == RoleId).Name;
                var user = ManageUser.FindWithInclude(UserId);
                user.Groups = user.GroupsList.Select(x => (short)x.Id).ToList();
                user.ApplicationsRoles = (!user.ApplicationsRolesList.IsNullOrEmpty()) ? user.ApplicationsRolesList.Select(x => new UserApplicationRoleViewModel()
                {
                    Id = x.Id,
                    ApplicationId = x.Application.Id,
                    RoleId = (short)x.Role.Id,
                    UserTypeId = x.UserTypeId
                }).ToList() : null;
                user.Structures = user.StructuresList.Select(x => x.Id).ToList();
                user.UserStructureAttributeListValues = user.UserStructureAttributesValues;
                if (user.StructureId == StructureId)
                {
                    user.StructureId = null;
                }
                if (user.Structures.Contains(StructureId))
                {
                    //var deletedAttributes = user.UserStructureAttributeListValues.Where(x => x.StructureId == StructureId).ToList();
                    //foreach ( var item in deletedAttributes)
                    //{
                    //    user.UserStructureAttributeListValues.Remove(item);
                    //}
                    user.UserStructureAttributeListValues.RemoveAll(item => item.StructureId == StructureId);
                    user.Structures.Remove(StructureId);
                }
                user.ObjectAttributeListValues = user.AttributesValues;
                attributeErrorSummary = ManageUser.Edit(this.UserId, Role, user, (Language)Language);
            }
            else
            {
                attributeErrorSummary.Message = "ErrorOccured";
            }

            return Ok(attributeErrorSummary);
        }

        [HttpPost]
        public IActionResult GetAllUserStructures(string name, [FromForm] int? draw, [FromForm] int? start, [FromForm] int? length)
        {
            var count = Core.API.ManageUserStructure.GetUsersStructuresTotalCount(name);
            var data = Core.API.ManageUserStructure.GetUserStructures(name, start, length);
            return Json(new
            {
                draw,
                recordsTotal = count,
                recordsFiltered = count,
                data = new { userStructures = data }
            });
        }
        [HttpGet]
        public IActionResult GetAllUserStructuresWithoutUserName()
        {
            return Ok(new { userStructures = Core.API.ManageUserStructure.GetUserStructures(UserId) });
        }
        [HttpGet]
        public IActionResult GetUsersFromCTS(string userName, long? delegationId, bool enableSendingRules, bool enableTransferToUsers, long? startIndex, long? pageSize)
        {
            try
            {
                return Ok(Core.API.ManageUser.GetUsersFromCTS(userName, delegationId, Core.Configuration.EnableSendingRules, IsStructureSender, StructureIds, UserId, StructureId, startIndex, pageSize, Language));
            }
            catch (Exception ex)
            {
                if (ex.InnerException != null)
                {
                    return BadRequest(ex.InnerException.Message);
                }
                else
                {
                    return BadRequest(ex.Message);
                }
            }
        }

        [HttpPost]
        public IActionResult GetUsersStructuresFromCTS(string searchText, long? delegationId, long? startIndex, long? pageSize, CTS.Core.StructureType structureType, string searchStructure, string searchUser, long? categoryId=null, bool fromSendingEntity= false, bool fromSendingandReceiving = false, bool filterByLoggedInStructure = false) 
       {
            try
            {
                return Ok(Core.API.ManageUser.GetUsersStructuresFromCTS(searchText,
                    delegationId, Core.Configuration.EnableSendingRules, Core.Configuration.EnableTransferToUsers, IsStructureSender, StructureIds, UserId, StructureId, startIndex, pageSize, structureType, searchStructure, searchUser,fromSendingandReceiving , Language, categoryId, fromSendingEntity , Core.Configuration.AllowTransferToMe, filterByLoggedInStructure?StructureId:default));
            }
            catch (Exception ex)
            {
                if (ex.InnerException != null)
                {
                    return BadRequest(ex.InnerException.Message);
                }
                else
                {
                    return BadRequest(ex.Message);
                }
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetUser(long id)
        {
            var user =  ManageUser.GetUser(id);
            return Ok(user);
        }

        [HttpGet]
        public async Task<IActionResult> GetUserMenusNodesBaskets()
        {
            var userPermission = Core.Utility.IdentityHelperExtension.GetUserStrucurePermission(UserId, Language, false);
            var menus = Core.Configuration.EnablePerStructure ? Core.API.ManageUserStructure.ListMenuesByRoleIdUserIdStructureId(RoleId, UserId, StructureId) : Intalio.Core.API.ManageMenu.ListMenuesByRoleIdUserId(RoleId, UserId);
            
            var nodes = Core.Configuration.EnablePerStructure ? Intalio.Core.API.ManageNode.ListNodesByRoleIdUserIdStructureId(RoleId, UserId, StructureId, (bool)userPermission.IsSecurityBreakedInheritance, Language) : Intalio.Core.API.ManageNode.ListNodesByRoleIdUserId(RoleId, UserId, Language);
            
            var baskets = Core.API.ManageBasket.ListMyBasket(UserId);
            baskets.AddRange(Core.API.ManageBasket.ListOtherBasket(UserId));

            var delegationList = Core.API.ManageDelegation.ListDelegatedToUser(UserId);
            List<Intalio.Core.Model.NodeTreeListViewModel> delegatedNodes = new List<Intalio.Core.Model.NodeTreeListViewModel>();
            foreach (var delegation in delegationList)
            {
                delegatedNodes.AddRange(Intalio.Core.API.ManageNode.ListNodesByRoleIdUserId(delegation.FromUserRoleId.Value, delegation.FromUserId, Language).Where(t => t.IsSearch != true && t.Visible == true).ToList());
            }

            var userNodes = Intalio.Core.API.ManageNode.ListUserNodesByUserIdandStructureId(RoleId, UserId, StructureId, Language);

            return Ok(new { menus, nodes, baskets, delegatedNodes, userNodes });
        }
    }
}
