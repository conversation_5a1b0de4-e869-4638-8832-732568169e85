# CTS (Correspondence Tracking System) Documentation

## Overview

This documentation provides comprehensive guidance for developing and maintaining the CTS (Correspondence Tracking System). The CTS is an enterprise-level correspondence management system built with ASP.NET Core, Backbone.js, and SQL Server.

## Documentation Index

### 📋 Core Architecture
- **[CTS Architecture Overview](CTS-Architecture-Overview.md)** - Controllers → Managers → Entities → Database Context pattern with code examples
- **[CTS Project Structure](CTS-Project-Structure.md)** - Directory structure, file organization, and component relationships

### 🛠️ Development Guidelines  
- **[CTS Development Guidelines](CTS-Development-Guidelines.md)** - Development constraints, best practices, and coding standards
- **[CTS Database Management](CTS-Database-Management.md)** - SQL-only approach, script naming conventions, and database change procedures

### 🌐 Frontend & Internationalization
- **[CTS Frontend Architecture](CTS-Frontend-Architecture.md)** - Backbone.js routing system, Handlebars template structure, and build requirements
- **[CTS Translation System](CTS-Translation-System.md)** - Internationalization workflow, _Translator.cshtml usage, and SQL script patterns

## Quick Start Guide

### For New Developers

1. **Read Architecture Overview** - Understand the Controllers → Managers → Entities → Database Context pattern
2. **Review Development Guidelines** - Learn about constraints (no global variables, no EF migrations, etc.)
3. **Study Project Structure** - Familiarize yourself with file organization and naming conventions
4. **Understand Translation System** - Learn how to add new UI text and translations

### For Specific Tasks

#### Adding New Features
1. Review [Architecture Overview](CTS-Architecture-Overview.md) for layer responsibilities
2. Follow [Development Guidelines](CTS-Development-Guidelines.md) for coding standards
3. Use [Database Management](CTS-Database-Management.md) for database changes
4. Reference [Project Structure](CTS-Project-Structure.md) for file placement

#### Frontend Development
1. Study [Frontend Architecture](CTS-Frontend-Architecture.md) for Backbone.js patterns
2. Understand template compilation requirements (full rebuild needed)
3. Follow component lifecycle management practices

#### Database Changes
1. **Never use EF migrations** - Use SQL scripts only
2. Follow naming convention: `{Developer}_{Feature}.sql`
3. Place scripts in `Database/` directory
4. Reference [Database Management](CTS-Database-Management.md) for patterns

#### Adding Translations
1. Check existing translations first
2. Add to `_Translator.cshtml`
3. Create SQL script for `TranslatorDictionary` table
4. Follow [Translation System](CTS-Translation-System.md) workflow

## Key Architectural Principles

### 🏗️ Layered Architecture
```
Controllers (HTTP) → Managers (Business) → Entities (Data) → Database
```

### 🚫 Development Constraints
- **No global variables** - Use proper scoping and dependency injection
- **No EF migrations** - All database changes via SQL scripts
- **No manual package editing** - Use package managers only
- **Full rebuild required** - For Handlebars template changes

### ✅ Required Practices
- Follow Controllers → Managers → Entities → Database Context pattern
- Use SQL scripts for all database changes
- Use package managers for dependency management
- Follow established naming conventions

## Technology Stack

### Backend
- **ASP.NET Core** - Web framework
- **Entity Framework Core** - Data access (no migrations)
- **SQL Server** - Database
- **C#** - Programming language

### Frontend
- **Backbone.js** - MVC framework and routing
- **Handlebars** - Template engine
- **jQuery** - DOM manipulation
- **Bootstrap** - CSS framework

### Database
- **SQL Server** - Primary database
- **SQL Scripts** - All schema changes
- **TranslatorDictionary** - Translation storage

## Common Workflows

### Adding a New Feature

1. **Plan the Architecture**
   ```
   Controller → Manager → Entity → Database
   ```

2. **Create Database Changes**
   ```sql
   -- File: YourName_FeatureName.sql
   CREATE TABLE [dbo].[NewFeature] (
       [Id] [bigint] IDENTITY(1,1) NOT NULL,
       -- columns here
   );
   ```

3. **Create Entity**
   ```csharp
   // File: Intalio.CTS.Core/DAL/NewFeature.cs
   public partial class NewFeature : IDbObject<NewFeature>, IDisposable
   {
       // Entity implementation
   }
   ```

4. **Create Manager**
   ```csharp
   // File: Intalio.CTS.Core/API/ManageNewFeature.cs
   public static class ManageNewFeature
   {
       // Business logic methods
   }
   ```

5. **Create Controller**
   ```csharp
   // File: Intalio.CTS/Controllers/NewFeatureController.cs
   [Route("[controller]/[action]")]
   public class NewFeatureController : BaseController
   {
       // HTTP endpoints
   }
   ```

6. **Create Frontend Components** (if needed)
   ```javascript
   // File: wwwroot/components/newFeatureComponent.js
   var NewFeatureComponent = Backbone.View.extend({
       // Component implementation
   });
   ```

### Adding Translations

1. **Check Existing**
   ```sql
   SELECT * FROM TranslatorDictionary WHERE Keyword LIKE '%YourKeyword%';
   ```

2. **Add to _Translator.cshtml**
   ```csharp
   Resources.YourKeyword = '@Html.Raw(Localizer["YourKeyword"].Encode())';
   ```

3. **Create SQL Script**
   ```sql
   -- File: YourName_YourFeature_Translation.sql
   IF NOT EXISTS (SELECT 1 FROM TranslatorDictionary WHERE Keyword = N'YourKeyword')
   BEGIN
       INSERT [dbo].[TranslatorDictionary] ([Keyword], [EN], [FR], [AR], [IsSystem]) 
       VALUES (N'YourKeyword', N'English', N'Français', N'العربية', 1)
   END
   ```

4. **Use in Frontend**
   ```javascript
   var message = Resources.YourKeyword;
   ```

## File Location Quick Reference

| Component | Location | Example |
|-----------|----------|---------|
| Controllers | `Intalio.CTS/Controllers/` | `DocumentController.cs` |
| Managers | `Intalio.CTS.Core/API/` | `ManageDocument.cs` |
| Entities | `Intalio.CTS.Core/DAL/` | `Document.cs` |
| Frontend Components | `wwwroot/components/` | `documentComponent.js` |
| Templates | `wwwroot/templates/` | `document.handlebars` |
| Database Scripts | `Database/` | `Ibrahim_AddFollowUp.sql` |
| Translations | `Views/Shared/_Translator.cshtml` | Client-side resources |
| Documentation | `docs/` | This file |

## Troubleshooting

### Common Issues

#### Template Changes Not Appearing
- **Cause**: Handlebars templates require full rebuild
- **Solution**: `dotnet clean && dotnet build`

#### Translation Not Working
- **Check**: Database entry exists in `TranslatorDictionary`
- **Check**: Entry exists in `_Translator.cshtml`
- **Check**: Application restarted after `_Translator.cshtml` changes

#### Database Script Errors
- **Check**: Script follows naming convention
- **Check**: Script includes existence checks (`IF NOT EXISTS`)
- **Check**: Script tested in development environment first

#### Component Not Loading
- **Check**: Component registered in router
- **Check**: Template exists and compiles
- **Check**: JavaScript syntax errors in browser console

## Best Practices Summary

### Code Quality
- ✅ Follow established architecture pattern
- ✅ Use descriptive naming conventions
- ✅ Implement proper error handling
- ✅ Write unit tests for business logic
- ✅ Use structured logging

### Database Management
- ✅ Always use SQL scripts for changes
- ✅ Include existence checks in scripts
- ✅ Test scripts in development first
- ✅ Use transactions for data modifications
- ✅ Document script purpose and dependencies

### Frontend Development
- ✅ Follow Backbone.js patterns
- ✅ Use Handlebars helpers consistently
- ✅ Implement proper component cleanup
- ✅ Handle errors gracefully
- ✅ Rebuild after template changes

### Translation Management
- ✅ Check existing translations first
- ✅ Use descriptive keyword names
- ✅ Provide all language variants
- ✅ Test UI layout with longer text
- ✅ Consider RTL layout for Arabic

## Getting Help

### Documentation Structure
Each documentation file is self-contained but cross-references related topics. Use the links between documents to navigate related concepts.

### Code Examples
All documentation includes practical code examples from the actual CTS codebase, showing real patterns and implementations used in the system.

### Quick Reference Sections
Each document includes quick reference sections for common commands, patterns, and file locations to speed up development tasks.

---

**Note**: This documentation reflects the current state of the CTS system architecture and should be updated as the system evolves. Always refer to the actual codebase for the most current implementation details.
