import Intalio from './common.js'
var gIsLocked = false;
class EventIndex extends Intalio.Model
{
    constructor()
    {
        super();
        this.documentId = null;
        this.eventId = null;
    }
}
function submitEvent(self, closeModal) {
    var $form = $(self.refs['formIndexPost']);
    $form.parsley().reset();

    var eventName = $(self.refs['eventName']).val();
    var eventLocation = $(self.refs['eventLocation']).val();
    var eventDescription = $(self.refs['eventDescription']).val();
    var eventFromDate = $(self.refs['fromDate']).val();
    var eventToDate = $(self.refs['toDate']).val();
    //if (eventName.trim().length < 3 || eventLocation.trim().length < 3 || eventDescription.trim().length < 5) {
    //    return;
    //}

    var isValid = $form.parsley().validate();
    if (isValid) {
        gIsLocked = true;
        let params = {
            'Name': eventName,
            'Location': eventLocation,
            'Description': eventDescription,
            'FromDate': eventFromDate,
            'ToDate': eventToDate,
            'DocumentId': self.model.documentId,
            '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
        };

        if (self.model.eventId) {
            params.Id = self.model.eventId;
        }

        var btn = $(self.refs['btnSubmitEvent']);
        btn.button('loading');
        var btnSubmitClose = $(self.refs['btnSubmitCloseEvent']);
        var btnClose = $(self.refs['btnCloseEvent']);
        var btnCloseX = $(self.refs['eventClose']);
        btnClose.attr('disabled', 'disabled');
        btnCloseX.attr('disabled', 'disabled');
        btnSubmitClose.attr('disabled', 'disabled');

        // Send request to save event (either create or update)
        Common.ajaxPost('/Events/Index', params, function (data) {
            gIsLocked = false;
            if (data.message === Resources.NoPermission) {
                Common.alertMsg(Resources.NoPermission);
            } else {
                Common.showScreenSuccessMsg();
                self.model.eventId = data.id;
                $(self.refs['modalEventTitle']).html(Resources.Edit);
                $(".grdEventItems").DataTable().ajax.reload();
            }

            btn.button('reset');
            btnClose.removeAttr('disabled');
            btnCloseX.removeAttr('disabled');
            btnSubmitClose.removeAttr('disabled');

            if (closeModal) {
                $(self.refs['btnCloseEvent']).trigger("click");
            }
        }, function () {
            gIsLocked = false;
            btn.button('reset');
            btnClose.removeAttr('disabled');
            btnCloseX.removeAttr('disabled');
            btnSubmitClose.removeAttr('disabled');
            Common.showScreenErrorMsg();
        }, {
            contentType: 'application/json',
            dataType: 'json'
        });
    }
}
class EventIndexView extends Intalio.View {
    constructor(element, model) {
        super(element, "eventindex", model);
    }
    render() {
        var self = this;

      
        //var template = Handlebars.compile(document.getElementById('modalTemplate').innerHTML);
        //var context = {
        //    eventTitle: self.model.eventId ? Resources.Edit : Resources.New,
        //    name: self.model.name || '',
        //    location: self.model.location || '',
        //    description: self.model.description || ''
        //};

        //var html = template(context);
        //$(self.refs['modalEvent']).html(html); 

        var model = this.model;
        var fromDate = $(self.refs['fromDate']).flatpickr({
            noCalendar: false,
            enableTime: false,
            dateFormat: 'd/m/Y',
            onOpen: function (ct) {
                this.set({
                    maxDate: jQuery(self.refs['toDate']).val() && jQuery(self.refs['toDate']).val() !== "" ? jQuery(self.refs['toDate']).val() : false
                });
            }
        });
        $(self.refs["fromDate_img"]).on('click',function () {
          //  fromDate.toggle();
        });
        var toDate = $(self.refs['toDate']).flatpickr({
            noCalendar: false,
            enableTime: false,
            dateFormat: 'd/m/Y',
            onOpen: function (ct) {
                this.set({
                    minDate: jQuery(self.refs['fromDate']).val() && jQuery(self.refs['fromDate']).val() !== "" ? jQuery(self.refs['fromDate']).val() : false,
                    
                });
            }
        });
        $(self.refs["toDate_img"]).click(function () {
         //   toDate.toggle();
        });
        $(self.refs['btnSubmitEvent']).on('click', function (event, closeModal) {
            if (gIsLocked === false) {
                if (window.EnableConfirmationMessage === "True") {
                    Common.showConfirmMsg(Resources.SubmitEventConfirmation, function () {
                        submitEvent(self, closeModal);
                    });
                } else {
                    submitEvent(self, closeModal);
                }
            }
        });

        $(self.refs['btnSubmitCloseEvent']).on('click', function () {
            $(self.refs['btnSubmitEvent']).trigger("click", [true]);
        });
    }

    setData(data) {
        $(this.refs['eventName']).val(data.name);
        $(this.refs['eventLocation']).val(data.location);
        $(this.refs['eventDescription']).val(data.description);
        $(this.refs['fromDate']).val(data.fromDate);
        $(this.refs['toDate']).val(data.toDate);
        if (data.fromDate) {
            $(this.refs['fromDate']).flatpickr().setDate(data.fromDate);
        }

        if (data.toDate) {
            $(this.refs['toDate']).flatpickr().setDate(data.toDate);
        }
        if (data.fromDate && data.toDate) {
            $(this.refs['toDate']).flatpickr().set({
                minDate: data.fromDate
            });
            $(this.refs['fromDate']).flatpickr().set({
                maxDate: data.toDate
            });
        }
    }
    }

export default { EventIndex, EventIndexView };
