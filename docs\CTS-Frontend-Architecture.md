# CTS Frontend Architecture

## Table of Contents
- [Overview](#overview)
- [Technology Stack](#technology-stack)
- [Backbone.js Routing System](#backbonejs-routing-system)
- [Handlebars Template System](#handlebars-template-system)
- [Component Architecture](#component-architecture)
- [Build Requirements](#build-requirements)
- [Asset Management](#asset-management)
- [Best Practices](#best-practices)

## Overview

The CTS frontend is built using **Backbone.js** as the primary JavaScript framework with **Handlebars** for templating. The architecture emphasizes component-based development with clear separation between routing, views, and templates.

### Key Characteristics
- **Single Page Application (SPA)** architecture
- **Backbone.js Router** for client-side navigation
- **Handlebars templates** for dynamic content rendering
- **Component-based** structure for reusability
- **Custom build process** requiring full rebuilds for template changes

## Technology Stack

### Core Technologies
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Backbone.js   │    │   Handlebars    │    │     jQuery      │
│   (Routing &    │ -> │   (Templates)   │ -> │   (D<PERSON> Manip)   │
│    Views)       │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Frontend Stack Components
- **Backbone.js**: MVC framework for routing and views
- **Handlebars**: Template engine for dynamic HTML generation
- **jQuery**: DOM manipulation and AJAX operations
- **Bootstrap**: CSS framework for responsive design
- **Custom Components**: CTS-specific UI components

### File Structure
```
Intalio.CTS/wwwroot/
├── components/                 # Backbone.js components
│   ├── appComponent.js        # Main application routing
│   └── {feature}Component.js  # Feature-specific components
├── js/                        # JavaScript files
│   ├── CTSCoreComponents.dev.js  # Core component routing
│   └── {feature}.js           # Feature-specific scripts
├── templates/                 # Handlebars templates
│   ├── {feature}.handlebars   # Feature templates
│   └── shared/                # Shared templates
├── css/                       # Stylesheets
└── lib/                       # Third-party libraries
```

## Backbone.js Routing System

### Main Application Router (`appComponent.js`)

The primary routing configuration is defined in `appComponent.js`:

```javascript
// Main application routes
var AppRouter = Backbone.Router.extend({
    routes: {
        // Inbox routes
        'inbox/:nodeId': 'inboxRoute',
        'inbox/:nodeId/:page': 'inboxRoute',
        
        // Draft routes  
        'draft/:nodeId': 'draftRoute',
        'draft/:nodeId/:page': 'draftRoute',
        
        // Completed routes
        'completed/:nodeId': 'completedRoute',
        'completed/:nodeId/:page': 'completedRoute',
        
        // Sent routes
        'sent/:nodeId': 'sentRoute',
        'sent/:nodeId/:page': 'sentRoute',
        
        // Custom node routes
        'custom/:nodeId': 'customNodeRoute',
        'custom/:nodeId/:page': 'customNodeRoute',
        
        // Default route
        '': 'defaultRoute',
        '*path': 'defaultRoute'
    },
    
    // Route handlers
    inboxRoute: function(nodeId, page) {
        this.loadComponent('inbox', nodeId, page);
    },
    
    draftRoute: function(nodeId, page) {
        this.loadComponent('draft', nodeId, page);
    },
    
    // Component loading logic
    loadComponent: function(componentType, nodeId, page) {
        var component = new window[componentType + 'Component']({
            nodeId: nodeId,
            page: page || 1
        });
        component.render();
    }
});
```

### Core Components Router (`CTSCoreComponents.dev.js`)

Additional routing for system components:

```javascript
// Core system routes
var CoreRouter = Backbone.Router.extend({
    routes: {
        // Administration routes
        'admin/users': 'adminUsersRoute',
        'admin/categories': 'adminCategoriesRoute',
        'admin/templates': 'adminTemplatesRoute',
        
        // Configuration routes
        'config/parameters': 'configParametersRoute',
        'config/security': 'configSecurityRoute',
        
        // Report routes
        'reports/dashboard': 'reportsDashboardRoute',
        'reports/statistics': 'reportsStatisticsRoute'
    },
    
    // Route implementations
    adminUsersRoute: function() {
        this.loadAdminComponent('users');
    },
    
    configParametersRoute: function() {
        this.loadConfigComponent('parameters');
    }
});
```

### Route Pattern Examples

#### Standard Node-Based Routes
```javascript
// Pattern: {type}/{nodeId}[/{page}]
'inbox/123'           // Inbox for node 123, page 1
'inbox/123/2'         // Inbox for node 123, page 2
'draft/456'           // Draft for node 456
'completed/789/3'     // Completed for node 789, page 3
```

#### Administrative Routes
```javascript
// Pattern: admin/{feature}
'admin/users'         // User management
'admin/categories'    // Category management
'admin/templates'     // Template management
```

#### Custom Routes
```javascript
// Pattern: custom/{nodeId}[/{action}]
'custom/123'          // Custom node 123
'custom/123/edit'     // Edit custom node 123
'custom/123/export'   // Export custom node 123
```

### Route Handler Pattern
```javascript
// Standard route handler pattern
routeHandler: function(param1, param2) {
    // 1. Validate parameters
    if (!param1 || isNaN(param1)) {
        this.navigate('error', {trigger: true});
        return;
    }
    
    // 2. Load required data
    var data = this.loadData(param1);
    
    // 3. Create component
    var component = new ComponentClass({
        data: data,
        param: param2
    });
    
    // 4. Render component
    component.render();
    
    // 5. Update UI state
    this.updateActiveNavigation(param1);
}
```

## Handlebars Template System

### Template Organization

Templates are organized by feature in the `wwwroot/templates/` directory:

```
templates/
├── document.handlebars           # Document management
├── inbox.handlebars             # Inbox view
├── draft.handlebars             # Draft documents
├── transfer.handlebars          # Transfer operations
├── search.handlebars            # Search functionality
├── attachment.handlebars        # Attachment handling
└── shared/                      # Shared templates
    ├── header.handlebars
    ├── footer.handlebars
    └── pagination.handlebars
```

### Template Structure Example

```handlebars
{{!-- Document template example --}}
{{#each basicAttributes}}
{{#if Enabled}}
{{#ifEquals Name "Subject"}}
{{setVar "isSubject" true}}
{{/ifEquals}}
{{#ifEquals Name "Priority"}}
{{setVar "isPriority" true}}
{{/ifEquals}}
{{/if}}
{{/each}}

<div ref="{{ComponentId}}" class="form-scroll">
    {{#if readonly}}
    <div class="panel panel-default borderTop-1">
        <div class="panel-body">
            <form ref="formDocumentAttributePost" method="post" data-parsley-validate="">
                {{#if isSubject}}
                <div class="row">
                    <div class="col-sm-6">
                        <div class="form-group">
                            <label>{{Resources.Subject}}</label>
                            <input type="text" class="form-control" value="{{Subject}}" readonly>
                        </div>
                    </div>
                </div>
                {{/if}}
                
                {{#if isPriority}}
                <div class="row">
                    <div class="col-sm-6">
                        <div class="form-group">
                            <label>{{Resources.Priority}}</label>
                            <select class="form-control" disabled>
                                {{#each priorities}}
                                <option value="{{Id}}" {{#if Selected}}selected{{/if}}>
                                    {{Name}}
                                </option>
                                {{/each}}
                            </select>
                        </div>
                    </div>
                </div>
                {{/if}}
            </form>
        </div>
    </div>
    {{/if}}
</div>
```

### Custom Handlebars Helpers

CTS uses custom Handlebars helpers for common operations:

```javascript
// Custom helper examples
Handlebars.registerHelper('ifEquals', function(arg1, arg2, options) {
    return (arg1 == arg2) ? options.fn(this) : options.inverse(this);
});

Handlebars.registerHelper('setVar', function(varName, varValue, options) {
    if (!options.data.root) {
        options.data.root = {};
    }
    options.data.root[varName] = varValue;
});

Handlebars.registerHelper('formatDate', function(date, format) {
    return moment(date).format(format || 'DD/MM/YYYY');
});

Handlebars.registerHelper('translate', function(key) {
    return Resources[key] || key;
});
```

### Template Data Binding

```javascript
// Template data preparation
var templateData = {
    document: {
        Id: 123,
        Subject: "Document Subject",
        CreatedDate: new Date(),
        readonly: true
    },
    basicAttributes: [
        { Name: "Subject", Enabled: true },
        { Name: "Priority", Enabled: true },
        { Name: "DueDate", Enabled: false }
    ],
    priorities: [
        { Id: 1, Name: "High", Selected: false },
        { Id: 2, Name: "Medium", Selected: true },
        { Id: 3, Name: "Low", Selected: false }
    ],
    Resources: window.Resources // Translation resources
};

// Template compilation and rendering
var template = Handlebars.compile(templateSource);
var html = template(templateData);
$('#container').html(html);
```

## Component Architecture

### Component Structure Pattern

```javascript
// Standard component pattern
var DocumentComponent = Backbone.View.extend({
    // Component configuration
    tagName: 'div',
    className: 'document-component',
    
    // Event bindings
    events: {
        'click .btn-save': 'saveDocument',
        'click .btn-cancel': 'cancelEdit',
        'change .form-control': 'validateField'
    },
    
    // Initialization
    initialize: function(options) {
        this.options = options || {};
        this.model = options.model;
        this.template = Handlebars.compile($('#document-template').html());
        
        // Bind model events
        this.listenTo(this.model, 'change', this.render);
        this.listenTo(this.model, 'sync', this.onSaveSuccess);
    },
    
    // Rendering
    render: function() {
        var data = this.prepareTemplateData();
        var html = this.template(data);
        this.$el.html(html);
        
        // Post-render initialization
        this.initializeFormValidation();
        this.bindCustomEvents();
        
        return this;
    },
    
    // Data preparation
    prepareTemplateData: function() {
        return {
            document: this.model.toJSON(),
            readonly: this.options.readonly || false,
            basicAttributes: this.getBasicAttributes(),
            Resources: window.Resources
        };
    },
    
    // Event handlers
    saveDocument: function(e) {
        e.preventDefault();
        
        if (this.validateForm()) {
            var formData = this.getFormData();
            this.model.save(formData, {
                success: this.onSaveSuccess.bind(this),
                error: this.onSaveError.bind(this)
            });
        }
    },
    
    // Cleanup
    destroy: function() {
        this.undelegateEvents();
        this.stopListening();
        this.remove();
    }
});
```

### Component Lifecycle

```javascript
// Component lifecycle management
var ComponentManager = {
    // Active components registry
    activeComponents: {},
    
    // Create and register component
    createComponent: function(type, options) {
        var ComponentClass = window[type + 'Component'];
        if (!ComponentClass) {
            throw new Error('Component not found: ' + type);
        }
        
        var component = new ComponentClass(options);
        this.activeComponents[options.id || type] = component;
        
        return component;
    },
    
    // Destroy component
    destroyComponent: function(id) {
        var component = this.activeComponents[id];
        if (component) {
            component.destroy();
            delete this.activeComponents[id];
        }
    },
    
    // Destroy all components
    destroyAll: function() {
        Object.keys(this.activeComponents).forEach(function(id) {
            this.destroyComponent(id);
        }.bind(this));
    }
};
```

## Build Requirements

### Critical Build Constraint

⚠️ **Important**: Any changes to `.hbs` (Handlebars) files require a **full project rebuild**.

#### Why Full Rebuild is Required
1. **Template Compilation**: Handlebars templates are compiled into the application
2. **Asset Bundling**: Templates are bundled with other assets during build
3. **Cache Invalidation**: Browser caches need to be invalidated
4. **Dependency Resolution**: Template dependencies must be resolved

#### Build Process for Template Changes

```bash
# 1. Make changes to .hbs files
# Edit: wwwroot/templates/document.handlebars

# 2. Clean previous build
dotnet clean

# 3. Rebuild entire solution
dotnet build --configuration Release

# 4. Restart application
# IIS: Recycle application pool
# Development: Restart debugging session
```

### Build Workflow

```
Template Change (.hbs) 
        ↓
Clean Solution (dotnet clean)
        ↓
Full Rebuild (dotnet build)
        ↓
Deploy Application
        ↓
Clear Browser Cache
        ↓
Verify Changes
```

### Development Workflow

```javascript
// Development best practices for template changes

// 1. Test template syntax before build
var templateSource = $('#template-id').html();
try {
    var template = Handlebars.compile(templateSource);
    console.log('Template syntax valid');
} catch (error) {
    console.error('Template syntax error:', error);
}

// 2. Use template debugging
Handlebars.registerHelper('debug', function(optionalValue) {
    console.log('Current Context:', this);
    if (optionalValue) {
        console.log('Value:', optionalValue);
    }
});

// 3. Validate data before rendering
function validateTemplateData(data) {
    if (!data.Resources) {
        console.warn('Missing Resources in template data');
    }
    if (!data.document) {
        console.warn('Missing document in template data');
    }
    return data;
}
```

## Asset Management

### Static File Organization

```
wwwroot/
├── css/
│   ├── bootstrap.min.css      # Framework styles
│   ├── cts-main.css          # Main application styles
│   └── components/           # Component-specific styles
├── js/
│   ├── lib/                  # Third-party libraries
│   │   ├── backbone.min.js
│   │   ├── handlebars.min.js
│   │   └── jquery.min.js
│   ├── components/           # Component scripts
│   └── cts-core.js          # Core application script
├── images/
│   ├── icons/               # UI icons
│   └── logos/               # Application logos
└── templates/               # Handlebars templates
```

### Asset Loading Strategy

```html
<!-- Asset loading order in layout -->
<!DOCTYPE html>
<html>
<head>
    <!-- CSS files -->
    <link href="~/css/bootstrap.min.css" rel="stylesheet" />
    <link href="~/css/cts-main.css" rel="stylesheet" />
</head>
<body>
    <!-- Content -->
    
    <!-- JavaScript libraries (order matters) -->
    <script src="~/js/lib/jquery.min.js"></script>
    <script src="~/js/lib/underscore.min.js"></script>
    <script src="~/js/lib/backbone.min.js"></script>
    <script src="~/js/lib/handlebars.min.js"></script>
    
    <!-- Translation resources -->
    @await Html.PartialAsync("_Translator")
    
    <!-- Application scripts -->
    <script src="~/js/cts-core.js"></script>
    <script src="~/components/appComponent.js"></script>
    
    <!-- Component-specific scripts -->
    @RenderSection("Scripts", required: false)
</body>
</html>
```

### Cache Management

```javascript
// Template caching strategy
var TemplateCache = {
    cache: {},
    
    get: function(templateName) {
        if (!this.cache[templateName]) {
            var templateSource = $('#' + templateName + '-template').html();
            this.cache[templateName] = Handlebars.compile(templateSource);
        }
        return this.cache[templateName];
    },
    
    clear: function() {
        this.cache = {};
    },
    
    preload: function(templateNames) {
        templateNames.forEach(function(name) {
            this.get(name);
        }.bind(this));
    }
};
```

## Best Practices

### Routing Best Practices

1. **Consistent URL Patterns**
```javascript
// Good: Consistent pattern
'inbox/:nodeId'
'draft/:nodeId'  
'sent/:nodeId'

// Bad: Inconsistent pattern
'inbox/:id'
'drafts/:nodeId'
'sentItems/:node'
```

2. **Parameter Validation**
```javascript
routeHandler: function(nodeId, page) {
    // Validate required parameters
    if (!nodeId || isNaN(nodeId)) {
        this.navigate('error/invalid-node', {trigger: true});
        return;
    }
    
    // Validate optional parameters
    page = parseInt(page) || 1;
    if (page < 1) page = 1;
    
    // Continue with route handling
    this.loadComponent(nodeId, page);
}
```

### Template Best Practices

1. **Template Organization**
```handlebars
{{!-- Use consistent structure --}}
<div class="component-container" ref="{{ComponentId}}">
    {{!-- Header section --}}
    <div class="component-header">
        <h3>{{Resources.ComponentTitle}}</h3>
    </div>
    
    {{!-- Content section --}}
    <div class="component-content">
        {{> contentPartial}}
    </div>
    
    {{!-- Footer section --}}
    <div class="component-footer">
        {{> actionButtons}}
    </div>
</div>
```

2. **Data Validation in Templates**
```handlebars
{{!-- Always check for data existence --}}
{{#if document}}
    {{#if document.Subject}}
        <h2>{{document.Subject}}</h2>
    {{else}}
        <h2>{{Resources.NoSubject}}</h2>
    {{/if}}
{{else}}
    <div class="alert alert-warning">
        {{Resources.DocumentNotFound}}
    </div>
{{/if}}
```

### Component Best Practices

1. **Memory Management**
```javascript
// Always clean up components
var BaseComponent = Backbone.View.extend({
    destroy: function() {
        // Remove event listeners
        this.undelegateEvents();
        this.stopListening();
        
        // Clean up child components
        if (this.childComponents) {
            this.childComponents.forEach(function(child) {
                child.destroy();
            });
        }
        
        // Remove from DOM
        this.remove();
    }
});
```

2. **Error Handling**
```javascript
render: function() {
    try {
        var data = this.prepareTemplateData();
        var html = this.template(data);
        this.$el.html(html);
        
        this.postRender();
        return this;
    } catch (error) {
        console.error('Render error in component:', error);
        this.showError(Resources.RenderError);
        return this;
    }
}
```

## Quick Reference

### Key Files
- **Main Router**: `wwwroot/components/appComponent.js`
- **Core Router**: `wwwroot/js/CTSCoreComponents.dev.js`
- **Templates**: `wwwroot/templates/*.handlebars`
- **Translations**: `Views/Shared/_Translator.cshtml`

### Build Commands
```bash
# Full rebuild (required for .hbs changes)
dotnet clean && dotnet build

# Development server
dotnet run

# Production build
dotnet build --configuration Release
```

### Common Patterns
```javascript
// Route: 'feature/:id'
// Component: FeatureComponent
// Template: feature.handlebars
// Data: { model: data, Resources: window.Resources }
```
