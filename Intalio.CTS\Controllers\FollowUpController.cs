﻿using AngleSharp.Css;
using Intalio.Core;
using Intalio.Core.DAL;
using Intalio.Core.Model;
using Intalio.Core.UI.Filters;
using Intalio.CTS.Core;
using Intalio.CTS.Core.API;
using Intalio.CTS.Core.DAL;
using Intalio.CTS.Core.Model;
using Intalio.CTS.Filters;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;


namespace Intalio.CTS.Controllers
{
    [Route("[controller]/[action]")]
    public class FollowUpController : BaseController
    {
        #region Ajax
        /// <summary>
        /// Create a FollowUp 
        /// </summary>
        /// <param name="model">followUp parameters</param>
        /// <returns></returns>
        [HttpPost]
        [CustomActionsAuthorizationFilter(new string[] { nameof(CustomActions.CreateFollowUp) })]
        public async Task<IActionResult> Create([FromForm] FollowUpCreateModel model)
        {
            try
            {
                var returnVal = await ManageFollowUp.Create(model, UserId, RoleId, StructureIds, IsStructureReceiver,IsStructureSender, PrivacyLevel, StructureId, Language);
                return Ok(new { returnVal.Status, returnVal.FollowUpId, returnVal.Message});
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }
        /// <summary>
        /// Get a FollowUp 
        /// </summary>
        /// <param name="id">followUp id</param>
        /// <returns></returns>
        [HttpGet]
        //[ProducesResponseType(typeof(FollowUpDetailsModel), 200)]
        [Produces("application/json")]
        public async Task<IActionResult> Get(long id)
        {
            try
            {

                var model = await ManageFollowUp.Get(UserId, id, RoleId, StructureId, Language);
                return Ok(new { model.Status,model.Model,model.Message });

            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex, id);
            }
        }
        /// <summary>
        /// List FollowUp
        /// </summary>
        /// <param name="draw"></param>
        /// <param name="start">Starting record - The number of records to skip before starting to return records from the query. Default value is 0.</param>
        /// <param name="length">Number of records to be returned</param>
        /// <returns></returns> 
        [HttpPost]
        [ProducesResponseType(typeof(GridListViewModel<FollowUpListViewModel>), 200)]
        public async Task<JsonResult> ListFollowUp([FromForm] int draw, [FromForm] int start, [FromForm] int length)
        {
            try
            {
                short nodeId = Request.Form["NodeId"].Count > 0 ? Convert.ToInt16(Request.Form["NodeId"][0]) : default;
                //long structureId = Request.Form["StructureId"].Count > 0 ? Convert.ToInt32(Request.Form["StructureId"][0]) : default;
                int categoryId = Request.Form["CategoryId"].Count > 0 ? Convert.ToInt32(Request.Form["CategoryId"][0]) : default;
                string fromDate = Request.Form["FromDate"].Count > 0 ? Convert.ToString(Request.Form["FromDate"][0]) : string.Empty;
                string toDate = Request.Form["ToDate"].Count > 0 ? Convert.ToString(Request.Form["ToDate"][0]) : string.Empty;
                long year = Request.Form["Year"].Count > 0 ? Convert.ToInt32(Request.Form["Year"][0]) : default;
                string subject = Request.Form["Subject"].Count > 0 ? Convert.ToString(Request.Form["Subject"][0]) : string.Empty;
                string referenceNumber = Request.Form["ReferenceNumber"].Count > 0 ? Convert.ToString(Request.Form["ReferenceNumber"][0]) : string.Empty;
                string keyWord = Request.Form["KeyWord"].Count > 0 ? Convert.ToString(Request.Form["KeyWord"][0]) : string.Empty;
                string note = Request.Form["Note"].Count > 0 ? Convert.ToString(Request.Form["Note"][0]) : string.Empty;
                long receivingEntityId = Request.Form["ReceivingEntityId"].Count > 0 ? Convert.ToInt32(Request.Form["ReceivingEntityId"][0]) : default;
                long sendingEntityId = Request.Form["SendingEntityId"].Count > 0 ? Convert.ToInt32(Request.Form["SendingEntityId"][0]) : default;
                long followedUpById = Request.Form["FollowedUpById"].Count > 0 ? Convert.ToInt32(Request.Form["FollowedUpById"][0]) : default;
                long priorityId = Request.Form["PriorityId"].Count > 0 ? Convert.ToInt32(Request.Form["PriorityId"][0]) : default;

                //if ((structureId == default || !StructureIds.Any(S => S == structureId)) && Intalio.CTS.Core.Configuration.EnablePerStructure)
                //{
                //    return Json(new
                //    {
                //        draw,
                //        recordsTotal = 0,
                //        recordsFiltered = 0,
                //        data = new List<FollowUpListViewModel>()
                //    });
                //}

                var result = ManageNode.GetNodeConditionsInExpressionBuilder(nodeId);
                ExpressionBuilderFilters filter = result.Filters;
                if (categoryId != default)
                {
                    filter.Add("OriginalDocument.CategoryId", categoryId, Operator.Equals);
                }
                if (!string.IsNullOrEmpty(fromDate))
                {
                    DateTime from = DateTime.Parse(fromDate).Date;
                    filter.Add("CreatedDate.Date", from.Date, Operator.GreaterThanOrEqual);
                }
                if (!string.IsNullOrEmpty(toDate))
                {
                    DateTime to = DateTime.Parse(toDate);
                    filter.Add("CreatedDate.Date", to.Date, Operator.LessThanOrEqualTo);
                }
                if (year!=default)
                {
                    filter.Add("CreatedDate.Date.Year", year, Operator.Equals);
                }
                if (!string.IsNullOrEmpty(subject))
                {
                    filter.Add("FollowUpDocument.Subject", Core.Helper.GetSearchString(subject), Operator.Contains);
                }
                if (!string.IsNullOrEmpty(referenceNumber))
                {
                    filter.Add("OriginalDocument.ReferenceNumber", Core.Helper.GetSearchString(referenceNumber), Operator.Contains);
                }
                if (!string.IsNullOrEmpty(keyWord))
                {
                    filter.Add("FollowUpDocument.DocumentForm.Keyword", Core.Helper.GetSearchString(keyWord), Operator.Contains);
                }
                if (receivingEntityId != default)
                {
                    filter.Add("OriginalDocument.DocumentReceiverEntity[Id]", receivingEntityId, Operator.Equals);
                }
                if (sendingEntityId != default)
                {
                    filter.Add("OriginalDocument.SendingEntityId", sendingEntityId, Operator.Equals);
                }
                if (priorityId != default)
                {
                    filter.Add("FollowUpDocument.PriorityId", priorityId, Operator.Equals);
                }
                if (followedUpById != default)
                {
                    filter.Add("FollowUpUsers[UserId]", followedUpById, Operator.Equals);
                }
                if (result.OverDue.HasValue)
                {
                    if (result.OverDue.Value)
                    {
                        filter.Add("FollowUpToDate", DateTime.Now.Date, Operator.LessThan);
                    }
                    else
                    {
                        filter.Add("FollowUpToDate", DateTime.Now.Date, Operator.GreaterThanOrEqual);
                    }
                }
                //if (structureId != default && Intalio.CTS.Core.Configuration.EnablePerStructure)
                //{
                //    filter.Add("FollowUpDocument.CreatedByStructureId", structureId, Operator.Equals);
                //}
                List<SortExpression> sorting = new List<SortExpression>();
                string column = Request.Form["order[0][column]"];
                string sort = Request.Form["order[0][dir]"];
               
                    sorting.Add(new SortExpression { Order = SortDirection.Descending, PropertyName = "CreatedDate" });
                
                var retValue = await ManageFollowUp.ListFollowUp(start, length, UserId, StructureId, filter, sorting, Language);
                return Json(new
                {
                    draw,
                    recordsTotal = retValue.Item1,
                    recordsFiltered = retValue.Item1,
                    data = retValue.Item2
                });
            }
            catch (Exception ex)
            {
                return HttpGridAjaxError(ex);
            }
        }
        /// <summary>
        /// Get FollowUp count
        /// </summary>
        /// <param name="nodeId">Node id</param>
        /// <param name="loggedInStructureId">Node id</param>
        /// <returns></returns>
        [HttpGet]
        public async Task<IActionResult> GetFollowUpCounts(short nodeId, long? loggedInStructureId)
        {
            try
            {

                if (Core.Configuration.EnablePerStructure && (loggedInStructureId == null || !StructureIds.Any(S => S == loggedInStructureId.Value)))
                {
                    (int Total, int Today) errValue = (0, 0);
                    return Ok(new { errValue.Total, errValue.Today });
                }

                var result = ManageNode.GetNodeConditionsInExpressionBuilder(nodeId);
                if (result.OverDue.HasValue)
                {
                    if (result.OverDue.Value)
                    {
                        result.Filters.Add("FollowUpToDate", DateTime.Now.Date, Operator.LessThan);
                    }
                    else
                    {
                        result.Filters.Add("FollowUpToDate", DateTime.Now.Date, Operator.GreaterThanOrEqual);
                    }
                }

                (int Total, int Today) retValue = await ManageFollowUp.GetFollowUpCounts(UserId, loggedInStructureId, result.Filters);
                return Ok(new { retValue.Total, retValue.Today });
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }
        /// <summary>
        /// List all followUpStatuses
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [ProducesResponseType(typeof(List<FollowUpStatusViewModel>), 200)]
        [Produces("application/json")]
        public IActionResult ListFullFollowUpStatuses()
        {
            try
            {
                return Ok(ManageFollowUp.ListAllFollowUpStatuses());
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        /// <summary>
        /// List categories returns all categories DDL.
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [ProducesResponseType(typeof(List<ValueText>), 200)]
        [Produces("application/json")]
        public IActionResult ListFollowUpStatuses()
        {
            try
            {
                return Ok(ManageFollowUp.ListFollowUpStatuses(Language));
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        /// <summary>
        /// List FollowUp Security returns all FollowUp Security DDL.
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [ProducesResponseType(typeof(List<ValueText>), 200)]
        [Produces("application/json")]
        public IActionResult ListFollowUpSecurityDDL()
        {
            try
            {
                return Ok(ManageFollowUp.ListFollowUpSecurityDDL(Language));
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }
        /// <summary>
        /// List transfer inbox
        /// </summary>
        /// <param name="draw"></param>
        /// <param name="start">Starting record - The number of records to skip before starting to return records from the query. Default value is 0.</param>
        /// <param name="length">Number of records to be returned</param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(GridListViewModel<TransferListViewModel>), 200)]
        //[ValidateAntiForgeryToken]
        public async Task<JsonResult> ListFollowUpDocumentTransfers([FromForm] int draw, [FromForm] int start, [FromForm] int length)
        {
            try
            {
                var retValue = (0, new List<TransferListViewModel>());

                long documentId = Request.Form["DocumentId"].Count > 0 ? Convert.ToInt64(Request.Form["DocumentId"][0]) : default;

                var filter = new ExpressionBuilderFilters {
                    { "Document.Id", documentId, Operator.Equals },
                    { "StatusId", DocumentStatus.InProgress, Operator.Equals }
                };

                List<SortExpression> sorting = new List<SortExpression>
                {
                    new SortExpression { Order = SortDirection.Descending, PropertyName = "CreatedDate" }
                };

                retValue = await ManageFollowUp.ListFollowUpDocumentTransfers(start, length,filter, sorting, Language);
                return Json(new
                {
                    draw,
                    recordsTotal = retValue.Item1,
                    recordsFiltered = retValue.Item1,
                    data = retValue.Item2
                });
            }
            catch (Exception ex)
            {
                return HttpGridAjaxError(ex, draw);
            }
        }

        /// <summary>
        /// Get Team by team id
        /// </summary>
        /// <param name="id">team Id</param>
        /// <returns>team with team id</returns>
        [HttpGet]
        [ProducesResponseType(typeof(FollowUpUsersModel), 200)]
        public JsonResult GetFollowUpUsers(long followUpId)
        {
            try
            {
                var followUpUsers = ManageFollowUp.GetFollowUpUsers(followUpId);

                return Json(new
                {
                    success = true,
                    data = followUpUsers
                });
            }
            catch (Exception ex)
            {
                return HttpGridAjaxError(ex);
            }
        }

        /// <summary>
        /// add  FollowUp user
        /// </summary>
        /// <param name="model">followUp parameters</param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(FollowUpUsersModel), 200)]
        public async Task<IActionResult> AddUserToFollowUp([FromForm] FollowUpSecurityModel model)
        {
            try
            {
                var returnVal = await ManageFollowUp.AddUserToFollowUp(model, UserId);
                return Ok(new { returnVal.Status, returnVal.model, returnVal.Message });
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }
        /// <summary>
        /// delete  FollowUp user
        /// </summary>
        /// <param name="followUpUserId">followUp parameters</param>
        /// <param name="followUpDocumentId">followUp parameters</param>
        /// <returns></returns>
        [HttpDelete]
        [ProducesResponseType(typeof(FollowUpUsersModel), 200)]
        public async Task<IActionResult> DeleteUserFromFollowUp(long followUpUserId, long followUpDocumentId)
        {
            try
            {
                var returnVal = await ManageFollowUp.DeleteUserFromFollowUp(followUpUserId,followUpDocumentId,UserId, Language);
                return Ok(new { returnVal.Status, returnVal.model, returnVal.Message });
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        /// <summary>
        /// delete  FollowUp user
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(FollowUpResponModel), 200)]
        public async Task<IActionResult> CompleteFollowUp( List<FollowUpActionModel> followUpCompleteModels)
        {
            try
            {
                var returnVal = await ManageFollowUp.CompleteFollowUp(followUpCompleteModels, UserId, StructureId, Language);
                return Ok(returnVal);
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }


        /// <summary>
        /// delete  FollowUp user
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(FollowUpResponModel), 200)]
        public async Task<IActionResult> CancelFollowUp(List<FollowUpActionModel> followUpCancelModels)
        {
            try
            {
                var returnVal = await ManageFollowUp.CancelFollowUp(followUpCancelModels, UserId, StructureId, Language);
                return Ok(returnVal);
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }
        /// <summary>
        ///Postpone FollowUp
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(FollowUpResponModel), 200)]
        public async Task<IActionResult> PostponeFollowUp(FollowUpPostponeModel followUpPostponeModels)
        {
            try
            {
                var returnVal = await ManageFollowUp.PostponeFollowUp(followUpPostponeModels, UserId,StructureId, Language);
                return Ok(returnVal);
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        /// <summary>
        /// Edit  followUp user role
        /// </summary>
        /// <param name="model">followUp parameters</param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(FollowUpUsersModel), 200)]
        public IActionResult UpdateFollowUpUserRole([FromForm] FollowUpSecurityModel model)
        {
            try
            {
                var returnVal =  ManageFollowUp.UpdateFollowUpUserRole(model, UserId);
                return Ok(new { returnVal.Status, returnVal.model, returnVal.Message });
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        /// <summary>
        /// Create a FollowUp panel
        /// </summary>
        /// <param name="model">followUp parameters</param>
        /// <returns></returns>
        [HttpPost]
        [CustomActionsAuthorizationFilter(new string[] { nameof(CustomActions.CreateFollowUp) })]
        public async Task<IActionResult> CreateFollowUpPanel([FromForm] FollowUpPanelCreateModel model)
        {
            try
            {
                var returnVal = await ManageFollowUp.CreateFollowUpPanel(model, UserId, Language);
                return Ok(new { returnVal.Status, returnVal.FollowUpId, returnVal.Message });
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex);
            }
        }

        /// <summary>
        /// List FollowUp
        /// </summary>
        /// <param name="draw"></param>
        /// <param name="start">Starting record - The number of records to skip before starting to return records from the query. Default value is 0.</param>
        /// <param name="length">Number of records to be returned</param>
        /// <returns></returns> 
        [HttpPost]
        [ProducesResponseType(typeof(GridListViewModel<FollowUpPanelListViewModel>), 200)]
        public async Task<JsonResult> ListFollowUpPanel([FromForm] int draw, [FromForm] int start, [FromForm] int length)
        {
            try
            {
                short nodeId = Request.Form["NodeId"].Count > 0 ? Convert.ToInt16(Request.Form["NodeId"][0]) : default;
                string followUpSubject = Request.Form["FollowUpSubject"].Count > 0 ? Convert.ToString(Request.Form["FollowUpSubject"][0]) : string.Empty;
                long followUpCreatedByUserId = Request.Form["FollowUpCreatedByUserId"].Count > 0 ? Convert.ToInt64(Request.Form["FollowUpCreatedByUserId"][0]) : default;
                var result = ManageNode.GetNodeConditionsInExpressionBuilder(nodeId);
                ExpressionBuilderFilters filter = result.Filters;

                if (!string.IsNullOrEmpty(followUpSubject))
                {
                    filter.Add("FollowUpDocument.Subject", Core.Helper.GetSearchString(followUpSubject), Operator.Contains);
                }
                if (followUpCreatedByUserId != default)
                {
                    filter.Add("CreatedByUserId", followUpCreatedByUserId, Operator.Equals);
                }
                List<SortExpression> sorting = new List<SortExpression>();
                string column = Request.Form["order[0][column]"];
                string sort = Request.Form["order[0][dir]"];
                if (!string.IsNullOrEmpty(column))
                {
                    SortDirection sortDirection = sort == "asc" ? SortDirection.Ascending : SortDirection.Descending;
                    int columnNb = Convert.ToInt32(column);
                    switch (columnNb)
                    {
                        case 2:
                            sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "FollowUp.OriginalDocument.SendingEntity.Name" });
                            break;
                        case 3:
                            sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "FollowUp.FollowUpDocument.ReferenceNumber" });
                            break;
                        case 4:
                            sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "FollowUp.OriginalDocument.CreatedDate" });
                            break;
                        case 5:
                            sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "FollowUp.FollowUpDocument.Subject" });
                            break;
                        case 6:
                            sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "FollowUp.FollowUpDocument.Priority.Name" });
                            break;
                        case 7:
                            sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "CreatedByUser.Firstname" }); 
                            break;
                        case 8:
                            sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "ModifiedByUSer.Firstname" });
                            break;
                        case 9:
                            sorting.Add(new SortExpression { Order = sortDirection, PropertyName = "ModifiedDate" });
                            break;

                    }
                }
                else
                {
                    sorting.Add(new SortExpression { Order = SortDirection.Descending, PropertyName = "CreatedDate" });
                }
                var retValue = await ManageFollowUp.ListFollowUpPanel(start, length, UserId, StructureId, filter, sorting, Language);
                return Json(new
                {
                    draw,
                    recordsTotal = retValue.Item1,
                    recordsFiltered = retValue.Item1,
                    data = retValue.Item2
                });
            }
            catch (Exception ex)
            {
                return HttpGridAjaxError(ex);
            }
        }

        /// <summary>
        /// List FollowUp panel items
        /// </summary>
        /// <param name="draw"></param>
        /// <param name="start">Starting record - The number of records to skip before starting to return records from the query. Default value is 0.</param>
        /// <param name="length">Number of records to be returned</param>
        /// <param name="followUpId">Number of records to be returned</param>
        /// <returns></returns> 
        [HttpPost]
        [ProducesResponseType(typeof(GridListViewModel<FollowUpPanelListViewModel>), 200)]
        public async Task<JsonResult> ListFollowUpPanelItems([FromForm] int draw, [FromForm] int start, [FromForm] int length,long followUpId)
        {
            try
            {
                var retValue = await ManageFollowUp.ListFollowUpPanelItems(start, length, followUpId, Language);
                return Json(new
                {
                    draw,
                    recordsTotal = retValue.Item1,
                    recordsFiltered = retValue.Item1,
                    data = retValue.Item2
                });
            }
            catch (Exception ex)
            {
                return HttpGridAjaxError(ex);
            }
        }

        /// <summary>
        /// Check if user can access followUp 
        /// </summary>
        /// <param name="id">followUp id</param>
        /// <returns></returns>
        [HttpGet]
        [ProducesResponseType(typeof(bool), 200)]
        public async Task<IActionResult> CheckIfCanAccessFollowUp(long id)
        {
            try
            {
                return Ok(await ManageFollowUp.CheckIfUserCanAccess( id, UserId, StructureId));
            }
            catch (Exception ex)
            {
                return HttpAjaxError(ex, id);
            }
        }
        #endregion
    }
}