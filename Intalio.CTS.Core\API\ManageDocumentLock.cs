﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Intalio.Core;
using Intalio.CTS.Core.DAL;
using Microsoft.EntityFrameworkCore;

namespace Intalio.CTS.Core.API;

public static class ManageDocumentLock
{
    /// <summary>
    /// Locks the document by the given userId
    /// </summary>
    /// <param name="documentId">The document id to be locked</param>
    /// <param name="userId">The user id who has triggered this action</param>
    /// <returns></returns>
    public static async Task LockDocumentAsync (long documentId, long userId)
    {
        using var context = new CTSContext();
        DocumentLock newLock = new()
        {
            DocumentId = documentId,
            UserId = userId
        };
        context.DocumentLock.Add(newLock);
        await context.SaveChangesAsync();

        //Now we lock existing entries related to the correspondence
        //Notes
        var notes = await context.Note
        .Where(m => m.DocumentId == documentId && m.DocumentLockId == null)
        .ToListAsync();
        notes.ForEach(m => m.DocumentLockId = newLock.Id);

        //Non archived attachments
        
        var nonArchivedAttachments = await context.NonArchivedAttachments
            .Where(m => m.DocumentId == documentId && m.DocumentLock == null)
            .ToListAsync();
        nonArchivedAttachments.ForEach(m => m.DocumentLockId = newLock.Id);

        //Linked Document
        var linkedDocuments = await context.LinkedDocument
        .Where(m => m.DocumentId == documentId && m.DocumentLockId == null)
        .ToListAsync();
        linkedDocuments.ForEach(m => m.DocumentLockId = newLock.Id);

        //Attachments
        var attachments = await context.Attachment
        .Where(m => m.DocumentId == documentId && m.DocumentLockId == null)
        .ToListAsync();
        attachments.ForEach(m => m.DocumentLockId = newLock.Id);

        ManageActivityLog.AddFullActivityLog(documentId, null, (int) ActivityLogs.SignDocument, userId, "", "");
        await context.SaveChangesAsync();
    }

    /// <summary>
    /// Returns the id of the user that locked the document
    /// </summary>
    /// <param name="documentId">The document id</param>
    /// <returns>The user id returned, might be null if the document is not locked</returns>
    public static async Task<long?> LockedByUserAsync (long documentId)
    {
        using var context = new CTSContext();
        var lastLock = await context.DocumentLock
        .Where(l => l.DocumentId == documentId)
        .OrderByDescending(l => l.LockedAt)
        .FirstOrDefaultAsync();

        return lastLock?.UserId;
    }

    /// <summary>
    /// Unlocks the document
    /// </summary>
    /// <param name="documentId">The id of the document that was locked</param>
    /// <param name="userId">The user id who was responsible for locking the document</param>
    /// <returns>Returns a status corresponding to the unlock if it was successful or failure</returns>
    public static async Task<DocumentLockStatus> UnlockDocument (long documentId, long userId, long structureId, short privacyLevel)
    {
        if (new Document().CheckHasLockedAttachmentsWithOriginal(documentId))
        {
            return DocumentLockStatus.DocumentHasLockedAttachments;
        }
        //After unlocking, we have to revert the original document
        await ManageAttachment.RestoreDocumentBeforeSignAsync(documentId);
        //If the user is unlocking it
        //And the last one who has locked it is him, then
        //it is obvious that he has permission to unlock it
        using var context = new CTSContext();

        var lastLock = await context.DocumentLock
        .Where(l => l.DocumentId == documentId)
        .OrderByDescending(l => l.LockedAt)
        .FirstOrDefaultAsync();

        if (lastLock is null)
        {
            return DocumentLockStatus.DocumentNotLocked;
        }

        //if (lastLock.UserId != userId)
        //{
        //    return DocumentLockStatus.DocumentLockedByDifferentUser;
        //}

        //Now we lock existing entries related to the corrspondence
        //Notes
        var notes = await context.Note
        .Where(m => m.DocumentId == documentId && m.DocumentLockId == lastLock.Id)
        .ToListAsync();
        notes.ForEach(m => m.DocumentLockId = null);

        //Linked Document
        var linkedDocuments = await context.LinkedDocument
        .Where(m => m.DocumentId == documentId && m.DocumentLockId == lastLock.Id)
        .ToListAsync();
        linkedDocuments.ForEach(m => m.DocumentLockId = null);

        //Attachments
        var attachments = await context.Attachment
        .Where(m => m.DocumentId == documentId && m.DocumentLockId == lastLock.Id)
        .ToListAsync();
        attachments.ForEach(m => m.DocumentLockId = null);

        //transfers
        var transfers = await context.Transfer
        .Where(t => t.DocumentId == documentId /*&& t.IsSigned == true*/)
        .ToListAsync();
        transfers.ForEach(t => { 
            
            t.IsSigned = false;
            t.ClosedDate = DateTime.Now;
            t.StatusId = (short)DocumentStatus.Completed; 
            t.SignedByUserId = null;
        
        });
        await context.SaveChangesAsync();
        context.Remove(lastLock);

        ManageActivityLog.AddFullActivityLog(documentId, null, (int) ActivityLogs.UnsignDocument, userId, "", "");

        ManageDocument.UpdateDocumentStatusById(documentId, DocumentStatus.Draft);
        ManageDocument.ChangeDraftStatus(documentId, (int)DraftStatus.Pending,userId, structureId, privacyLevel);

        await context.SaveChangesAsync();
        return DocumentLockStatus.Unlocked;
    }
    /// <summary>
    /// Unlocks the document
    /// </summary>
    /// <param name="documentId">The id of the document that was locked</param>
    /// <param name="userId">The user id who was responsible for locking the document</param>
    /// <param name="structureId"></param>
    /// <param name="privacyLevel"></param>
    /// <param name="language"></param>
    /// <returns>Returns a status corresponding to the unlock if it was successful or failure</returns>
    public static async Task<(bool success,string message)> EditAfterExport(long documentId, long userId, long structureId, short privacyLevel,Language language)
    {
        List<long> incomingDocIds = new();
        var eportedOutgoingTransfer = new Document().FindIncludeTransfers(documentId).Transfer.FirstOrDefault(t => t.IsExported);
        var exportedTransfers = new Transfer().ListByExportDocumentId(documentId);
        if (exportedTransfers.Any(t => t.RequestStatus == (long)RequestStatuses.Pending))
        {
            return (false, TranslationUtility.Translate("ThereIsAnotherTransferPending", language));
        }
        var returnedTransfers = exportedTransfers.Where(t => t.RequestStatus != (long)RequestStatuses.Accepted).ToList();
        incomingDocIds = returnedTransfers.Select(t => t.DocumentId ?? 0).ToList();
        await ManageDocument.Delete(userId, structureId, incomingDocIds, true);
        ManageDocument.Reopen(new List<long> { documentId });
        var unlockRes = await UnlockDocument(documentId, userId, structureId, privacyLevel);
        if (unlockRes == DocumentLockStatus.Unlocked)
        {
            ManageTransfer.UpdateExportedDate(eportedOutgoingTransfer, false, null);
            ManageActivityLog.AddActivityLog(documentId, null, (int)ActivityLogs.EditAfterExport, userId);
        }
        return (unlockRes == DocumentLockStatus.Unlocked ? true : false, "");
    }

}
