﻿import Intalio from './common.js'
import DocumentDetails from './documentDetails.js'
import LinkedCorrespondenceIndex from './linkedCorrespondenceIndex.js'
import LinkedCorrespondenceDocument from './linkedCorrespondenceDocument.js'
import ReportCorrespondenceDetailExport from './reportCorrespondenceDetailExport.js'
class MoveTransfers extends Intalio.Model
{
    constructor()
    {
        super();
        this.categories = null;
        this.statuses = null;
        this.importances = null;
        this.priorities = null;
        this.privacies = null;
        this.documentId = null;
        this.fromLink = false;
    }
}
var table = null;
var firstTime = true;
var gLocked = false;
var gTableName = "grdMoveTransfersItems";
var gFromLink = false;
function isFilled()
{
    if (   $("#cmbMoveTransfersFilterFromUser").val() === null
        || $("#cmbMoveTransfersFilterToUser").val() === null
        || $("#cmbMoveTransfersFilterStructure").val() === null )
    {
        return false;
    }
    return true;
}
function MigrateTransfers(model) {
    let newParams = {
        'fromUser': $("#cmbMoveTransfersFilterFromUser").val() === null ? '0' : $("#cmbMoveTransfersFilterFromUser").val(),
        'toUser': $("#cmbMoveTransfersFilterToUser").val() === null ? '0' : $("#cmbMoveTransfersFilterToUser").val(),
        'structure': $("#cmbMoveTransfersFilterStructure").val() === null ? '0' : $("#cmbMoveTransfersFilterStructure").val(),
        '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
    };

    Common.ajaxPost('/Transfer/MoveTransfers', newParams,
    function (response) {
        if (response && response.status) {
            Common.showScreenSuccessMsg();
            GridCommon.Refresh(gTableName);

        }
        else {
            Common.alertMsg(response.message);
        }
    }, null, false);




}
function CheckTransfersForMoving() {
        if (isFilled()) {
            Common.mask(document.getElementById('grdMoveTransfersItems'), "MoveTransfersItemsContainer-mask");
            let params = {
                'fromUser': $("#cmbMoveTransfersFilterFromUser").val() === null ? '0' : $("#cmbMoveTransfersFilterFromUser").val(),
                'toUser': $("#cmbMoveTransfersFilterToUser").val() === null ? '0' : $("#cmbMoveTransfersFilterToUser").val(),
                'structure': $("#cmbMoveTransfersFilterStructure").val() === null ? '0' : $("#cmbMoveTransfersFilterStructure").val(),
                '__RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
            };

            Common.ajaxPost('/Transfer/CheckTransfersForMoving', params,
                function (result) {
                    Common.unmask("MoveTransfersItemsContainer-mask");

                    if (result && result.status)
                    {
                        if (result.message =='NoAttachmentsFound') {
                                Common.showConfirmMsg(Resources.MigrateTransfersConfirmation, function () {
                                    MigrateTransfers(result);

                                });
                            
                        }
                        else if (result.message == 'FilesNeededToDiscardCheckOutByViewerAdmin') {
                            Common.showConfirmMsg(Resources.FilesNeededToDiscardCheckOutByViewerAdmin, function () {
                                MigrateTransfers(result);

                            });
                        }
                       

                    }
                    else {
                        Common.alertMsg(result.message);
                    }
                }, null, false);
        } else
        {
            Common.unmask("MoveTransfersItemsContainer-mask");

            Common.alertMsg(Resources.FillSearchCritirea);
        }
   
    
}
function getTransfers(self)
{
    
    if (firstTime)
    {
        var columns = [];
        Common.gridCommon();
        var securityMatrix = JSON.parse(new CoreComponents.Lookup.SecurityMatrix().get(window.language));// JSON.parse(sessionStorage.getItem("SecurityMatrix"));
        var buttons = SecurityMatrix.getToolbarActions(securityMatrix, TreeNodes.Search, gTableName);
        buttons.push({
            className: 'btn-sm btn-primary',
            text: /*'<em class="fa fa-check-square-o mr-sm"></em>' +*/ Resources.Migrate,
            action: function (e, table, node, config) {
                
                CheckTransfersForMoving()
                
            }
        });
        var showCheckBoxes = self.model.fromLink || buttons.length > 0;
        //columns.push({
        //    visible: showCheckBoxes,
        //    title: '<input id="chkAll" type="checkbox" />',
        //    width: '16px',
        //    "orderable": false,
        //    "render": function (data, type, row)
        //    {
        //        var searchSelectedDocIds = GridCommon.GetSelectedRows(gTableName);
        //        var index = searchSelectedDocIds.indexOf(row.id);
        //        if (index > -1)
        //        {
        //            return "<input type='checkbox' checked=true onclick='event.stopPropagation();' data-id=" + row.id + " />";
        //        }
        //        else
        //        {
        //            return "<input type='checkbox' onclick='event.stopPropagation();' data-id=" + row.id + " />";
        //        }
        //    }
        //});
        columns.push({
            "className": 'details-control',
            "orderable": false,
            "data": null,
            "defaultContent": '',
            width: '16px'
        });
        columns.push({ title: "Id", data: "id", visible: false, "orderable": false });
        columns.push({
            title: Resources.Category, data: "categoryId", "orderable": true, orderSequence: ["asc", "desc"],
            render: function (data, type, full, meta)
            {
                var categories = self.model.categories;
                for (var i = 0; i < categories.length; i++)
                {
                    if (categories[i].id === data)
                    {
                        return categories[i].text;
                    }
                }
                return "";
            }
        });
        columns.push({ title: Resources.ReferenceNumber, data: "referenceNumber", render: $.fn.dataTable.render.text(), "orderable": true, orderSequence: ["asc", "desc"] });
        columns.push({ title: Resources.Subject, data: "subject", render: $.fn.dataTable.render.text(), "orderable": true, orderSequence: ["asc", "desc"] });
        columns.push({
            title: Resources.CreatedDate, data: "createdDate", "orderable": true, orderSequence: ["asc", "desc"],
            render: function (data, type, full, meta)
            {
                return DateConverter.toHijriFormated(full.createdDate, null, window.CalendarType);
            }
        });
        columns.push({
            title: Resources.Status, data: "statusId", "orderable": false, width: "50px",
            "render": function (data, type, full, meta)
            {
                var statuses = self.model.statuses;
                for (var i = 0; i < statuses.length; i++)
                {
                    if (statuses[i].id === data)
                    {
                        return "<div class='label' style='background-color:" + (statuses[i].color !== null ? statuses[i].color : "#27c24c") + "'>" + statuses[i].text + "</div>";
                    }
                }
                return "";
            }
        });
        //columns.push({
        //    "className": "text-right",
        //    "autoWidth": false,
        //    "bAutoWidth": false,
        //    width: "16px",
        //    'orderable': false,
        //    'sortable': false,
        //    'render': function (data, type, full, meta)
        //    {
        //        var delegationId = null;
        //        let btnView = document.createElement("button");
        //        btnView.setAttribute("class", "btn btn-xs btn-warning mr-sm view");
        //        btnView.setAttribute("title", Resources.View);
        //        btnView.setAttribute("type", "button");
        //        btnView.setAttribute("clickattr", "openSearchDocument(" + full.documentId + "," + delegationId + "," + self.model.fromLink + ")");
        //        btnView.innerHTML = "<i class='fa fa-eye fa-white'/>";
        //        return btnView.outerHTML;
        //    }
        //});
        if (!gFromLink)
        {
            SecurityMatrix.getRowActions(securityMatrix, columns, TreeNodes.Search);
        }
        table = $("#grdMoveTransfersItems")
            .on('draw.dt', function ()
            {
                
                $('#grdMoveTransfersItems tbody tr td').each(function ()
                {
                    this.setAttribute('title', $(this).text());
                });
                GridCommon.CheckSelectedRows(gTableName);
            })
            .DataTable({
                processing: true,
                ordering: true,
                serverSide: true,
                pageLength: 10,
                "ajax": {
                    "url": "/Transfer/PreviewTransfersForMoving",
                    "type": "POST",
                    "datatype": "json",
                    "data": function (d)
                    {
                        
                        var model = {};
                        model.fromUser = $("#cmbMoveTransfersFilterFromUser").val() === null ? '0' : $("#cmbMoveTransfersFilterFromUser").val();
                        model.toUser = $("#cmbMoveTransfersFilterToUser").val() === null ? '0' : $("#cmbMoveTransfersFilterToUser").val();
                        model.structure = $("#cmbMoveTransfersFilterStructure").val() === null ? '0' : $("#cmbMoveTransfersFilterStructure").val();
                      
                        d.__RequestVerificationToken = $('input[name="__RequestVerificationToken"]').val();
                        d.Model = JSON.stringify(model);
                        return d;
                    },
                    "dataSrc": function (response)
                    {
                        
                        $('#grdMoveTransfersItems_processing').css('display', 'none');
                        if (response.recordsTotal > 0)
                        {
                            return response.data;
                        } else
                        {
                            if (response.message != undefined && response.message != "")
                            {
                                Common.showScreenErrorMsg(response.message);
                            }
                            response.data = [] //since datatables will be checking for the object as array
                            return response.data;
                        }
                    }
                },
                "order": [],
                "columns": columns,
                "fnInitComplete": function (settings, json)
                {
                    gLocked = false;
                    $('[data-toggle="tooltip"]').tooltip();
                },
                dom: '<"html5buttons"B>ltrpi',
                buttons: !gFromLink ? buttons : []
            });

            
        if (showCheckBoxes)
        {
            GridCommon.AddCheckBoxEvents(gTableName);
        }
        //$('#grdMoveTransfersItems tbody').on('click', ".view", function ()
        //{
        //    var onclick = $(this).attr("clickattr");
        //    eval(onclick);
        //});
        //$('#grdMoveTransfersItems tbody').on('dblclick', 'tr', function ()
        //{
        //    var onclick = $(this).find(".view").attr("clickattr");
        //    eval(onclick);
        //});
        $('#grdMoveTransfersItems tbody').on('click', 'td.details-control', function ()
        {
            let tr = $(this).closest('tr');
            let row = table.row(tr);
            if (row.child.isShown())
            {
                row.child.hide();
                tr.removeClass('shown');
            }
            else
            {
                var details = format(row.data(), self.model.importances, self.model.priorities, self.model.privacies)

                row.child(details).show();
                tr.addClass('shown');
            }
        });
        table.$('td').tooltip({
            "delay": 0,
            "track": true,
            "fade": 250
        });
        if (!gFromLink)
        {
            SecurityMatrix.InitToolbarColor();
            SecurityMatrix.InitContextMenu(securityMatrix, TreeNodes.Search);
        }
    } else
    {
        GridCommon.Refresh(gTableName);
        gLocked = false;
    }
    firstTime = false;
}
function format(row, importances, priorities, privacies)
{
    var importance = "", priority = "", privacy = "";
    for (let i = 0; i < priorities.length; i++)
    {
        if (priorities[i].id === row.priorityId)
        {
            priority = priorities[i].text;
        }
    }
    for (let i = 0; i < privacies.length; i++)
    {
        if (privacies[i].id === row.privacyId)
        {
            privacy = privacies[i].text;
        }
    }
    for (let i = 0; i < importances.length; i++)
    {
        if (importances[i].id === row.importanceId)
        {
            importance = importances[i].text;
        }
    }
    return '<table style="width:100%" cellspacing="0" border="0">' +
        '<tr>' +
        '<th style="width: 10%;padding:5px">' + Resources.SendingEntity + ':</th>' +
        '<td style="width: 90%;padding:5px;word-break: break-all;"">' + (row.sendingEntity || '') + '</td>' +
        '</tr>' +
        '<tr>' +
        '<th style="width: 10%;padding:5px">' + Resources.ReceivingEntity + ':</th>' +
        '<td style="width: 90%;padding:5px;word-break: break-all;"">' + (row.receivingEntity || '') + '</td>' +
        '</tr>' +
        '<tr>' +
        '<th style="width: 10%;padding:5px">' + Resources.Priority + ':</th>' +
        '<td style="width: 90%;padding:5px;word-break: break-all;"">' + priority + '</td>' +
        '</tr>' +
        '<tr>' +
        '<th style="width: 10%;padding:5px">' + Resources.Privacy + ':</th>' +
        '<td style="width: 90%;padding:5px;word-break: break-all;"">' + privacy + '</td>' +
        '</tr>' +
        '<tr>' +
        '<th style="width: 10%;padding:5px">' + Resources.Importance + ':</th>' +
        '<td style="width: 90%;padding:5px;word-break: break-all;"">' + importance + '</td>' +
        '</tr>' +
        '<tr>' +
        '<th style="width: 10%;padding:5px">' + Resources.CreatedBy + ':</th>' +
        '<td style="width: 90%;padding:5px;word-break: break-all;"">' + (row.createdByUser || '') + '</td>' +
        '</tr>' +
        '<tr>' +
        '<th style="width: 10%;padding:5px">' + Resources.DueDate + ':</th>' +
        '<td style="width: 90%;padding:5px;word-break: break-all;"">' + (DateConverter.toHijriFormated(row.dueDate, null, window.CalendarType) || '') + '</td>' +
        '</tr>' +
        '</table>';
}
function openSearchDocument(id, delegationId, fromLink)
{
    if (true)
    {
        var params = { id: id };
        if (delegationId !== null)
        {
            params.delegationId = delegationId;
        }
        Common.ajaxGet('/Document/GetSearchDocument', params, function (response)
        {
            if (response && response === "NoAccess")
            {
                Common.alertMsg(Resources.NoPermission);
            } else
            {
                if (!response.id)
                {
                    return;
                }
                var wrapper = $(".modal-window");
                var model = new LinkedCorrespondenceIndex.LinkedCorrespondenceIndex();
                var linkedCorrespondenceDocument = new LinkedCorrespondenceDocument.LinkedCorrespondenceDocumentView(wrapper, model);
                linkedCorrespondenceDocument.render();

                model = new DocumentDetails.DocumentDetails();
                model.documentModel = response;
                model.readonly = true;
                model.delegationId = delegationId;
                model.documentId = response.id;
                model.referenceNumber = response.referenceNumber;
                model.categoryName = response.categoryName;
                model.statusId = response.status;
                model.createdByUser = response.createdByUser;
                model.statuses = new CoreComponents.Lookup.Statuses().get(window.language);
                model.showMyTransfer = false;
                model.showVisualTrackingOnly = false;
                var securityMatrix = JSON.parse(new CoreComponents.Lookup.SecurityMatrix().get(window.language));// JSON.parse(sessionStorage.getItem("SecurityMatrix"));
                var tabs = [];
                var nodeId = /*$('[data-inherit="' + TreeNode.Inbox + '"]').first().data("id")*/ TreeNodes.Search;
                if (nodeId !== undefined && $.isNumeric(nodeId))
                {
                    tabs = securityMatrix.SecurityNodes[nodeId].SecurityCategories[response.categoryId].Tabs;
                    model.tabsActions = securityMatrix.SecurityNodes[nodeId].SecurityCategories[response.categoryId].SecurityTabs;
                }
                model.tabs = $.grep(tabs, function (element, index)
                {
                    return !element.Name.includes("myTransfer") && !element.Name.includes("documentMetadata") &&
                        !element.Name.includes("notes") &&
                        !element.Name.includes("visualTracking") && !element.Name.includes("activityLog") &&
                        !element.Name.includes("linkedDocument") && !element.Name.includes("transferHistory") && !element.Name.includes("attachments");
                });
                model.tabsWithStatic = tabs;
                model.showBackButton = false;
                model.isModal = true;
                model.attachmentId = response.attachmentId;
                model.attachmentVersion = response.attachmentVersion;
                wrapper = $(linkedCorrespondenceDocument.refs['linkDocumentDiv']);
                var view = new DocumentDetails.DocumentDetailsView(wrapper, model);
                view.render();

                var title = response.categoryName;
                if (response.referenceNumber)
                {
                    title += ' - ' + response.referenceNumber;
                }
                if (response.createdByUser)
                {
                    title += ' - ' + response.createdByUser;
                }
                $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocumentTitle']).html(title);
                $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).off("hidden.bs.modal");
                $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).off("shown.bs.modal");
                $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).on('shown.bs.modal', function ()
                {
                    $("ul[ref='tabDocumentDetails']").scrollingTabs('refresh');
                });
                $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).on('hidden.bs.modal', function ()
                {
                    if ($(this).data("remove") != true)
                        return;
                    $(linkedCorrespondenceDocument.refs[linkedCorrespondenceDocument.model.ComponentId]).remove();
                    swal.close();
                    //if ($(".modal-window").children().length > 0 || $(".modal-documents").children().length > 0)
                    //{
                    //    $('body').addClass('modal-open');
                    //}
                });
                $(linkedCorrespondenceDocument.refs['modalLinkedCorrespondenceDocument']).modal("show");

            }
        }, function () { Common.showScreenErrorMsg(); }, true);
    }
    else
    {
        var params = { id: id };
        if (delegationId !== null)
        {
            params.delegationId = delegationId;
        }
        Common.ajaxGet('/Document/GetSearchDocument', params, function (response)
        {
            if (response && response === "NoAccess")
            {
                Common.alertMsg(Resources.NoPermission);
            } else
            {
                Common.setActiveSidebarMenu("liSearch");
                $(".delegation").removeClass("active");
                $("#searchContainerDiv").hide();

                var model = new DocumentDetails.DocumentDetails();
                model.documentModel = response;
                model.delegationId = delegationId;
                model.documentId = response.id;
                model.referenceNumber = response.referenceNumber;
                model.categoryName = response.categoryName;
                model.statusId = response.status;
                model.documentModel = response;
                model.readonly = true;
                model.statuses = new CoreComponents.Lookup.Statuses().get(window.language);
                model.showMyTransfer = false;
                model.fromSearch = true;
                model.attachmentId = response.attachmentId;
                model.attachmentVersion = response.attachmentVersion;
                var securityMatrix = JSON.parse(new CoreComponents.Lookup.SecurityMatrix().get(window.language));// JSON.parse(sessionStorage.getItem("SecurityMatrix"));
                var tabs = securityMatrix.SecurityNodes[Number(TreeNodes.Search)].SecurityCategories[response.categoryId].Tabs;
                model.tabs = $.grep(tabs, function (element, index)
                {
                    return !element.Name.includes("myTransfer") && !element.Name.includes("documentMetadata") &&
                        !element.Name.includes("transferHistory") && !element.Name.includes("visualTracking") &&
                        !element.Name.includes("activityLog") && !element.Name.includes("attachments") && !element.Name.includes("notes") &&
                        !element.Name.includes("linkedDocument") && !element.Name.includes("nonArchivedAttachments");
                });
                model.tabsWithStatic = tabs;
                model.tabsActions = securityMatrix.SecurityNodes[Number(TreeNodes.Search)].SecurityCategories[response.categoryId].SecurityTabs;
                var wrapper = $(".content-wrapper");
                var view = new DocumentDetails.DocumentDetailsView(wrapper, model);
                view.render();
                if (!gFromLink)
                {
                    $(document).off('click', '.btn-back');
                    $(document).on('click', '.btn-back', function ()
                    {
                        $("#searchContainerDiv").show();
                        view.remove();
                        $(".toRemove").remove();
                    });

                    $(document).off('click', '.btn-export');
                    $(document).on('click', '.btn-export', function ()
                    {
                        var wrapper = $(".modal-window");
                        var model = new ReportCorrespondenceDetailExport.ReportCorrespondenceDetailExport();
                        model.documentId = response.id;
                        model.delegationId = delegationId;
                        var reportCorrespondenceDetailExportView = new ReportCorrespondenceDetailExport.ReportCorrespondenceDetailExportView(wrapper, model);
                        reportCorrespondenceDetailExportView.render();

                        $("#modalReportCorrespondenceDetailExport").off("hidden.bs.modal");
                        $("#modalReportCorrespondenceDetailExport").off("shown.bs.modal");
                        $("#modalReportCorrespondenceDetailExport").on('shown.bs.modal', function ()
                        { });
                        $("#modalReportCorrespondenceDetailExport").on('hidden.bs.modal', function ()
                        { });
                        $("#modalReportCorrespondenceDetailExport").modal("show");
                    });
                }
            }
        }, function () { Common.showScreenErrorMsg(); }, true);
    }
}
function createFromUserSelect2()
{
    
    var headers = {};
    var fromUserUrl = window.IdentityUrl + '/api/SearchUsers';
    headers.Authorization = 'Bearer ' + window.IdentityAccessToken;
    $("#cmbMoveTransfersFilterFromUser").select2({
        minimumInputLength: 0,
        allowClear: true,
        placeholder: "",
        dir: window.language === "ar" ? "rtl" : "ltr",
        language: window.language,
        dropdownParent: $('#fromUserMoveTransfersFilterContainer'),
        width: "100%",
        ajax: {
            delay: 250,
            url: fromUserUrl,
            type: "Get",
            dataType: 'json',
            headers: typeof headers !== "undefined" ? headers : "",
            data: function (term)
            {
                //return { "text":  "", "language": window.language };
                return { "text": term.term ? term.term : "", "language": window.language };
            },
            processResults: function (data, term)
            {
                return {
                    results: userDataForSelect2(data, term)
                };
            }
        }
    });
    $("#cmbMoveTransfersFilterFromUser").val('').trigger('change');
}
function createToUserSelect2(toStructure) {
    
    var headers = {};
    var toUserUrl = window.IdentityUrl + '/Api/ListUsersByStructureId?id=' + toStructure + '&showOnlyActiveUsers=true';
    headers.Authorization = 'Bearer ' + window.IdentityAccessToken;

    var toUserSelect2Instance = $("#cmbMoveTransfersFilterToUser").data('select2');
    if (toUserSelect2Instance) {
        $("#cmbMoveTransfersFilterToUser").select2('destroy');
    }

    $("#cmbMoveTransfersFilterToUser").select2({
        minimumInputLength: 0,
        allowClear: true,
        placeholder: "",
        dir: window.language === "ar" ? "rtl" : "ltr",
        language: window.language,
        dropdownParent: $('#toUserMoveTransfersFilterContainer'),
        width: "100%",
        ajax: {
            delay: 250,
            url: toUserUrl,
            type: "Get",
            dataType: 'json',
            headers: typeof headers !== "undefined" ? headers : "",
            data: function (term) {
                //return { "text":  "", "language": window.language };
                return { "text": term.term ? term.term : "", "language": window.language };
            },
            processResults: function (data, term) {
                return {
                    results: userDataForSelect2(data, term)
                };
            }
        }
    });
    $("#cmbMoveTransfersFilterToUser").val('').trigger('change');
}

function userDataForSelect2(data, term)
{
    var termSearch = term.term ? term.term : "";
    var retVal = [];
    $.each(data, function (key, val) {
        
        var fullName = val.fullName;
        if (window.language != 'en') {
            fullName = getFullNameByLangauge(val);
            fullName = fullName.trim() == "" ? val.fullName : fullName;
        }
        var allNames = getFullNameInAllLangauge(val);
        if (allNames.length == 0) allNames.push(fullName);
        if (termSearch != "" &&
            !allNames.some(r => r.toLowerCase().includes(termSearch.toLowerCase()))) {
            return;
        }
        let isExist = retVal.some(function (usr) { return usr.id === val.id; });
        if (!isExist) {
            var item = {};
            item.id = val.id;
            item.text = fullName;
            item.isStructure = false;
            item.dataId = val.id;
            retVal.push(item);
        }
    });
    return retVal;
}
function createStructuresSelect2()
{
    
    var headers = {};
    var url = window.IdentityUrl + '/api/SearchStructuresWithSearchAttributes';
    headers.Authorization = 'Bearer ' + window.IdentityAccessToken;
    $("#cmbMoveTransfersFilterStructure").select2({
        minimumInputLength: 0,
        allowClear: true,
        placeholder: "",
        dir: window.language === "ar" ? "rtl" : "ltr",
        language: window.language,
        dropdownParent: $('#StructureMoveTransfersFilterContainer'),
        width: "100%",
        ajax: {
            delay: 250,
            url: url,
            type: "POST",
            dataType: 'json',
            headers: typeof headers !== "undefined" ? headers : "",
            data: function (term)
            {
                return { "text": term.term ? term.term : "", "attributes": [window.StructureNameAr, window.StructureNameFr] };
            },
            processResults: function (data)
            {
                return {
                    results: structureDataForSelect2(data)
                };
            }
        }
    });
    $("#cmbMoveTransfersFilterStructure").val('').trigger('change');

}
function getStructureName(data)
{
    var structureName = data.name;
    if (data.attributes != null && data.attributes.length > 0)
    {
        var attributeLang = $.grep(data.attributes, function (e)
        {
            return e.text === window.StructureNameAr && window.language === "ar" ? e.value : (e.text === window.StructureNameFr && window.language === "fr" ? e.value : "");
        });
        if (attributeLang.length > 0)
        {
            structureName = attributeLang[0].value === "" ? structureName : attributeLang[0].value;
        }
    }
    return structureName;
}
function structureDataForSelect2(data)
    {
    var retVal = [];
    if (typeof data !== 'undefined' && data.items)
    {
        for (var i = 0; i < data.items.length; i++)
        {
            retVal.push({
                id: data.items[i].id,
                text: getStructureName(data.items[i])
            });
        }
    } else if (data)
    {
        for (var i = 0; i < data.length; i++)
        {
            retVal.push({
                id: data[i].id,
                text: getStructureName(data[i])
            });
        }
    }
    return retVal;
}
class MoveTransfersView extends Intalio.View
{
    constructor(element, model)
    {
        super(element, "movetransfers", model);
    }
    render()
    {
        
        var self = this;
        gFromLink = self.model.fromLink;
        $.fn.select2.defaults.set("theme", "bootstrap");
        firstTime = true;

        $('#formPost').keydown(function (e)
        {
            if (!$(e.target).parent().hasClass('bootstrap-tagsinput'))
            {
                var code = e.keyCode || e.which;
                if (code === 13)
                {
                    e.preventDefault();
                    $('#btnFilterMoveTransfersPreview').trigger("click");
                }
            }
        });

        createStructuresSelect2();
        createFromUserSelect2()
        createToUserSelect2('0')


        $("#cmbMoveTransfersFilterStructure").on('change', function () {
            
            //// Get the new value of cmbMoveTransfersFilterStructure
            var toStructure = $(this).val();

            createToUserSelect2(toStructure);

        });


        $("#btnFilterMoveTransfersPreview").on('click', function (e)
        {
            if (isFilled())
            {
                if (!gLocked)
                {
                    gLocked = true;
                    try
                    {
                        $("#grdMoveTransfersItems tbody").empty();
                        getTransfers(self);
                        //$('.search').hide();
                        //$("#expandIcon").show();
                        //$('.btn-scroll').fadeIn();
                        //$('.gridResult').fadeIn();
                        //$("#expandIcon").removeClass("fa-compress").addClass("fa-expand");
                        //$("#resultExpandIcon").removeClass("fa-compress").addClass("fa-expand");
                    } catch (e)
                    {
                        gLocked = false;
                    }
                }
            } else
            {
                Common.alertMsg(Resources.FillSearchCritirea);
            }
        });

        $("#btnFilterMoveTransfersClear").on('click', function ()
        {
            $("#cmbMoveTransfersFilterFromUser").val('').trigger('change');
            $("#cmbMoveTransfersFilterToUser").val('').trigger('change');
            $("#cmbMoveTransfersFilterStructure").val('').trigger('change');
            
            //$('.with-border').addClass('without-border').removeClass('with-border');
            //$("#expandIcon").hide();
            //$("#grdMoveTransfersItems tbody").empty();
        });

        getTransfers(self)
        
    }
}
export default { MoveTransfers, MoveTransfersView };
