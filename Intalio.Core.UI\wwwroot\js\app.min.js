function getParentOfParentElement(n){return $(n.parent().parent())}function convertDateToHijri(n,t){var s,a,h,f;if(n===undefined)return{date:new Date,nYear:"",nMonth:"",nDay:"",nHour:"",nMinute:"",nSecond:""};if(t===CalendarTypes.None||t===undefined)return{date:n,nYear:n.getFullYear(),nMonth:n.getMonth+1,nDay:n.getDate(),nHour:n.getHours(),nMinute:n.getMinutes(),nSecond:n.getSeconds()};var o=n.getFullYear(),v=n.getMonth(),y=n.getDate(),i,r,e,c,u,l=function(n){return n%4==0&&n%100!=0||n%400==0},p=function(n){return(n-1)*354+Math.floor((3+11*n)/30)},w=function(n,t){return Math.ceil(29.5*t)+(n-1)*354+Math.floor((3+11*n)/30)},b=function(n){return n.getTime()<gFirstRefForUAQ.getTime()||n.getTime()>gLastRefForUAQ.getTime()?(console.log("You operate with the dates not suitable for current implementation of 'Umm al-Qura' calendar.\nCalendar is switched to 'Civil'"),!1):!0},k=function(n){for(var i=50,t=1883;t<n.getFullYear();t++)i+=l(t)?366:365;for(t=0;t<n.getMonth();t++)i+=DAYS_IN_GR_MONTH[t],t==1&&l(n.getFullYear())&&i++;return i+n.getDate()};if(t===CalendarTypes.Civil||t===CalendarTypes.Tabular)c=Math.floor(1721425.5-1+365*(o-1)+Math.floor((o-1)/4)+-Math.floor((o-1)/100)+Math.floor((o-1)/400)+Math.floor((367*(v+1)-362)/12+(v+1<=2?0:l(o)?-1:-2)+y))+.5,u=t===CalendarTypes.Tabular?c-1948438.5:c-1948439.5,i=Math.floor((30*u+10646)/10631),r=Math.ceil((u-29-p(i))/29.5),r=Math.min(r,11),e=Math.ceil(u-w(i,r)+1);else if(t===CalendarTypes.UmmAlQura){if(!b(n))return convertDateToHijri(n,CalendarTypes.Civil);for(s=k(n),i=1300,e=0,r=0,a=!1,h=0;h<UAQ_MONTH_LENGTH.length;h++,i++){for(f=0;f<12;f++){if(u=parseInt(UAQ_MONTH_LENGTH[h][f])+29,s<=u){e=s;e>u&&(e=1,f++);f>11&&(f=0,i++);r=f;a=!0;break}s-=u}if(a)break}}return{date:new Date(i,r,e,n.getHours(),n.getMinutes(),n.getSeconds()),nYear:i,nMonth:r+1,nDay:e,nHour:n.getHours(),nMinute:n.getMinutes(),nSecond:n.getSeconds()}}function pad(n,t){return t===void 0&&(t=2),("000"+n).slice(t*-1)}function getStrDate(n,t,i,r,u,f,e){var s=r+"/"+i+"/"+t,o;return n.toLowerCase()==="dd/mm/yyyy hh:mm"?s=r+"/"+i+"/"+t+" "+u+":"+f:n.toLowerCase()==="dd/mm/yyyy hh:mm:ss"?s=r+"/"+i+"/"+t+" "+u+":"+f+":"+e:n.toLowerCase()==="dd/mm/yyyy hh:mm a"?(o="",u>12?(o=" PM",u=u-12):o=" AM",s=r+"/"+i+"/"+t+" "+u+":"+f+o):n.toLowerCase()==="dd/mm/yyyy hh:mm:ss a"?(o="",u>12?(o=" PM",u=u-12):o=" AM",s=r+"/"+i+"/"+t+" "+u+":"+f+":"+e+o):n.toLowerCase()==="dd-mm-yyyy"?s=r+"-"+i+"-"+t:n.toLowerCase()==="dd-mm-yyyy hh:mm"?s=r+"-"+i+"-"+t+" "+u+":"+f:n.toLowerCase()==="dd-mm-yyyy hh:mm:ss"?s=r+"-"+i+"-"+t+" "+u+":"+f+":"+e:n.toLowerCase()==="dd-mm-yyyy hh:mm a"?(o="",u>12?(o=" PM",u=u-12):o=" AM",s=r+"-"+i+"-"+t+" "+u+":"+f+o):n.toLowerCase()==="dd-mm-yyyy hh:mm:ss a"?(o="",u>12?(o=" PM",u=u-12):o=" AM",s=r+"-"+i+"-"+t+" "+u+":"+f+":"+e+o):n.toLowerCase()==="yyyy-mm-dd"?s=t+"-"+i+"-"+r:n.toLowerCase()==="yyyy-mm-dd hh:mm"?s=t+"-"+i+"-"+r+" "+u+":"+f:n.toLowerCase()==="yyyy-mm-dd hh:mm:ss"?s=t+"-"+i+"-"+r+" "+u+":"+f+":"+e:n.toLowerCase()==="yyyy-mm-dd hh:mm a"?(o="",u>12?(o=" PM",u=u-12):o=" AM",s=t+"-"+i+"-"+r+" "+u+":"+f+o):n.toLowerCase()==="yyyy-mm-dd hh:mm:ss a"?(o="",u>12?(o=" PM",u=u-12):o=" AM",s=t+"-"+i+"-"+r+" "+u+":"+f+":"+e+o):n.toLowerCase()==="mm/dd/yyyy"?s=i+"/"+r+"/"+t:n.toLowerCase()==="mm/dd/yyyy hh:mm"?s=i+"/"+r+"/"+t+" "+u+":"+f:n.toLowerCase()==="mm/dd/yyyy hh:mm:ss"?s=i+"/"+r+"/"+t+" "+u+":"+f+":"+e:n.toLowerCase()==="mm/dd/yyyy hh:mm a"?(o="",u>12?(o=" PM",u=u-12):o=" AM",s=i+"/"+r+"/"+t+" "+u+":"+f+o):n.toLowerCase()==="mm/dd/yyyy hh:mm:ss a"?(o="",u>12?(o=" PM",u=u-12):o=" AM",s=i+"/"+r+"/"+t+" "+u+":"+f+":"+e+o):n.toLowerCase()==="mm-dd-yyyy"?s=i+"-"+r+"-"+t:n.toLowerCase()==="mm-dd-yyyy hh:mm"?s=i+"-"+r+"-"+t+" "+u+":"+f:n.toLowerCase()==="mm-dd-yyyy hh:mm:ss"?s=i+"-"+r+"-"+t+" "+u+":"+f+":"+e:n.toLowerCase()==="mm-dd-yyyy hh:mm a"?(o="",u>12?(o=" PM",u=u-12):o=" AM",s=i+"-"+r+"-"+t+" "+u+":"+f+o):n.toLowerCase()==="mm-dd-yyyy hh:mm:ss a"&&(o="",u>12?(o=" PM",u=u-12):o=" AM",s=i+"-"+r+"-"+t+" "+u+":"+f+":"+e+o),s}function getDaysInNationalMonth(n,t,i){return i===CalendarTypes.None?n===1&&t%4==0&&(t%100!=0||t%400==0)?29:DAYS_IN_GR_MONTH[n]:i===CalendarTypes.UmmAlQura?29+parseInt(UAQ_MONTH_LENGTH[t-1300][n]):n%2==0?30:n===11?civilMonth12Length[(t-1)%30]===355?30:29:29}function convertToNational(n,t){var s,a,h,r,v;if(t===CalendarTypes.None||n===undefined)return n;var o=n.getFullYear(),y=n.getMonth(),p=n.getDate(),u,f,e,c,i,l=function(n){return n%4==0&&n%100!=0||n%400==0},w=function(n){return(n-1)*354+Math.floor((3+11*n)/30)},b=function(n,t){return Math.ceil(29.5*t)+(n-1)*354+Math.floor((3+11*n)/30)},k=function(n){return n.getTime()<gFirstRefForUAQ.getTime()||n.getTime()>gLastRefForUAQ.getTime()?(console.log("You operate with the dates not suitable for current implementation of 'Umm al-Qura' calendar.\nCalendar is switched to 'Civil'"),!1):!0},d=function(n){for(var i=50,t=1883;t<n.getFullYear();t++)i+=l(t)?366:365;for(t=0;t<n.getMonth();t++)i+=DAYS_IN_GR_MONTH[t],t==1&&l(n.getFullYear())&&i++;return i+n.getDate()};if(t===CalendarTypes.Civil||t===CalendarTypes.Tabular)c=Math.floor(1721425.5-1+365*(o-1)+Math.floor((o-1)/4)+-Math.floor((o-1)/100)+Math.floor((o-1)/400)+Math.floor((367*(y+1)-362)/12+(y+1<=2?0:l(o)?-1:-2)+p))+.5,i=t===CalendarTypes.Tabular?c-1948438.5:c-1948439.5,u=Math.floor((30*i+10646)/10631),f=Math.ceil((i-29-w(u))/29.5),f=Math.min(f,11),e=Math.ceil(i-b(u,f)+1);else if(t===CalendarTypes.UmmAlQura){if(!k(n))return convertToNational(n,CalendarTypes.Civil);for(s=d(n),u=1300,e=0,f=0,a=!1,h=0;h<UAQ_MONTH_LENGTH.length;h++,u++){for(r=0;r<12;r++){if(i=parseInt(UAQ_MONTH_LENGTH[h][r])+29,s<=i){e=s;e>i&&(e=1,r++);r>11&&(r=0,u++);f=r;a=!0;break}s-=i}if(a)break}}return v=new Date(0),v.setTime(n.getTime()),saveNationalDate(v,u,f,e)}function saveNationalDate(n,t,i,r){var u=new Date(0);return u.setTime(n.getTime()),{date:u,nYear:t,nMonth:i,nDay:r}}var AuditAction,AuditModule;window.Seperator="/";window.Splitter=",";window.TimeStep="5";window.DefaultColor="#00AE8D";var DAYS_IN_GR_MONTH=[31,28,31,30,31,30,31,31,30,31,30,31],gFirstRefForUAQ=new Date(1882,10,12,0,0,0,0),gLastRefForUAQ=new Date(2174,10,25,23,59,59,999),ONE_DAY=864e5,ONE_HOUR=36e5,civilMonth12Length=[354,355,354,354,355,354,355,354,354,355,354,354,355,354,354,355,354,355,354,354,355,354,354,355,354,355,354,354,355,354,],UAQ_MONTH_LENGTH=["101010101010","110101010100","111011001001","011011010100","011011101010","001101101100","101010101101","010101010101","011010101001","011110010010","101110101001","010111010100","101011011010","010101011100","110100101101","011010010101","011101001010","101101010100","101101101010","010110101101","010010101110","101001001111","010100010111","011010001011","011010100101","101011010101","001011010110","100101011011","010010011101","101001001101","110100100110","110110010101","010110101100","100110110110","001010111010","101001011011","010100101011","101010010101","011011001010","101011101001","001011110100","100101110110","001010110110","100101010110","101011001010","101110100100","101111010010","010111011001","001011011100","100101101101","010101001101","101010100101","101101010010","101110100101","010110110100","100110110110","010101010111","001010010111","010101001011","011010100011","011101010010","101101100101","010101101010","101010101011","010100101011","110010010101","110101001010","110110100101","010111001010","101011010110","100101010111","010010101011","100101001011","101010100101","101101010010","101101101010","010101110101","001001110110","100010110111","010001011011","010101010101","010110101001","010110110100","100111011010","010011011101","001001101110","100100110110","101010101010","110101010100","110110110010","010111010101","001011011010","100101011011","010010101011","101001010101","101101001001","101101100100","101101110001","010110110100","101010110101","101001010101","110100100101","111010010010","111011001001","011011010100","101011101001","100101101011","010010101011","101010010011","110101001001","110110100100","110110110010","101010111001","010010111010","101001011011","010100101011","101010010101","101100101010","101101010101","010101011100","010010111101","001000111101","100100011101","101010010101","101101001010","101101011010","010101101101","001010110110","100100111011","010010011011","011001010101","011010101001","011101010100","101101101010","010101101100","101010101101","010101010101","101100101001","101110010010","101110101001","010111010100","101011011010","010101011010","101010101011","010110010101","011101001001","011101100100","101110101010","010110110101","001010110110","101001010110","111001001101","101100100101","101101010010","101101101010","010110101101","001010101110","100100101111","010010010111","011001001011","011010100101","011010101100","101011010110","010101011101","010010011101","101001001101","110100010110","110110010101","010110101010","010110110101","001011011010","100101011011","010010101101","010110010101","011011001010","011011100100","101011101010","010011110101","001010110110","100101010110","101010101010","101101010100","101111010010","010111011001","001011101010","100101101101","010010101101","101010010101","101101001010","101110100101","010110110010","100110110101","010011010110","101010010111","010101000111","011010010011","011101001001","101101010101","010101101010","101001101011","010100101011","101010001011","110101000110","110110100011","010111001010","101011010110","010011011011","001001101011","100101001011","101010100101","101101010010","101101101001","010101110101","000101110110","100010110111","001001011011","010100101011","010101100101","010110110100","100111011010","010011101101","000101101101","100010110110","101010100110","110101010010","110110101001","010111010100","101011011010","100101011011","010010101011","011001010011","011100101001","011101100010","101110101001","010110110010","101010110101","010101010101","101100100101","110110010010","111011001001","011011010010","101011101001","010101101011","010010101011","101001010101","110100101001","110101010100","110110101010","100110110101","010010111010","101000111011","010010011011","101001001101","101010101010","101011010101","001011011010","100101011101","010001011110","101000101110","110010011010","110101010101","011010110010","011010111001","010010111010","101001011101","010100101101","101010010101","101101010010","101110101000","101110110100","010110111001","001011011010","100101011010","101101001010","110110100100","111011010001","011011101000","101101101010","010101101101","010100110101","011010010101","110101001010","110110101000","110111010100","011011011010","010101011011","001010011101","011000101011","101100010101","101101001010","101110010101","010110101010","101010101110","100100101110","110010001111","010100100111","011010010101","011010101010","101011010110","010101011101","001010011101",],Common=function(n){return n={},window.navigator.msSaveBlob&&$.ajaxSetup({cache:!1}),n.format=function(){for(var i,t=arguments[0],n=0;n<arguments.length-1;n++)i=new RegExp("\\{"+n+"\\}","gm"),t=t.replace(i,arguments[n+1]);return t},n.getPosition=function(n){for(var t=0,i=0;n;)t+=n.offsetLeft-n.scrollLeft+n.clientLeft,i+=n.offsetTop-n.scrollTop+n.clientTop,n=n.offsetParent;return{x:t,y:i}},n.mask=function(){var t=document.createElement("div"),r,u,i;arguments[1]==="body-mask"?t.setAttribute("class","loading-mask-container"):t.setAttribute("class","loading-mask-container maskDivHeight");t.setAttribute("id",arguments[1]);t.innerHTML='<div class="loading-mask-image" style="display: block;"><i class="fa fa-spinner fa-pulse fa-2x fa - fw" /><\/div>';r=arguments[0];r.appendChild(t);u=n.getPosition(r);i=document.getElementById(arguments[1]);i.style.top="0px";i.style.height=$(document).height()+"px";i.style.width="100%"},n.unmask=function(){var n=document.getElementById(arguments[0]);n!==null&&n.parentNode.removeChild(n)},n.showConfirmMsg=function(n,t,i,r,u){typeof r!="undefined"&&(n+="<br/>"+r);swal({title:"",text:n,showCancelButton:!0,confirmButtonClass:"btn-info btn",confirmButtonText:Resources.Yes,cancelButtonText:Resources.No,closeOnConfirm:typeof u!="undefined"?u:!0,closeOnCancel:!0,html:typeof r!="undefined"?!0:!1},function(n){n?t():typeof i=="function"&&i()})},n.alertMsg=function(n,t){swal({title:"",text:n,showCancelButton:!1,confirmButtonClass:"btn-info btn",confirmButtonText:Resources.OK,closeOnConfirm:!0,closeOnCancel:!0,html:typeof additionalMsg!="undefined"?additionalMsg:""},function(){typeof t=="function"&&t()})},n.ajaxPost=function(n,t,i,r,u,f){if(u)if(f!==undefined&&f!==null){var e=document.getElementById(f);Common.mask(e,f+"-mask")}else Common.mask(document.body,"body-mask");$.ajax({url:n,type:"POST",data:t}).done(function(n,t,r){r.getResponseHeader("LoginPage")!==null&&location.reload(!0);typeof i=="function"&&i(n)}).fail(function(n,t,i){typeof r=="function"&&r(i,n)}).always(function(){u&&(f!==undefined&&f!==null?Common.unmask(f+"-mask"):Common.unmask("body-mask"))})},n.objectToFormData=function(n,t,i){i=i||"";typeof t!="object"||t instanceof File||t instanceof Blob?t instanceof Blob?n.append(i,t,t.name):n.append(i,t):$.each(t,function(t,r){i===""?Common.objectToFormData(n,r,t):Common.objectToFormData(n,r,i+"["+t+"]")})},n.ajaxPostWithFile=function(n,t,i,r,u,f){var o,e;u&&(f!==undefined&&f!==null?(o=document.getElementById(f),Common.mask(o,f+"-mask")):Common.mask(document.body,"body-mask"));e=new FormData;Common.objectToFormData(e,t);$.ajax({url:n,type:"POST",processData:!1,contentType:!1,data:e}).done(function(n,t,r){r.getResponseHeader("LoginPage")!==null&&location.reload(!0);typeof i=="function"&&i(n)}).fail(function(n,t,i){typeof r=="function"&&r(i,n)}).always(function(){u&&(f!==undefined&&f!==null?Common.unmask(f+"-mask"):Common.unmask("body-mask"))})},n.showScreenSuccessMsg=function(n){toastr.options.positionClass="toast-bottom-right";window.language==="ar"&&(toastr.options.positionClass="toast-bottom-left",toastr.options.rtl=!0);toastr.success(n===undefined?Resources.UpdateSuccessMsg:n)},n.showScreenWarningMsg=function(n){toastr.options.positionClass="toast-bottom-right";window.language==="ar"&&(toastr.options.positionClass="toast-bottom-left",toastr.options.rtl=!0);toastr.warning(n)},n.showScreenErrorMsg=function(n){toastr.options.positionClass="toast-bottom-right";window.language==="ar"&&(toastr.options.positionClass="toast-bottom-left",toastr.options.rtl=!0);toastr.error(n===undefined?Resources.ErrorOccured:n)},n.ajaxGet=function(n,t,i,r,u,f,e){if((typeof e=="undefined"||e===null)&&(e=!0),u)if(f!==undefined&&f!==null){var o=document.getElementById(f);Common.mask(o,f+"-mask")}else Common.mask(document.body,"body-mask");$.ajax({url:n,type:"GET",data:t,"async":e}).done(function(n,t,r){r.getResponseHeader("LoginPage")!==null&&location.reload(!0);typeof i=="function"&&i(n)}).fail(function(n,t,i){typeof r=="function"&&r(i,n)}).always(function(){u&&(f!==undefined&&f!==null?Common.unmask(f+"-mask"):Common.unmask("body-mask"))})},n.ajaxGetWithHeaders=function(n,t,i,r,u,f,e,o){if((typeof o=="undefined"||o===null)&&(o=!0),u)if(f!==undefined){var s=document.getElementById(f);Common.mask(s,f+"-mask")}else Common.mask(document.body,"body-mask");$.ajax({url:n,type:"GET",data:t,"async":o,headers:typeof e!="undefined"?e:""}).done(function(n,t,r){r.getResponseHeader("LoginPage")!==null?location.reload(!0):typeof i=="function"&&i(n)}).fail(function(n,t,i){n.getResponseHeader("LoginPage")!==null||n.status===401?location.reload(!0):typeof r=="function"&&r(i,n)}).always(function(){u&&(f!==undefined&&f!==null?Common.unmask(f+"-mask"):Common.unmask("body-mask"))})},n.gridCommon=function(){window.language==="ar"&&($.fn.DataTable.defaults.oLanguage.sProcessing="جارٍ التحميل...",$.fn.DataTable.defaults.oLanguage.sLengthMenu="أظهر _MENU_ مدخلات",$.fn.DataTable.defaults.oLanguage.sZeroRecords="لا يوجد معلومات",$.fn.DataTable.defaults.oLanguage.sEmptyTable="لا يوجد معلومات",$.fn.DataTable.defaults.oLanguage.sInfo="إظهار _START_ إلى _END_ من أصل _TOTAL_",$.fn.DataTable.defaults.oLanguage.sInfoEmpty="يعرض 0 إلى 0 من أصل 0 سجل",$.fn.DataTable.defaults.oLanguage.sInfoFiltered="(منتقاة من مجموع _MAX_ مُدخل)",$.fn.DataTable.defaults.oLanguage.sSearch="ابحث:",$.fn.DataTable.defaults.oLanguage.oPaginate.sFirst="الأول",$.fn.DataTable.defaults.oLanguage.oPaginate.sPrevious="السابق",$.fn.DataTable.defaults.oLanguage.oPaginate.sNext="التالي",$.fn.DataTable.defaults.oLanguage.oPaginate.sLast="الأخير");window.language==="fr"&&($.fn.DataTable.defaults.oLanguage.sProcessing="Traitement...",$.fn.DataTable.defaults.oLanguage.sLengthMenu="Afficher _MENU_ éléments",$.fn.DataTable.defaults.oLanguage.sZeroRecords="Aucun élément correspondant trouvé",$.fn.DataTable.defaults.oLanguage.sEmptyTable="Aucune donnée disponible dans le tableau",$.fn.DataTable.defaults.oLanguage.sInfo="Affichage de l'élément _START_ à _END_ sur _TOTAL_ éléments",$.fn.DataTable.defaults.oLanguage.sInfoEmpty="Affichage de l'élément 0 à 0 sur 0 élément",$.fn.DataTable.defaults.oLanguage.sInfoFiltered="(filtré à partir de _MAX_ éléments au total)",$.fn.DataTable.defaults.oLanguage.sSearch="Rechercher :",$.fn.DataTable.defaults.oLanguage.oPaginate.sFirst="Premier",$.fn.DataTable.defaults.oLanguage.oPaginate.sPrevious="Précédent",$.fn.DataTable.defaults.oLanguage.oPaginate.sNext="Suivant",$.fn.DataTable.defaults.oLanguage.oPaginate.sLast="Dernier");screen.width<=450&&($.fn.DataTable.ext.pager.numbers_length=4,$.fn.DataTable.defaults.oLanguage.oPaginate.sNext='<i class="fa fa-angle-right"><\/i>',$.fn.DataTable.defaults.oLanguage.oPaginate.sPrevious='<i class="fa fa-angle-left"><\/i>');$.fn.DataTable.ext.errMode=function(){Common.showScreenErrorMsg()}},n.b64toBlob=function(n,t,i){var e,o,r,f,s,u,h;for(t=t||"",i=i||512,e=atob(n),o=[],r=0;r<e.length;r+=i){for(f=e.slice(r,r+i),s=new Array(f.length),u=0;u<f.length;u++)s[u]=f.charCodeAt(u);h=new Uint8Array(s);o.push(h)}return new Blob(o,{type:t})},n.dataURItoBlob=function(n){for(var i=atob(n.split(",")[1]),f=n.split(",")[0].split(":")[1].split(";")[0],r=new ArrayBuffer(i.length),u=new Uint8Array(r),t=0;t<i.length;t++)u[t]=i.charCodeAt(t);return new Blob([r])},n.isPlatformIOS=function(){return/iPad|iPhone|iPod/.test(navigator.userAgent)&&!window.MSStream},n.isIEBrowser=function(){var n=window.navigator.userAgent,t=n.indexOf("MSIE ");return t>0||!!navigator.userAgent.match(/Trident.*rv\:11\./)?!0:!1},n.removeHashFromUrl=function(){var n=window.location.toString(),t;n.indexOf("#")>0&&(t=n.substring(0,n.indexOf("#")),window.history.replaceState({},document.title,t))},n.setActiveSidebarMenu=function(n){let r=$(".sidebar-subnav");r.removeClass("in");let u=$('.sidebar li[class="active"]');u.attr("class","");let i=$("#"+n);if(i.length>0){i.attr("class","active");for(var t=getParentOfParentElement($("#"+n));!t.hasClass("sidebar");)i.parent().addClass("in"),t.parent().addClass("in"),t.addClass("active"),t=getParentOfParentElement(t)}},n.reloadCurrentPage=function(n){var t=document.location.hash;typeof n!="undefined"&&(t=document.location.hash.split("/")[0]+"/"+n);Backbone.history.fragment=null;Backbone.history.navigate(t,!0)},n.disableModelClose=function(n){$(n).data("bs.modal").options.keyboard=!1;$(n).data("bs.modal").options.backdrop="static"},n.enableModelClose=function(n){$(n).data("bs.modal").options.keyboard=!0;$(n).data("bs.modal").options.backdrop="true"},n.translate=function(n){var t=Resources[n];return typeof t=="undefined"&&(Common.ajaxGet("/TranslatorDictionary/Translate",{keyword:n},function(n){t=n},null,null,null,!1),Resources[n]=t),t},n.getRandomColor=function(n){let t=["#2b957a","#434348","#1797be","#7cb5ec","#90ed7d","#f7a35c"];if(n!==undefined&&n<t.length)return t[n];let r="0123456789ABCDEF",i="#";for(let n=0;n<6;n++)i+=r[Math.floor(Math.random()*16)];return i},n.translateHighchartsExport=function(){Highcharts.setOptions({lang:{viewFullscreen:Resources.ViewFullscreen,printChart:Resources.PrintChart,downloadPNG:Resources.DownloadPNG,downloadPDF:Resources.DownloadPDF,downloadSVG:Resources.DownloadSVG,downloadCSV:Resources.DownloadCSV,downloadXLS:Resources.DownloadXLS,downloadJPEG:Resources.DownloadJPEG,viewData:Resources.ViewData,exitFullscreen:Resources.ExitFullscreen}})},n.ajaxDelete=function(n,t,i,r,u,f){if(u)if(f!==undefined&&f!==null){var e=document.getElementById(f);Common.mask(e,f+"-mask")}else Common.mask(document.body,"body-mask");$.ajax({url:n,type:"DELETE",data:t}).done(function(n,t,r){r.getResponseHeader("LoginPage")!==null&&location.reload(!0);typeof i=="function"&&i(n)}).fail(function(n,t,i){typeof r=="function"&&r(i,n)}).always(function(){u&&(f!==undefined&&f!==null?Common.unmask(f+"-mask"):Common.unmask("body-mask"))})},n.removeUnUsedDomElementsOnRoute=function(){GridCommon.Destroy();$(".sweet-alert").remove();$(".sweet-overlay").remove();$(".modal-backdrop").remove();$("body").removeClass("modal-open")},n.ajaxPostWithHeaders=function(n,t,i,r,u,f,e,o){if((typeof o=="undefined"||o===null)&&(o=!0),u)if(f!==undefined&&f!==null){var s=document.getElementById(f);Common.mask(s,f+"-mask")}else Common.mask(document.body,"body-mask");$.ajax({url:n,type:"POST",data:t,headers:typeof e!="undefined"?e:"","async":o}).done(function(n,t,r){r.getResponseHeader("LoginPage")!==null&&location.reload(!0);typeof i=="function"&&i(n)}).fail(function(n,t,i){typeof r=="function"&&r(i,n)}).always(function(){u&&(f!==undefined&&f!==null?Common.unmask(f+"-mask"):Common.unmask("body-mask"))})},n.ajaxPostJSON=function(n,t,i,r,u,f){if(u)if(f!==undefined&&f!==null){var e=document.getElementById(f);Common.mask(e,f+"-mask")}else Common.mask(document.body,"body-mask");$.ajax({url:n,type:"POST",data:t,contentType:"application/json; charset=utf-8",dataType:"json"}).done(function(n,t,r){r.getResponseHeader("LoginPage")!==null&&location.reload(!0);typeof i=="function"&&i(n)}).fail(function(n,t,i){typeof r=="function"&&r(i,n)}).always(function(){u&&(f!==undefined&&f!==null?Common.unmask(f+"-mask"):Common.unmask("body-mask"))})},n.sanatize=function(n){return n.replace(/"/gi,"&quot;").replace(/</gi,"&lt;").replace(/>/gi,"&gt;")},n.sanatizeTree=function(n){for(var t=0;t<n.length;t++)n[t].text=Common.sanatize(n[t].text),n[t].children&&n[t].children.length>0&&(n[t].children=Common.sanatizeTree(n[t].children));return n},n.ajaxError=function(n,t,i,r){n.getResponseHeader("LoginPage")!==null&&location.reload(!0);Common.showScreenErrorMsg();typeof r=="function"&&r(n.responseJSON)},n.getMonths=function(){var n=[Resources.January,Resources.February,Resources.March,Resources.April,Resources.May,Resources.June,Resources.July,Resources.August,Resources.September,Resources.October,Resources.November,Resources.December];return window.CalendarType&&window.CalendarType!==window.CalendarTypes.None&&(n=[Resources.Muharram,Resources.Safar,Resources.RabiAlAwwal,Resources.RabiAthani,Resources.JumadaAlAwwal,Resources.JumadaAthani,Resources.Rajab,Resources.Shaban,Resources.Ramadan,Resources.Shawwal,Resources.DhuAlQadah,Resources.DhuAlHijjan]),n},n}(Common);$(document).ready(function(){window.language==="ar"?flatpickr.localize(flatpickr.l10ns.ar):flatpickr.localize(flatpickr.l10ns.fr);flatpickr(".flatpickr-input");window.language==="ar"&&flatpickr.setDefaults({position:"auto right"});$(document).on("show.bs.modal",".modal",function(){var n=1050+10*$(".modal:visible").length;$(this).css("z-index",n);setTimeout(function(){$(".modal-backdrop").not(".modal-stack").css("z-index",n-1).addClass("modal-stack")},0)});Handlebars.registerHelper("Localizer",function(n){return Resources[n]!==undefined?Resources[n]:n});Handlebars.registerHelper("Format",function(n,t){var r,i,u;for(t=eval(t),r=n,i=0;i<t.length;i++)u=new RegExp("\\{"+i+"\\}","gm"),r=r.replace(u,t[i]);return r});Handlebars.registerHelper("ifEquals",function(n,t,i){return n===t?i.fn(this):i.inverse(this)})});var isMobile={Android:function(){return navigator.userAgent.match(/Android/i)},BlackBerry:function(){return navigator.userAgent.match(/BlackBerry/i)},iOS:function(){return navigator.userAgent.match(/iPhone|iPad|iPod/i)},Opera:function(){return navigator.userAgent.match(/Opera Mini/i)},Windows:function(){return navigator.userAgent.match(/IEMobile/i)},any:function(){return isMobile.Android()||isMobile.BlackBerry()||isMobile.iOS()||isMobile.Opera()||isMobile.Windows()}},TypeMenu=function(n){return n={},n.TopNavBar="1",n.SideBar="2",n}(TypeMenu),TypeAction=function(n){return n={},n.Row="1",n.Toolbar="2",n.Context="3",n.Tab="4",n}(TypeAction),MenuType=function(n){return n={},n.Url="1",n.JsFunction="2",n.Html="3",n}(MenuType),UrlType=function(n){return n={},n.Target="1",n.Self="2",n.Frame="3",n.Modal="4",n}(UrlType),TabType=function(n){return n={},n.Url="1",n.JavascriptFunction="2",n.HTML="3",n}(TabType),MenuSecurityActions=function(n){return n={},n.renderHtmlToWrapper=function(n,t,i){var u=$("#"+n).val();let r=null;i?(r=document.getElementsByClassName("modal-window")[0],MenuSecurityActions.buildModalWindow(r,u,t)):(r=document.getElementsByClassName("content-wrapper")[0],t?MenuSecurityActions.buildHtmlPage(r,u,t):(r.innerHTML="",r.innerHTML=u))},n.buildFromUrlCall=function(n,t,i,r,u){if(r){let r=null;u=u!==""?u:"100%";i?(r=document.getElementsByClassName("modal-window")[0],MenuSecurityActions.buildModalWindowWithIframe(r,n,t,u)):(r=document.getElementsByClassName("content-wrapper")[0],MenuSecurityActions.buildHtmlPageWithIframe(r,n,t,u))}else Common.ajaxGet(n,null,function(n){let r=null;i?(r=document.getElementsByClassName("modal-window")[0],MenuSecurityActions.buildModalWindow(r,n,t)):(r=document.getElementsByClassName("content-wrapper")[0],t?MenuSecurityActions.buildHtmlPage(r,n,t):(r.innerHTML="",r.innerHTML=n))})},n.buildModalWindow=function(n,t,i){var r=document.createElement("div"),o,f,e,u,s,h;r.setAttribute("id","modal");r.setAttribute("tabindex","-1");r.setAttribute("role","dialog");r.setAttribute("aria-hidden","true");r.setAttribute("class","modal fade");o=document.createElement("div");o.setAttribute("class","modal-dialog modal-xl");f=document.createElement("div");f.setAttribute("class","modal-content");e=document.createElement("div");e.setAttribute("class","modal-header");u=document.createElement("button");u.setAttribute("type","button");u.setAttribute("class","close");u.setAttribute("data-dismiss","modal");u.innerHTML="&times;";s=document.createElement("h4");s.setAttribute("class","modal-title");s.innerHTML=i;h=document.createElement("div");h.setAttribute("class","modal-body");h.innerHTML=t;e.appendChild(u);i&&e.appendChild(s);f.appendChild(e);f.appendChild(h);o.appendChild(f);r.appendChild(o);n.appendChild(r);$("#modal").modal("show");$("#modal").off("hidden.bs.modal");$("#modal").off("shown.bs.modal");$("#modal").on("hidden.bs.modal",function(){$("#modal").remove()})},n.buildHtmlPage=function(n,t,i){var f,r,u;n.innerHTML="";f=document.createElement("h3");f.innerHTML=i;r=document.createElement("div");r.setAttribute("class","row");u=document.createElement("div");u.setAttribute("class","col-md-12");u.innerHTML=t;r.appendChild(u);n.appendChild(f);n.appendChild(r)},n.buildModalWindowWithIframe=function(n,t,i,r){var u=document.createElement("div"),h,e,o,f,c,s,l;u.setAttribute("id","modal");u.setAttribute("tabindex","-1");u.setAttribute("role","dialog");u.setAttribute("aria-hidden","true");u.setAttribute("class","modal fade");h=document.createElement("div");h.setAttribute("class","modal-dialog modal-xl");e=document.createElement("div");e.setAttribute("class","modal-content");o=document.createElement("div");o.setAttribute("class","modal-header");f=document.createElement("button");f.setAttribute("type","button");f.setAttribute("class","close");f.setAttribute("data-dismiss","modal");f.innerHTML="&times;";c=document.createElement("h4");c.setAttribute("class","modal-title");c.innerHTML=i;s=document.createElement("div");s.setAttribute("class","modal-body");s.setAttribute("style","padding:0px");l=document.createElement("iframe");l.setAttribute("style","width:100%;height:"+r+";border:0;");l.src=t;s.appendChild(l);o.appendChild(f);i&&o.appendChild(c);e.appendChild(o);e.appendChild(s);h.appendChild(e);u.appendChild(h);n.appendChild(u);$("#modal").modal("show");$("#modal").off("hidden.bs.modal");$("#modal").off("shown.bs.modal");$("#modal").on("hidden.bs.modal",function(){$("#modal").remove()})},n.buildHtmlPageWithIframe=function(n,t,i,r){var o,u,f,e;n.innerHTML="";o=document.createElement("h3");o.innerHTML=i;u=document.createElement("div");u.setAttribute("class","row");f=document.createElement("div");f.setAttribute("class","col-md-12");e=document.createElement("iframe");e.setAttribute("style","width:100%;height:"+r+";border:0;");e.src=t;f.appendChild(e);u.appendChild(f);n.appendChild(o);n.appendChild(u)},n}(MenuSecurityActions),SecurityMatrix=function(n){function t(n,i,r){var c=[],s=$.grep(i,function(n){return n.ParentActionId===r}),o,u,e,h;if(s.length>0)for(o=0;o<s.length;o++){u=s[o];let r=u.Tooltip!==null?u.Tooltip:"";e={text:(u.Icon?'<i class="fa '+u.Icon+' mr-sm"><\/i>':"")+'<span data-color="'+u.Color+'">'+u.Name+"<\/span>",titleAttr:r,attr:{divId:n,"function":u.JsFunction,id:u.Id}};h=t(n,i,u.Id);h.length>0?(e.extend="collection",e.buttons=h):e.action=f;c.push(e)}return c}function i(n,t,r){var o=$.grep(n,function(n){return n.ParentActionId===r}),e,u,f;if(o.length>0)for(t[r].items={},e=0;e<o.length;e++){u=o[e];let s=u.Tooltip!==null?u.Tooltip:"";for(t[r].items[u.Id]={name:"<span class='customConext' data-function='"+u.JsFunction+"' title='"+s+"'>"+u.Name+"<\/span>",isHtmlName:!0,order:u.Order,icon:u.Icon!==""&&u.Icon!==null?u.Icon.replace("fa ",""):"","function":u.JsFunction,callback:function(n,t,i){var r=$(i.target).find(".customConext").data("function"),u;let f=$(this).closest("tr"),e=$(this).parents("table").attr("id"),o=$("#"+e).DataTable().row(f);u=o.data();r&&(r=r.split("(")[0],eval(r+"("+JSON.stringify(u)+")"))}},i(n,t[r].items,u.Id),optionSortedArray=Object.keys(t[r].items).sort(function(n,i){return t[r].items[n].order-t[r].items[i].order}).map(function(n){return t[r].items[n]}),t[r].items={},f=0;f<optionSortedArray.length;f++)t[r].items[f]=optionSortedArray[f]}}function r(n,t,i){var o=$.grep(n,function(n){return n.ParentActionId===i}),e,u,f;if(o.length>0){for(t.items={},e=0;e<o.length;e++){u=o[e];let i=u.Tooltip!==null?u.Tooltip:"";t.items[u.Id]={name:"<span class='customConext' data-function='"+u.JsFunction+"' title='"+i+"'>"+u.Name+"<\/span>",isHtmlName:!0,order:u.Order,icon:u.Icon!==""&&u.Icon!==null?u.Icon.replace("fa ",""):"","function":u.JsFunction,callback:function(n,t,i){var r=$(i.target).find(".customConext").data("function"),u;let f=$(this).closest("tr"),e=$(this).parents("table").attr("id"),o=$("#"+e).DataTable().row(f);u=o.data();r&&(r=r.split("(")[0],eval(r+"("+JSON.stringify(u)+")"))}};r(n,t.items[u.Id],u.Id)}for(optionSortedArray=Object.keys(t.items).sort(function(n,i){return t.items[n].order-t.items[i].order}).map(function(n){return t.items[n]}),t.items={},f=0;f<optionSortedArray.length;f++)t.items[f]=optionSortedArray[f]}}function u(n,t,i){var o=$.grep(n,function(n){return n.ParentActionId===i}),e,r,f;if(o.length>0){for(t.items={},e=0;e<o.length;e++){r=o[e];let i=r.Tooltip!==null?r.Tooltip:"";t.items[r.Id]={name:"<span class='customConext' data-function='"+r.JsFunction+"' title='"+i+"'>"+r.Name+"<\/span>",isHtmlName:!0,order:r.Order,icon:r.Icon!==""&&r.Icon!==null?r.Icon.replace("fa ",""):"","function":r.JsFunction,callback:function(n,t,i){var r=$(i.target).find(".customConext").data("function");eval(r)}};u(n,t.items[r.Id],r.Id)}for(optionSortedArray=Object.keys(t.items).sort(function(n,i){return t.items[n].order-t.items[i].order}).map(function(n){return t.items[n]}),t.items={},f=0;f<optionSortedArray.length;f++)t.items[f]=optionSortedArray[f]}}var f=function(n,t,i,r){var u=GridCommon.GetSelectedRows(r.attr.divId),f;u.length>0?r.attr.function&&(f=r.attr.function.split("(")[0],eval(f+"("+JSON.stringify(u)+")")):Common.alertMsg(Resources.NoRowSelected)};return n={},n.CategoryIdPropertyName="categoryId",n.Menus="1",n.Actions="2",n.Tabs="3",n.Nodes="4",n.InitToolbarColor=function(){$("[data-color]").each(function(){var n=$(this).data("color");$(this).parents(".btn-default").attr("style","background-color:"+n)})},n.InitContextMenu=function(t,i){$.contextMenu("destroy",".custom-tab-action");$.contextMenu("destroy",".dataTable td");$.contextMenu("destroy",".row-action");$.contextMenu({selector:".dataTable td",trigger:isMobile.any()?"left":"right",build:function(r){var u,f;if(!r.hasClass("hasChildren")){let e=r.closest("tr"),o=r.parents("table").attr("id"),s=$("#"+o).DataTable().row(e);if(u=s.data(),u&&typeof u[n.CategoryIdPropertyName]!="undefined")return f={items:{}},SecurityMatrix.getContextActions(t,f,i,u[n.CategoryIdPropertyName]),jQuery.isEmptyObject(f.items)?!1:f}return!1},rtl:!0});$.contextMenu({selector:".row-action",trigger:"left",build:function(r){var e,u;let f=r.data("id"),o=r.closest("tr"),s=r.parents("table").attr("id"),h=$("#"+s).DataTable().row(o);return(e=h.data(),f)?(u={items:{}},SecurityMatrix.getRowSubActions(t,u,i,e[n.CategoryIdPropertyName],f),jQuery.isEmptyObject(u.items)?!1:u):!1}})},n.InitTabContextMenu=function(n){$.contextMenu("destroy",".custom-tab-action");$.contextMenu({selector:".custom-tab-action",trigger:"left",build:function(t){let i=t.data("id");if(i){var r={items:{}};return SecurityMatrix.getTabSubActions(n,r,i),r}return!1}})},n.getToolbarActions=function(n,i,r){for(var u,e,h,c=[],o=$.grep(n.SecurityNodes[Number(i)].Actions,function(n){return n.Type===Number(TypeAction.Toolbar)}),s=0;s<o.length;s++)if(u=o[s],u.ParentActionId===null){let n=u.Tooltip!==null?u.Tooltip:"";e={className:"btn-sm btn-primary",text:(u.Icon?'<i class="fa '+u.Icon+' mr-sm"><\/i>':"")+'<span data-color="'+u.Color+'">'+u.Name+"<\/span>",titleAttr:n,attr:{divId:r,"function":u.JsFunction,id:u.Id}};h=t(r,o,u.Id);h.length>0?(e.extend="collection",e.buttons=h):e.action=f;c.push(e)}return c},n.getRowActions=function(t,i,r){for(var u,s,o,f=$.grep(t.SecurityNodes[Number(r)].Actions,function(n){return n.Type===Number(TypeAction.Row)}),e=0;e<f.length;e++)if(u=f[e],u.ParentActionId===null){let h=u.Tooltip!==null?u.Tooltip:"",e=document.createElement("button");e.setAttribute("title",h);e.setAttribute("data-id",u.Id);e.innerHTML="<i class='fa "+(u.Icon!==""?u.Icon:"fa-ellipsis-h")+" fa-white'><\/i> ";s=$.grep(f,function(n){return n.ParentActionId===u.Id});o="text-right";u.Color&&e.setAttribute("style","background-color:"+u.Color+";border-color:"+u.Color);s.length>0?(o+=" hasChildren",e.setAttribute("class","btn btn-xs btn-primary row-action")):(e.setAttribute("class","btn btn-xs btn-primary custom-action"),e.setAttribute("clickattr",u.JsFunction));i.push({actionName:u.Name,className:o,autoWidth:!1,bAutoWidth:!1,width:"16px",orderable:!1,sortable:!1,defaultContent:e.outerHTML,createdCell:function(i,u,f){var e=i.children[0],o=e!==null&&typeof e!="undefined"?$(e).attr("data-id"):"0",s=$.grep(t.SecurityNodes[Number(r)].SecurityCategories[Number(f[n.CategoryIdPropertyName])].Actions,function(n){return n.Type===Number(TypeAction.Row)&&n.Id===Number(o)});s.length===0&&$(i).empty()}})}$(document).off("click",".custom-action");$(document).on("click",".custom-action",function(){var n=$(this).attr("clickattr"),t,i;let r=$(this).closest("tr"),u=$(this).parents("table").attr("id"),f=$("#"+u).DataTable().row(r);t=f.data();n&&(i=n.split("(")[0],eval(i+"("+JSON.stringify(t)+")"))});return i},n.getRowSubActions=function(n,t,i,u,f){var e=$.grep(n.SecurityNodes[Number(i)].SecurityCategories[Number(u)].Actions,function(n){return n.Type===Number(TypeAction.Row)});return r(e,t,f),t},n.getTabSubActions=function(n,t,i){return u(n,t,i),t},n.getContextActions=function(n,t,r,u){for(var f,o=$.grep(n.SecurityNodes[Number(r)].SecurityCategories[Number(u)].Actions,function(n){return n.Type===Number(TypeAction.Context)}),e=0;e<o.length;e++)if(f=o[e],f.ParentActionId===null){let n=f.Tooltip!==null?f.Tooltip:"";t.items[f.Id]={name:"<span class='customConext' data-function='"+f.JsFunction+"' title='"+n+"'>"+f.Name+"<\/span>",isHtmlName:!0,order:f.Order,icon:f.Icon!==""&&f.Icon!==null?f.Icon.replace("fa ",""):"fa-ellipsis-v","function":f.JsFunction,callback:function(n,t,i){var r=$(i.target).find(".customConext").data("function"),u;let f=$(this).closest("tr"),e=$(this).parents("table").attr("id"),o=$("#"+e).DataTable().row(f);u=o.data();r&&(r=r.split("(")[0],eval(r+"("+JSON.stringify(u)+")"))}};i(o,t.items,f.Id)}for(optionSortedArray=Object.keys(t.items).sort(function(n,i){return t.items[n].order-t.items[i].order}).map(function(n){return t.items[n]}),t.items={},e=0;e<optionSortedArray.length;e++)t.items[e]=optionSortedArray[e];return t},n.getTabActions=function(n,t,i){var u,r,e;let f=document.createElement("div");for(f.setAttribute("class","html5buttons btn-group"),u=0;u<n.length;u++)if(r=n[u],r.ParentActionId===null){let o="btn-primary",s=r.Tooltip!==null?r.Tooltip:"",u=document.createElement("button");u.setAttribute("title",s);u.innerHTML="<i class='fa "+(r.Icon!==""?r.Icon:"fa-ellipsis-h")+" fa-white'><\/i> "+r.Name;e=$.grep(n,function(n){return n.ParentActionId===r.Id});r.Color&&(u.setAttribute("style","background-color:"+r.Color+";border-color:"+r.Color),o=r.Color!==window.DefaultColor?"btn-info":"btn-primary");e.length>0?(u.setAttribute("data-id",r.Id),u.setAttribute("class","btn btn-sm "+o+" custom-tab-action custom-toolbar")):(u.setAttribute("class","btn btn-sm "+o+" custom-action custom-toolbar"),u.setAttribute("onclick",r.JsFunction));i?$(t).prepend(u):t==="#myTaskActions"?$(t).prepend(u):f.appendChild(u)}t!=="#myTaskActions"&&n.length>0&&(i||$(t).prepend(f))},n}(SecurityMatrix),GridCommon=function(n){return n={},n.GridsObject={},n.AddCheckBoxEvents=function(n){GridCommon.Clear(n);$("#"+n+" tbody").on("click","tr",function(){var t=this.getElementsByTagName("input")[0],i;typeof t!="undefined"&&(t.checked=t.checked?!1:!0,i=t.getAttribute("data-id"),t.checked?GridCommon.Add(n,i):GridCommon.Remove(n,i))});$("#"+n+" #chkAll").change(function(){var t=$(this).prop("checked");$("#"+n+' tbody tr td input[type="checkbox"]').prop("checked",t);$("#"+n+' tbody tr td input[type="checkbox"]').each(function(){var i=this.getAttribute("data-id");t?GridCommon.Add(n,i):GridCommon.Remove(n,i)})});$("#"+n+" tbody").on("change",'tr td input[type="checkbox"]',function(){var i=$(this).prop("checked"),t=this.getAttribute("data-id");i?GridCommon.Add(n,t):GridCommon.Remove(n,t)})},n.Remove=function(n,t){var i=GridCommon.GetSelectedRows(n);const r=i.indexOf(t);r>-1&&(i.splice(r,1),GridCommon.SetSelectedRows(n,i))},n.Add=function(n,t){var i=GridCommon.GetSelectedRows(n);const r=i.indexOf(t);r===-1&&(i.push(t),GridCommon.SetSelectedRows(n,i))},n.SetSelectedRows=function(n,t){GridCommon.GridsObject[n]=t},n.GetSelectedRows=function(n){return GridCommon.GridsObject[n]||(GridCommon.GridsObject[n]=[]),GridCommon.GridsObject[n]},n.CheckSelectedRows=function(n){var t=GridCommon.GetSelectedRows(n);$("#"+n+" tbody").find('input[type="checkbox"]').each(function(n,i){var r=i.getAttribute("data-id");t.includes(r)&&i.setAttribute("checked","checked")})},n.Clear=function(n){GridCommon.GridsObject[n]=[]},n.Refresh=function(n){$("#"+n).DataTable().ajax.reload();GridCommon.Clear(n)},n.RefreshCurrentPage=function(n,t){$("#"+n).DataTable().ajax.reload(null,!1);t&&$(".btn-back").click()},n.Destroy=function(){GridCommon.GridsObject={}},n}(GridCommon),GridCommonCore=function(n){return n={},n.GridsObject={},n.GridsWholeObject={},n.AddCheckBoxEvents=function(n,t){GridCommonCore.Clear(t);$($(n.refs[t]).find("tbody")).on("click","tr",function(){var i=this.getElementsByTagName("input")[0],r;if(typeof i!="undefined"){i.checked=i.checked?!1:!0;r=i.getAttribute("data-id");let f=$(this).closest("tr"),u=$(n.refs[t]).DataTable().row(f).data();i.checked?(GridCommonCore.Add(t,r),GridCommonCore.AddWholeRow(t,u)):(GridCommonCore.Remove(t,r),GridCommonCore.RemoveWholeRow(t,u))}});$($(n.refs[t]).find("#chkAll")).change(function(){var i=$(this).prop("checked");$($(n.refs[t]).find('tbody tr td input[type="checkbox"]')).prop("checked",i);$($(n.refs[t]).find('tbody tr td input[type="checkbox"]')).each(function(){var r=this.getAttribute("data-id");let f=$(this).closest("tr"),u=$(n.refs[t]).DataTable().row(f).data();i?(GridCommonCore.Add(t,r),GridCommonCore.AddWholeRow(t,u)):(GridCommonCore.Remove(t,r),GridCommonCore.RemoveWholeRow(t,u))})});$($(n.refs[t]).find("tbody")).on("change",'tr td input[type="checkbox"]',function(){var u=$(this).prop("checked"),i=this.getAttribute("data-id");let f=$(this).closest("tr"),r=$(n.refs[t]).DataTable().row(f).data();u?(GridCommonCore.Add(t,i),GridCommonCore.AddWholeRow(t,r)):(GridCommonCore.Remove(t,i),GridCommonCore.RemoveWholeRow(t,r))})},n.Remove=function(n,t){var i=GridCommonCore.GetSelectedRows(n);const r=i.indexOf(t);r>-1&&(i.splice(r,1),GridCommonCore.SetSelectedRows(n,i))},n.Add=function(n,t){var i=GridCommonCore.GetSelectedRows(n);const r=i.indexOf(t);r===-1&&(i.push(t),GridCommonCore.SetSelectedRows(n,i))},n.SetSelectedRows=function(n,t){GridCommonCore.GridsObject[n]=t},n.GetSelectedRows=function(n){return GridCommonCore.GridsObject[n]||(GridCommonCore.GridsObject[n]=[]),GridCommonCore.GridsObject[n]},n.CheckSelectedRows=function(n,t){var i=GridCommonCore.GetSelectedRows(t);$($(n.refs[t]).find("tbody")).find('input[type="checkbox"]').each(function(n,t){var r=t.getAttribute("data-id");i.includes(r)&&t.setAttribute("checked","checked")})},n.Clear=function(n){GridCommonCore.GridsObject[n]=[];GridCommonCore.GridsWholeObject[n]=[]},n.Refresh=function(n,t){$(n.refs[t]).DataTable().ajax.reload();GridCommonCore.Clear(t)},n.RefreshCurrentPage=function(n,t,i){$(n.refs[t]).DataTable().ajax.reload(null,!1);i&&$(".btn-back").click()},n.Destroy=function(){GridCommonCore.GridsObject={};GridCommonCore.GridsWholeObject={}},n.RemoveWholeRow=function(n,t){var i=GridCommonCore.GetWholeSelectedRows(n);const r=i.indexOf(t);r>-1&&(i.splice(r,1),GridCommonCore.SetWholeSelectedRows(n,i))},n.AddWholeRow=function(n,t){var i=GridCommonCore.GetWholeSelectedRows(n);const r=i.indexOf(t);r===-1&&(i.push(t),GridCommonCore.SetWholeSelectedRows(n,i))},n.SetWholeSelectedRows=function(n,t){GridCommonCore.GridsWholeObject[n]=t},n.GetWholeSelectedRows=function(n){return GridCommonCore.GridsWholeObject[n]||(GridCommonCore.GridsWholeObject[n]=[]),GridCommonCore.GridsWholeObject[n]},n}(GridCommonCore),AddressBookMode=function(n){return n={},n.Internal="1",n.External="2",n.InternalWithUsers="3",n}(AddressBookMode),AddressBookSelectionMode=function(n){return n={},n.Single=1,n.Multiple=2,n}(AddressBookSelectionMode),ConversionDateFormat=function(n){return n={},n.DateFormat_Slash="dd/mm/yyyy",n.DateTimeFormat24_Slash="dd/mm/yyyy hh:mm",n.DateTimeFormatSeconds24_Slash="dd/mm/yyyy hh:mm:ss",n.DateTimeFormat_Slash="dd/mm/yyyy hh:mm a",n.DateTimeFormatSeconds_Slash="dd/mm/yyyy hh:mm:ss a",n.DateFormat_Dash="dd-mm-yyyy",n.DateTimeFormat24_Dash="dd-mm-yyyy hh:mm",n.DateTimeFormatSeconds24_Dash="dd-mm-yyyy hh:mm:ss",n.DateTimeFormat_Dash="dd-mm-yyyy hh:mm a",n.DateTimeFormatSeconds_Dash="dd-mm-yyyy hh:mm:ss a",n.YearMonthDateFormat_Dash="yyyy-mm-dd",n}(ConversionDateFormat),CalendarTypes=function(n){return n={},n.Tabular="Tabular",n.UmmAlQura="UmmAlQura",n.Civil="Civil",n.None="None",n}(CalendarTypes),DateConverter=function(n){return n={},n.toHijriFormated=function(n,t,i){var r,u,f;if(n){if(r=n,i!==CalendarTypes.None){t=t?t:"dd/mm/yyyy";u=t.replaceAll("d","D").replaceAll("m","M").replaceAll("y","Y").replace(":MM",":mm");f=new Date(moment(n,u));r=convertDateToHijri(f,i);var e=r.nYear,o=pad(r.nMonth),s=pad(r.nDay),h=pad(r.nHour),c=pad(r.nMinute),l=pad(r.nSecond);r=getStrDate(t,e,o,s,h,c,l)}return r}return""},n.toHijri=function(n,t){return t!==CalendarTypes.None?convertDateToHijri(n,t):n},n.convertToGregorian=function(n,t,i,r,u,f){var h=new Date,o,e,s,c,l,a;if(u&&h.setHours(u.getHours(),u.getMinutes(),u.getSeconds()),o=convertToNational(h,r),r===CalendarTypes.None)return h.setFullYear(n,t,i),h;if(s=0,c=n<o.nYear||n===o.nYear&&t<o.nMonth||n===o.nYear&&t===o.nMonth&&i<o.nDay,Math.abs(o.nYear-n)>1)for(l=Math.min(o.nYear,n)+1;l<Math.max(o.nYear,n);l++)if(r===CalendarTypes.Civil||r===CalendarTypes.Tabular)s+=civilMonth12Length[(l-1)%30];else for(e=0;e<12;e++)s+=29+parseInt(UAQ_MONTH_LENGTH[l-1300][e]);if(s+=n!==o.nYear||t!==o.nMonth?c?o.nDay+getDaysInNationalMonth(t,n,r)-i:i+getDaysInNationalMonth(o.nMonth,o.nYear,r)-o.nDay:Math.abs(i-o.nDay),o.nYear!=n)if(c){for(e=0;e<o.nMonth;e++)s+=getDaysInNationalMonth(e,o.nYear,r);for(e=11;e>t;e--)s+=getDaysInNationalMonth(e,n,r)}else{for(e=o.nMonth+1;e<12;e++)s+=getDaysInNationalMonth(e,o.nYear,r);for(e=0;e<t;e++)s+=getDaysInNationalMonth(e,n,r)}else if(o.nMonth!=t)if(c)for(e=t+1;e<o.nMonth;e++)s+=getDaysInNationalMonth(e,o.nYear,r);else for(e=o.nMonth+1;e<t;e++)s+=getDaysInNationalMonth(e,o.nYear,r);return f&&f.toLowerCase().indexOf("h")<0&&h.getHours()===0&&h.setTime(h.getTime()+ONE_HOUR),a=c?-1:1,h.setTime(h.getTime()+s*ONE_DAY*a),h},n}(DateConverter);AuditAction=function(n){return n={},n.Add="1",n.Edit="2",n.Delete="3",n}(AuditAction);AuditModule=function(n){return n={},n.Action="1",n.Menu="2",n.Node="3",n.SearchAssignedSecurity="4",n.SendingRule="5",n.Tab="6",n.User="7",n.SystemDelegation="8",n.Parameter="9",n}(AuditModule);